@echo off
REM Launch three Windows Terminal tabs for different use cases

setlocal

REM Get the directory of this script
set "ROOT=%~dp0"

wt new-tab -d "%ROOT%atomsec-func-sfdc" pwsh -NoExit -Command "azurite --location .azurite --silent --blobPort 10000 --queuePort 10001 --tablePort 10002" ^
; new-tab -d "%ROOT%atomsec-func-sfdc" pwsh -NoExit -Command "func start" ^
; new-tab -d "%ROOT%atomsec-app-frontend" pwsh -NoExit -Command "npm start"

endlocal