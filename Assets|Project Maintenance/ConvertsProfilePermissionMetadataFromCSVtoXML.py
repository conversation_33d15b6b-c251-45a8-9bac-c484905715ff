import csv
import xml.etree.ElementTree as ET
import os
from collections import defaultdict

FOLDER = os.path.dirname(os.path.abspath(__file__))
csv_path = os.path.join(FOLDER, 'ProfileAndPermissionMetadataSample.csv')
xml_path = os.path.join(FOLDER, 'Profiles_PermissionSetRisks-BestPractice_from_csv.xml')

root = ET.Element('BestPractices')

# Group rows by UserType
user_type_dict = defaultdict(list)

with open(csv_path, newline='', encoding='utf-8') as csvfile:
    reader = csv.DictReader(csvfile)
    for row in reader:
        user_type = row.get('UserType', '').strip()
        if not user_type and not row.get('SalesforceSetting'):
            continue
        user_type_dict[user_type].append(row)

for user_type, practices in user_type_dict.items():
    usertype_elem = ET.SubElement(root, 'UserType', name=user_type)
    for row in practices:
        practice = ET.SubElement(usertype_elem, 'Practice')
        ET.SubElement(practice, 'SalesforceSetting').text = row.get('SalesforceSetting', '').strip()
        ET.SubElement(practice, 'StandardValue').text = row.get('StandardValue', '').strip()
        ET.SubElement(practice, 'Description').text = row.get('Description', '').strip()
        ET.SubElement(practice, 'OWASP').text = row.get('OWASP', '').strip()
        ET.SubElement(practice, 'RiskTypeBasedOnSeverity').text = row.get('RiskTypeBasedOnSeverity', '').strip()


tree = ET.ElementTree(root)
tree.write(xml_path, encoding='utf-8', xml_declaration=True)
print(f"XML written to {xml_path}") 