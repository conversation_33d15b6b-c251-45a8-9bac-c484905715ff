UserType,SalesforceSetting,Description,OWASP,RiskTypeBasedOnSeverity,StandardValue
Salesforce,DataExport,Allows users to export data weekly.,A1: Broken Access Control,Low,FALSE
Salesforce,ManageUsers,"Allows users to create, edit, and deactivate users.",A1: Broken Access Control,High,FALSE
Salesforce,EditPublicFilters,Allows users to create and manage public list views.,A1: Broken Access Control,Medium,FALSE
Salesforce,EditPublicTemplates,Allows users to create and manage public classic email templates.,A1: Broken Access Control,Informational,FALSE
Salesforce,ModifyAllData,Grants access to all data regardless of sharing rules.,A1: Broken Access Control,Low,FALSE
Salesforce,ManageCases,"Allows users to create, edit, and delete cases.",A1: Broken Access Control,High,TRUE
Salesforce,ManageSolutions,Allows users to manage published solutions.,A1: Broken Access Control,Medium,FALSE
Salesforce,CustomizeApplication,Allows users to customize the application.,A5: Security Misconfiguration,Informational,FALSE
Salesforce,EditReadonlyFields,Allows users to edit fields marked as read-only.,A1: Broken Access Control,Low,FALSE
Salesforce,RunReports,Allows users to run reports.,A1: Broken Access Control,High,FALSE
Salesforce,ViewSetup,Allows users to view setup and configuration pages.,A5: Security Misconfiguration,Medium,FALSE
Salesforce,TransferAnyEntity,Allows users to transfer ownership of records.,A1: Broken Access Control,Informational,FALSE
Salesforce,ImportLeads,Allows users to import leads.,A1: Broken Access Control,Low,FALSE
Salesforce,ManageLeads,"Allows users to create, edit, and delete leads.",A1: Broken Access Control,High,FALSE
Salesforce,TransferAnyLead,Allows users to transfer ownership of leads.,A1: Broken Access Control,Medium,FALSE
Salesforce,ViewAllData,Grants read access to all data regardless of sharing rules.,A1: Broken Access Control,Informational,FALSE
Salesforce,EditPublicDocuments,Allows users to manage public documents.,A1: Broken Access Control,Low,FALSE
Salesforce,ViewEncryptedData,Allows users to view encrypted data.,A3: Sensitive Data Exposure,High,FALSE
Salesforce,EditHtmlTemplates,Allows users to edit HTML email templates.,A1: Broken Access Control,Medium,FALSE
Salesforce,ManageEncryptionKeys,Allows users to manage encryption keys.,A6: Security Misconfiguration,Informational,FALSE
Salesforce,DeleteActivatedContract,Allows users to delete activated contracts.,A1: Broken Access Control,Low,FALSE
Salesforce,ApiUserOnly,Restricts user access to API only.,A6: Security Misconfiguration,High,FALSE
Salesforce,ManageRemoteAccess,Allows users to manage connected apps.,A5: Security Misconfiguration,Medium,FALSE
Salesforce,PasswordNeverExpires,Prevents user passwords from expiring.,A7: Identification & Authentication Failures,Informational,FALSE
Salesforce,InstallMultiforce,Allows users to download AppExchange packages.,A5: Security Misconfiguration,Low,FALSE
Salesforce,PublishMultiforce,Allows users to upload AppExchange packages.,A5: Security Misconfiguration,High,FALSE
Salesforce,ManagePartners,Allows users to manage partner accounts and users.,A1: Broken Access Control,Medium,FALSE
Salesforce,ChatterOwnGroups,Allows users to create and own new Chatter groups.,A1: Broken Access Control,Informational,FALSE
Salesforce,CreateMultiforce,Allows users to create AppExchange packages.,A5: Security Misconfiguration,Low,FALSE
Salesforce,BulkApiHardDelete,Allows users to perform hard deletes using the Bulk API.,A1: Broken Access Control,High,FALSE
Salesforce,PortalSuperUser,Grants super user access in portals.,A1: Broken Access Control,Medium,FALSE
Salesforce,DelegatedPortalUserAdmin,Allows delegated administrators to manage portal users.,A1: Broken Access Control,Informational,FALSE
Salesforce,ViewContent,Allows users to view content in portals.,A1: Broken Access Control,Low,FALSE
Salesforce,ManageEmailClientConfig,Allows users to manage email client configurations.,A5: Security Misconfiguration,High,FALSE
Salesforce,ManageDataIntegrations,Allows users to manage data integrations.,A5: Security Misconfiguration,Medium,FALSE
Salesforce,ViewDataCategories,Allows users to view data categories in setup.,A5: Security Misconfiguration,Informational,FALSE
Salesforce,ManageDataCategories,Allows users to manage data categories.,A1: Broken Access Control,Low,FALSE
Salesforce,ManageMobile,Allows users to manage mobile configurations.,A5: Security Misconfiguration,High,FALSE
Salesforce,ApiEnabled,Allows users to access Salesforce via API.,A6: Security Misconfiguration,Medium,FALSE
Salesforce,ManageCustomReportTypes,Allows users to manage custom report types.,A5: Security Misconfiguration,Informational,FALSE
Salesforce,EditCaseComments,Allows users to edit case comments.,A1: Broken Access Control,Low,FALSE
Salesforce,TransferAnyCase,Allows users to transfer ownership of cases.,A1: Broken Access Control,High,FALSE
Salesforce,ContentAdministrator,Allows users to manage Salesforce CRM content.,A1: Broken Access Control,Medium,FALSE
Salesforce,CreateWorkspaces,Allows users to create content libraries.,A1: Broken Access Control,Informational,FALSE
Salesforce,ManageRoles,"Allows the user to create, edit, and delete roles in the role hierarchy.",A1: Broken Access Control,Low,FALSE
Salesforce,ManageSharing,"Allows the user to create, edit, and delete sharing rules for objects. This includes the ability to manage criteria-based and owner-based sharing rules.",A1: Broken Access Control,High,FALSE
Salesforce,ResetPasswords,Grants the ability to reset passwords and unlock user accounts,A5: Security Misconfiguration,Medium,FALSE
Salesforce,ManagePasswordPolicies,Grants the ability to configure password policies for all users in the org.,A5: Security Misconfiguration,Informational,FALSE
Salesforce,Packaging2Delete,Allows a user to delete second-generation packages (2GP) in Salesforce Dev Hub-enabled orgs.,A5: Security Misconfiguration,Low,FALSE
Salesforce,Packaging2,"Grants users the ability to create, update, and manage Second-Generation Packages (2GP) using Salesforce DX and Dev Hub.",A5: Security Misconfiguration,High,FALSE
Salesforce,DownloadPackageVersionZips,Allows users to download the zipped contents of a 2GP package version.,A5: Security Misconfiguration,Medium,FALSE
Salesforce,ManageFilesAndAttachments,"Grants the user ability to upload, edit, delete, and manage files and attachments in Salesforce. This includes files linked to records and stored in Content or Notes & Attachments.",A1: Broken Access Control,Informational,FALSE
Salesforce,QueryAllFiles,"Allows the user to query and access all files in the org, including those not explicitly shared with the user.",A1: Broken Access Control,Low,FALSE
Salesforce,ViewAllUsers,"Allows a user to view all user records in the Salesforce org, regardless of role hierarchy or sharing settings.",A1: Broken Access Control,High,FALSE
Salesforce,PrivacyDataAccess,"Grants access to view, modify, and delete data subject records, including Individual records (used for GDPR/CCPA compliance). This also allows access to Do Not Contact flags and personal data that might otherwise be masked.",A1: Broken Access Control,Medium,FALSE
Salesforce,ViewAllProfiles ,"Allows users to view all profiles and permission sets, including settings they may not otherwise be able to see (e.g., even if they can’t assign them).",A5: Security Misconfiguration,Informational,FALSE
Salesforce Integration,DataExport,Allows users to export data weekly.,A1: Broken Access Control,Medium,FALSE
Salesforce Integration,ManageUsers,"Allows users to create, edit, and deactivate users.",A1: Broken Access Control,Informational,FALSE
Salesforce Integration,EditPublicFilters,Allows users to create and manage public list views.,A1: Broken Access Control,Low,FALSE
Salesforce Integration,EditPublicTemplates,Allows users to create and manage public classic email templates.,A1: Broken Access Control,High,FALSE
Salesforce Integration,ModifyAllData,Grants access to all data regardless of sharing rules.,A1: Broken Access Control,Medium,FALSE
Salesforce Integration,ManageCases,"Allows users to create, edit, and delete cases.",A1: Broken Access Control,Informational,FALSE
Salesforce Integration,ManageSolutions,Allows users to manage published solutions.,A1: Broken Access Control,Low,FALSE
Salesforce Integration,CustomizeApplication,Allows users to customize the application.,A5: Security Misconfiguration,High,FALSE
Salesforce Integration,EditReadonlyFields,Allows users to edit fields marked as read-only.,A1: Broken Access Control,Medium,FALSE
Salesforce Integration,RunReports,Allows users to run reports.,A1: Broken Access Control,Informational,FALSE
Salesforce Integration,ViewSetup,Allows users to view setup and configuration pages.,A5: Security Misconfiguration,Low,FALSE
Salesforce Integration,TransferAnyEntity,Allows users to transfer ownership of records.,A1: Broken Access Control,High,FALSE
Salesforce Integration,ImportLeads,Allows users to import leads.,A1: Broken Access Control,Medium,FALSE
Salesforce Integration,ManageLeads,"Allows users to create, edit, and delete leads.",A1: Broken Access Control,Informational,FALSE
Salesforce Integration,TransferAnyLead,Allows users to transfer ownership of leads.,A1: Broken Access Control,Low,FALSE
Salesforce Integration,ViewAllData,Grants read access to all data regardless of sharing rules.,A1: Broken Access Control,High,FALSE
Salesforce Integration,EditPublicDocuments,Allows users to manage public documents.,A1: Broken Access Control,Medium,FALSE
Salesforce Integration,ViewEncryptedData,Allows users to view encrypted data.,A3: Sensitive Data Exposure,Informational,FALSE
Salesforce Integration,EditHtmlTemplates,Allows users to edit HTML email templates.,A1: Broken Access Control,Low,FALSE
Salesforce Integration,ManageEncryptionKeys,Allows users to manage encryption keys.,A6: Security Misconfiguration,High,FALSE
Salesforce Integration,DeleteActivatedContract,Allows users to delete activated contracts.,A1: Broken Access Control,Medium,FALSE
Salesforce Integration,ApiUserOnly,Restricts user access to API only.,A6: Security Misconfiguration,Informational,TRUE
Salesforce Integration,ManageRemoteAccess,Allows users to manage connected apps.,A5: Security Misconfiguration,Low,FALSE
Salesforce Integration,PasswordNeverExpires,Prevents user passwords from expiring.,A7: Identification & Authentication Failures,High,FALSE
Salesforce Integration,InstallMultiforce,Allows users to download AppExchange packages.,A5: Security Misconfiguration,Medium,FALSE
Salesforce Integration,PublishMultiforce,Allows users to upload AppExchange packages.,A5: Security Misconfiguration,Informational,FALSE
Salesforce Integration,ManagePartners,Allows users to manage partner accounts and users.,A1: Broken Access Control,Low,FALSE
Salesforce Integration,ChatterOwnGroups,Allows users to create and own new Chatter groups.,A1: Broken Access Control,High,FALSE
Salesforce Integration,CreateMultiforce,Allows users to create AppExchange packages.,A5: Security Misconfiguration,Medium,FALSE
Salesforce Integration,BulkApiHardDelete,Allows users to perform hard deletes using the Bulk API.,A1: Broken Access Control,Informational,FALSE
Salesforce Integration,PortalSuperUser,Grants super user access in portals.,A1: Broken Access Control,Low,FALSE
Salesforce Integration,DelegatedPortalUserAdmin,Allows delegated administrators to manage portal users.,A1: Broken Access Control,High,FALSE
Salesforce Integration,ViewContent,Allows users to view content in portals.,A1: Broken Access Control,Medium,FALSE
Salesforce Integration,ManageEmailClientConfig,Allows users to manage email client configurations.,A5: Security Misconfiguration,Informational,FALSE
Salesforce Integration,ManageDataIntegrations,Allows users to manage data integrations.,A5: Security Misconfiguration,Low,FALSE
Salesforce Integration,ViewDataCategories,Allows users to view data categories in setup.,A5: Security Misconfiguration,High,FALSE
Salesforce Integration,ManageDataCategories,Allows users to manage data categories.,A1: Broken Access Control,Medium,FALSE
Salesforce Integration,ManageMobile,Allows users to manage mobile configurations.,A5: Security Misconfiguration,Informational,FALSE
Salesforce Integration,ApiEnabled,Allows users to access Salesforce via API.,A6: Security Misconfiguration,Low,TRUE
Salesforce Integration,ManageCustomReportTypes,Allows users to manage custom report types.,A5: Security Misconfiguration,High,FALSE
Salesforce Integration,EditCaseComments,Allows users to edit case comments.,A1: Broken Access Control,Medium,FALSE
Salesforce Integration,TransferAnyCase,Allows users to transfer ownership of cases.,A1: Broken Access Control,Informational,FALSE
Salesforce Integration,ContentAdministrator,Allows users to manage Salesforce CRM content.,A1: Broken Access Control,Low,FALSE
Salesforce Integration,CreateWorkspaces,Allows users to create content libraries.,A1: Broken Access Control,High,FALSE
Salesforce Integration,ManageRoles,"Allows the user to create, edit, and delete roles in the role hierarchy.",A1: Broken Access Control,Medium,FALSE
Salesforce Integration,ManageSharing,"Allows the user to create, edit, and delete sharing rules for objects. This includes the ability to manage criteria-based and owner-based sharing rules.",A1: Broken Access Control,Informational,FALSE
Salesforce Integration,ResetPasswords,Grants the ability to reset passwords and unlock user accounts,A5: Security Misconfiguration,Low,FALSE
Salesforce Integration,ManagePasswordPolicies,Grants the ability to configure password policies for all users in the org.,A5: Security Misconfiguration,High,FALSE
Salesforce Integration,Packaging2Delete,Allows a user to delete second-generation packages (2GP) in Salesforce Dev Hub-enabled orgs.,A5: Security Misconfiguration,Medium,FALSE
Salesforce Integration,Packaging2,"Grants users the ability to create, update, and manage Second-Generation Packages (2GP) using Salesforce DX and Dev Hub.",A5: Security Misconfiguration,Informational,FALSE
Salesforce Integration,DownloadPackageVersionZips,Allows users to download the zipped contents of a 2GP package version.,A5: Security Misconfiguration,Low,FALSE
Salesforce Integration,ManageFilesAndAttachments,"Grants the user ability to upload, edit, delete, and manage files and attachments in Salesforce. This includes files linked to records and stored in Content or Notes & Attachments.",A1: Broken Access Control,High,FALSE
Salesforce Integration,QueryAllFiles,"Allows the user to query and access all files in the org, including those not explicitly shared with the user.",A1: Broken Access Control,Medium,FALSE
Salesforce Integration,ViewAllUsers,"Allows a user to view all user records in the Salesforce org, regardless of role hierarchy or sharing settings.",A1: Broken Access Control,Informational,FALSE
Salesforce Integration,PrivacyDataAccess,"Grants access to view, modify, and delete data subject records, including Individual records (used for GDPR/CCPA compliance). This also allows access to Do Not Contact flags and personal data that might otherwise be masked.",A1: Broken Access Control,Low,FALSE
Salesforce Integration,ViewAllProfiles ,"Allows users to view all profiles and permission sets, including settings they may not otherwise be able to see (e.g., even if they can’t assign them).",A5: Security Misconfiguration,High,FALSE
Guest User License,DataExport,Allows users to export data weekly.,A1: Broken Access Control,High,FALSE
Guest User License,ManageUsers,"Allows users to create, edit, and deactivate users.",A1: Broken Access Control,Medium,FALSE
Guest User License,EditPublicFilters,Allows users to create and manage public list views.,A1: Broken Access Control,Informational,FALSE
Guest User License,EditPublicTemplates,Allows users to create and manage public classic email templates.,A1: Broken Access Control,Low,FALSE
Guest User License,ModifyAllData,Grants access to all data regardless of sharing rules.,A1: Broken Access Control,High,FALSE
Guest User License,ManageCases,"Allows users to create, edit, and delete cases.",A1: Broken Access Control,Medium,FALSE
Guest User License,ManageSolutions,Allows users to manage published solutions.,A1: Broken Access Control,Informational,FALSE
Guest User License,CustomizeApplication,Allows users to customize the application.,A5: Security Misconfiguration,Low,FALSE
Guest User License,EditReadonlyFields,Allows users to edit fields marked as read-only.,A1: Broken Access Control,High,FALSE
Guest User License,RunReports,Allows users to run reports.,A1: Broken Access Control,Medium,FALSE
Guest User License,ViewSetup,Allows users to view setup and configuration pages.,A5: Security Misconfiguration,Informational,FALSE
Guest User License,TransferAnyEntity,Allows users to transfer ownership of records.,A1: Broken Access Control,Low,FALSE
Guest User License,ImportLeads,Allows users to import leads.,A1: Broken Access Control,High,FALSE
Guest User License,ManageLeads,"Allows users to create, edit, and delete leads.",A1: Broken Access Control,Medium,FALSE
Guest User License,TransferAnyLead,Allows users to transfer ownership of leads.,A1: Broken Access Control,Informational,FALSE
Guest User License,ViewAllData,Grants read access to all data regardless of sharing rules.,A1: Broken Access Control,Low,FALSE
Guest User License,EditPublicDocuments,Allows users to manage public documents.,A1: Broken Access Control,High,FALSE
Guest User License,ViewEncryptedData,Allows users to view encrypted data.,A3: Sensitive Data Exposure,Medium,FALSE
Guest User License,EditHtmlTemplates,Allows users to edit HTML email templates.,A1: Broken Access Control,Informational,FALSE
Guest User License,ManageEncryptionKeys,Allows users to manage encryption keys.,A6: Security Misconfiguration,Low,FALSE
Guest User License,DeleteActivatedContract,Allows users to delete activated contracts.,A1: Broken Access Control,High,FALSE
Guest User License,ApiUserOnly,Restricts user access to API only.,A6: Security Misconfiguration,Medium,FALSE
Guest User License,ManageRemoteAccess,Allows users to manage connected apps.,A5: Security Misconfiguration,Informational,FALSE
Guest User License,PasswordNeverExpires,Prevents user passwords from expiring.,A7: Identification & Authentication Failures,Low,FALSE
Guest User License,InstallMultiforce,Allows users to download AppExchange packages.,A5: Security Misconfiguration,High,FALSE
Guest User License,PublishMultiforce,Allows users to upload AppExchange packages.,A5: Security Misconfiguration,Medium,FALSE
Guest User License,ManagePartners,Allows users to manage partner accounts and users.,A1: Broken Access Control,Informational,FALSE
Guest User License,ChatterOwnGroups,Allows users to create and own new Chatter groups.,A1: Broken Access Control,Low,FALSE
Guest User License,CreateMultiforce,Allows users to create AppExchange packages.,A5: Security Misconfiguration,High,FALSE
Guest User License,BulkApiHardDelete,Allows users to perform hard deletes using the Bulk API.,A1: Broken Access Control,Medium,FALSE
Guest User License,PortalSuperUser,Grants super user access in portals.,A1: Broken Access Control,Informational,FALSE
Guest User License,DelegatedPortalUserAdmin,Allows delegated administrators to manage portal users.,A1: Broken Access Control,Low,FALSE
Guest User License,ViewContent,Allows users to view content in portals.,A1: Broken Access Control,High,FALSE
Guest User License,ManageEmailClientConfig,Allows users to manage email client configurations.,A5: Security Misconfiguration,Medium,FALSE
Guest User License,ManageDataIntegrations,Allows users to manage data integrations.,A5: Security Misconfiguration,Informational,FALSE
Guest User License,ViewDataCategories,Allows users to view data categories in setup.,A5: Security Misconfiguration,Low,FALSE
Guest User License,ManageDataCategories,Allows users to manage data categories.,A1: Broken Access Control,High,FALSE
Guest User License,ManageMobile,Allows users to manage mobile configurations.,A5: Security Misconfiguration,Medium,FALSE
Guest User License,ApiEnabled,Allows users to access Salesforce via API.,A6: Security Misconfiguration,Informational,FALSE
Guest User License,ManageCustomReportTypes,Allows users to manage custom report types.,A5: Security Misconfiguration,Low,FALSE
Guest User License,EditCaseComments,Allows users to edit case comments.,A1: Broken Access Control,High,FALSE
Guest User License,TransferAnyCase,Allows users to transfer ownership of cases.,A1: Broken Access Control,Medium,FALSE
Guest User License,ContentAdministrator,Allows users to manage Salesforce CRM content.,A1: Broken Access Control,Informational,FALSE
Guest User License,CreateWorkspaces,Allows users to create content libraries.,A1: Broken Access Control,Low,FALSE
Guest User License,ManageRoles,"Allows the user to create, edit, and delete roles in the role hierarchy.",A1: Broken Access Control,High,FALSE
Guest User License,ManageSharing,"Allows the user to create, edit, and delete sharing rules for objects. This includes the ability to manage criteria-based and owner-based sharing rules.",A1: Broken Access Control,Medium,FALSE
Guest User License,ResetPasswords,Grants the ability to reset passwords and unlock user accounts,A5: Security Misconfiguration,Informational,FALSE
Guest User License,ManagePasswordPolicies,Grants the ability to configure password policies for all users in the org.,A5: Security Misconfiguration,Low,FALSE
Guest User License,Packaging2Delete,Allows a user to delete second-generation packages (2GP) in Salesforce Dev Hub-enabled orgs.,A5: Security Misconfiguration,High,FALSE
Guest User License,Packaging2,"Grants users the ability to create, update, and manage Second-Generation Packages (2GP) using Salesforce DX and Dev Hub.",A5: Security Misconfiguration,Medium,FALSE
Guest User License,DownloadPackageVersionZips,Allows users to download the zipped contents of a 2GP package version.,A5: Security Misconfiguration,Informational,FALSE
Guest User License,ManageFilesAndAttachments,"Grants the user ability to upload, edit, delete, and manage files and attachments in Salesforce. This includes files linked to records and stored in Content or Notes & Attachments.",A1: Broken Access Control,Low,FALSE
Guest User License,QueryAllFiles,"Allows the user to query and access all files in the org, including those not explicitly shared with the user.",A1: Broken Access Control,High,FALSE
Guest User License,ViewAllUsers,"Allows a user to view all user records in the Salesforce org, regardless of role hierarchy or sharing settings.",A1: Broken Access Control,Medium,FALSE
Guest User License,PrivacyDataAccess,"Grants access to view, modify, and delete data subject records, including Individual records (used for GDPR/CCPA compliance). This also allows access to Do Not Contact flags and personal data that might otherwise be masked.",A1: Broken Access Control,Informational,FALSE
Guest User License,ViewAllProfiles ,"Allows users to view all profiles and permission sets, including settings they may not otherwise be able to see (e.g., even if they can’t assign them).",A5: Security Misconfiguration,Low,FALSE
Customer Community Login,DataExport,Allows users to export data weekly.,A1: Broken Access Control,Low,FALSE
Customer Community Login,ManageUsers,"Allows users to create, edit, and deactivate users.",A1: Broken Access Control,High,FALSE
Customer Community Login,EditPublicFilters,Allows users to create and manage public list views.,A1: Broken Access Control,Medium,FALSE
Customer Community Login,EditPublicTemplates,Allows users to create and manage public classic email templates.,A1: Broken Access Control,Informational,FALSE
Customer Community Login,ModifyAllData,Grants access to all data regardless of sharing rules.,A1: Broken Access Control,Low,FALSE
Customer Community Login,ManageCases,"Allows users to create, edit, and delete cases.",A1: Broken Access Control,High,FALSE
Customer Community Login,ManageSolutions,Allows users to manage published solutions.,A1: Broken Access Control,Medium,FALSE
Customer Community Login,CustomizeApplication,Allows users to customize the application.,A5: Security Misconfiguration,Informational,FALSE
Customer Community Login,EditReadonlyFields,Allows users to edit fields marked as read-only.,A1: Broken Access Control,Low,FALSE
Customer Community Login,RunReports,Allows users to run reports.,A1: Broken Access Control,High,FALSE
Customer Community Login,ViewSetup,Allows users to view setup and configuration pages.,A5: Security Misconfiguration,Medium,FALSE
Customer Community Login,TransferAnyEntity,Allows users to transfer ownership of records.,A1: Broken Access Control,Informational,FALSE
Customer Community Login,ImportLeads,Allows users to import leads.,A1: Broken Access Control,Low,FALSE
Customer Community Login,ManageLeads,"Allows users to create, edit, and delete leads.",A1: Broken Access Control,High,FALSE
Customer Community Login,TransferAnyLead,Allows users to transfer ownership of leads.,A1: Broken Access Control,Medium,FALSE
Customer Community Login,ViewAllData,Grants read access to all data regardless of sharing rules.,A1: Broken Access Control,Informational,FALSE
Customer Community Login,EditPublicDocuments,Allows users to manage public documents.,A1: Broken Access Control,Low,FALSE
Customer Community Login,ViewEncryptedData,Allows users to view encrypted data.,A3: Sensitive Data Exposure,High,FALSE
Customer Community Login,EditHtmlTemplates,Allows users to edit HTML email templates.,A1: Broken Access Control,Medium,FALSE
Customer Community Login,ManageEncryptionKeys,Allows users to manage encryption keys.,A6: Security Misconfiguration,Informational,FALSE
Customer Community Login,DeleteActivatedContract,Allows users to delete activated contracts.,A1: Broken Access Control,Low,FALSE
Customer Community Login,ApiUserOnly,Restricts user access to API only.,A6: Security Misconfiguration,High,FALSE
Customer Community Login,ManageRemoteAccess,Allows users to manage connected apps.,A5: Security Misconfiguration,Medium,FALSE
Customer Community Login,PasswordNeverExpires,Prevents user passwords from expiring.,A7: Identification & Authentication Failures,Informational,FALSE
Customer Community Login,InstallMultiforce,Allows users to download AppExchange packages.,A5: Security Misconfiguration,Low,FALSE
Customer Community Login,PublishMultiforce,Allows users to upload AppExchange packages.,A5: Security Misconfiguration,High,FALSE
Customer Community Login,ManagePartners,Allows users to manage partner accounts and users.,A1: Broken Access Control,Medium,FALSE
Customer Community Login,ChatterOwnGroups,Allows users to create and own new Chatter groups.,A1: Broken Access Control,Informational,FALSE
Customer Community Login,CreateMultiforce,Allows users to create AppExchange packages.,A5: Security Misconfiguration,Low,FALSE
Customer Community Login,BulkApiHardDelete,Allows users to perform hard deletes using the Bulk API.,A1: Broken Access Control,High,FALSE
Customer Community Login,PortalSuperUser,Grants super user access in portals.,A1: Broken Access Control,Medium,FALSE
Customer Community Login,DelegatedPortalUserAdmin,Allows delegated administrators to manage portal users.,A1: Broken Access Control,Informational,FALSE
Customer Community Login,ViewContent,Allows users to view content in portals.,A1: Broken Access Control,Low,FALSE
Customer Community Login,ManageEmailClientConfig,Allows users to manage email client configurations.,A5: Security Misconfiguration,High,FALSE
Customer Community Login,ManageDataIntegrations,Allows users to manage data integrations.,A5: Security Misconfiguration,Medium,FALSE
Customer Community Login,ViewDataCategories,Allows users to view data categories in setup.,A5: Security Misconfiguration,Informational,FALSE
Customer Community Login,ManageDataCategories,Allows users to manage data categories.,A1: Broken Access Control,Low,FALSE
Customer Community Login,ManageMobile,Allows users to manage mobile configurations.,A5: Security Misconfiguration,High,FALSE
Customer Community Login,ApiEnabled,Allows users to access Salesforce via API.,A6: Security Misconfiguration,Medium,FALSE
Customer Community Login,ManageCustomReportTypes,Allows users to manage custom report types.,A5: Security Misconfiguration,Informational,FALSE
Customer Community Login,EditCaseComments,Allows users to edit case comments.,A1: Broken Access Control,Low,FALSE
Customer Community Login,TransferAnyCase,Allows users to transfer ownership of cases.,A1: Broken Access Control,High,FALSE
Customer Community Login,ContentAdministrator,Allows users to manage Salesforce CRM content.,A1: Broken Access Control,Medium,FALSE
Customer Community Login,CreateWorkspaces,Allows users to create content libraries.,A1: Broken Access Control,Informational,FALSE
Customer Community Login,ManageRoles,"Allows the user to create, edit, and delete roles in the role hierarchy.",A1: Broken Access Control,Low,FALSE
Customer Community Login,ManageSharing,"Allows the user to create, edit, and delete sharing rules for objects. This includes the ability to manage criteria-based and owner-based sharing rules.",A1: Broken Access Control,High,FALSE
Customer Community Login,ResetPasswords,Grants the ability to reset passwords and unlock user accounts,A5: Security Misconfiguration,Medium,FALSE
Customer Community Login,ManagePasswordPolicies,Grants the ability to configure password policies for all users in the org.,A5: Security Misconfiguration,Informational,FALSE
Customer Community Login,Packaging2Delete,Allows a user to delete second-generation packages (2GP) in Salesforce Dev Hub-enabled orgs.,A5: Security Misconfiguration,Low,FALSE
Customer Community Login,Packaging2,"Grants users the ability to create, update, and manage Second-Generation Packages (2GP) using Salesforce DX and Dev Hub.",A5: Security Misconfiguration,High,FALSE
Customer Community Login,DownloadPackageVersionZips,Allows users to download the zipped contents of a 2GP package version.,A5: Security Misconfiguration,Medium,FALSE
Customer Community Login,ManageFilesAndAttachments,"Grants the user ability to upload, edit, delete, and manage files and attachments in Salesforce. This includes files linked to records and stored in Content or Notes & Attachments.",A1: Broken Access Control,Informational,FALSE
Customer Community Login,QueryAllFiles,"Allows the user to query and access all files in the org, including those not explicitly shared with the user.",A1: Broken Access Control,Low,FALSE
Customer Community Login,ViewAllUsers,"Allows a user to view all user records in the Salesforce org, regardless of role hierarchy or sharing settings.",A1: Broken Access Control,High,FALSE
Customer Community Login,PrivacyDataAccess,"Grants access to view, modify, and delete data subject records, including Individual records (used for GDPR/CCPA compliance). This also allows access to Do Not Contact flags and personal data that might otherwise be masked.",A1: Broken Access Control,Medium,FALSE
Customer Community Login,ViewAllProfiles ,"Allows users to view all profiles and permission sets, including settings they may not otherwise be able to see (e.g., even if they can’t assign them).",A5: Security Misconfiguration,Informational,FALSE
Blank,ManageFilesAndAttachments,"Grants the user ability to upload, edit, delete, and manage files and attachments in Salesforce. This includes files linked to records and stored in Content or Notes & Attachments.",A1: Broken Access Control,Informational,FALSE
Blank,QueryAllFiles,"Allows the user to query and access all files in the org, including those not explicitly shared with the user.",A1: Broken Access Control,Low,FALSE
Blank,ViewAllUsers,"Allows a user to view all user records in the Salesforce org, regardless of role hierarchy or sharing settings.",A1: Broken Access Control,High,FALSE
Blank,PrivacyDataAccess,"Grants access to view, modify, and delete data subject records, including Individual records (used for GDPR/CCPA compliance). This also allows access to Do Not Contact flags and personal data that might otherwise be masked.",A1: Broken Access Control,Medium,FALSE
Blank,ViewAllProfiles ,"Allows users to view all profiles and permission sets, including settings they may not otherwise be able to see (e.g., even if they can’t assign them).",A5: Security Misconfiguration,Informational,FALSE