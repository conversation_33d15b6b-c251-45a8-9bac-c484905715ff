# Starter pipeline
# Start with a minimal pipeline that you can customize to build and deploy your code.
# Add steps that build, run tests, deploy, and more:
# https://aka.ms/yaml

trigger:
- dev

pool:
  vmImage: ubuntu-latest

steps:


- task: UsePythonVersion@0
  inputs:
    versionSpec: '3.12'
  displayName: 'Set up Python 3.12'

- script: |
    python --version
    python -m pip install --upgrade pip

    echo 'Installing ODBC driver for SQL Server on the build agent...'
    # Ensure curl is present, apt-get update will run after adding the repo
    sudo apt-get install -y curl

    # Add Microsoft GPG key
    curl -fsSL https://packages.microsoft.com/keys/microsoft.asc | sudo apt-key add -
    
    # Add Microsoft repository for Ubuntu
    # $(lsb_release -rs) will get the version of Ubuntu on the agent (e.g., 20.04, 22.04)
    sudo curl -fsSL https://packages.microsoft.com/config/ubuntu/$(lsb_release -rs)/prod.list -o /etc/apt/sources.list.d/mssql-release.list
    
    # Update package list again and install driver & dev package
    sudo apt-get update
    sudo ACCEPT_EULA=Y apt-get install -y msodbcsql17
    sudo ACCEPT_EULA=Y apt-get install -y unixodbc-dev

    echo 'ODBC driver installation attempt on build agent complete.'

    echo 'Installing Python dependencies ...'
    head requirements.txt
    pip install -r requirements.txt -t .
  displayName: 'Install ODBC driver and Python dependencies'

- script: |
    echo 'Installing Java for PMD...'
    sudo apt-get update
    sudo apt-get install -y openjdk-17-jdk
    java -version
  displayName: 'Install Java for PMD'

- script: |
    echo 'Installing PMD...'
    # Create directory for PMD
    mkdir -p /opt/pmd
    
    # Download PMD
    PMD_VERSION="7.14.0"
    wget https://github.com/pmd/pmd/releases/download/pmd_releases%2F${PMD_VERSION}/pmd-bin-${PMD_VERSION}.zip
    
    # Extract PMD
    unzip pmd-bin-${PMD_VERSION}.zip
    
    # Move PMD to the appropriate location
    sudo mv pmd-bin-${PMD_VERSION}/* /opt/pmd/
    
    # Create symbolic link to make PMD available in path
    sudo ln -s /opt/pmd/bin/run.sh /usr/local/bin/pmd
    
    # Test PMD installation with the correct syntax
    pmd --version
    pmd check --help
    
    # Verify PMD installation
    pmd --version
    
    # Include PMD in the deployment package
    mkdir -p $(System.DefaultWorkingDirectory)/opt/pmd
    cp -r /opt/pmd/* $(System.DefaultWorkingDirectory)/opt/pmd/
    
    echo 'PMD installation complete'
  displayName: 'Install PMD for Salesforce code quality analysis'

- task: ArchiveFiles@2
  inputs:
    rootFolderOrFile: '$(System.DefaultWorkingDirectory)'
    includeRootFolder: false
    archiveType: 'zip'
    archiveFile: '$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).zip'
    replaceExistingArchive: true

- task: AzureFunctionApp@2
  inputs:
    connectedServiceNameARM: 'sc-atomsec-dev-backend'
    appType: 'functionAppLinux'
    appName: 'func-atomsec-sfdc-dev'
    package: '$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).zip'
    runtimeStack: 'PYTHON|3.12'
    deploymentMethod: 'zipDeploy'
    deployToSlotOrASE: true
    resourceGroupName: 'atomsec-dev-backend'
    slotName: 'stage'

- script: |
    pip install pytest pytest-azurepipelines 
    echo 'Set base_url for test--'
    export base_url="https://func-atomsec-sfdc-dev-stage.azurewebsites.net/"
    pytest tests/ 
  displayName: 'Run Tests via. pytest'

#- script: |
#    pip install pytest coverage
#    coverage run -m pytest tests/
#    coverage xml -o cobertura-coverage.xml
#  displayName: Code coverage check
#  continueOnError: true
#  workingDirectory: '$(System.DefaultWorkingDirectory)'

#- task: PublishCodeCoverageResults@2
#  inputs:
#    summaryFileLocation: 'cobertura-coverage.xml'

- task: AzureAppServiceManage@0
  inputs:
    azureSubscription: 'sc-atomsec-dev-backend'
    Action: 'Swap Slots'
    WebAppName: 'func-atomsec-sfdc-dev'
    ResourceGroupName: 'atomsec-dev-backend'
    SourceSlot: 'stage'

- task: PublishBuildArtifacts@1
  inputs:
    PathtoPublish: '$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).zip'
    ArtifactName: 'drop'
    publishLocation: 'Container'