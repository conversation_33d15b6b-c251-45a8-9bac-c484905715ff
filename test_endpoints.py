"""
Test script for Azure Functions endpoints

This script tests various endpoints to help diagnose issues with Azure Functions.
"""

import requests
import json
import sys
import time

def test_endpoint(base_url, endpoint, method="GET", data=None, headers=None):
    """Test an endpoint and return the result"""
    url = f"{base_url}/{endpoint}"

    print(f"Testing {method} {url}...")

    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers, timeout=10)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, headers=headers, timeout=10)
        else:
            return f"ERROR: Unsupported method {method}"

        # Print response details
        print(f"Status code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")

        # Try to parse JSON response
        try:
            json_response = response.json()
            print(f"Response: {json.dumps(json_response, indent=2)}")
        except:
            print(f"Response: {response.text[:500]}")

        return f"{'SUCCESS' if response.status_code < 400 else 'FAILURE'}: {method} {url} - {response.status_code}"

    except requests.RequestException as e:
        return f"ERROR: {method} {url} - {str(e)}"

def main():
    """Main function"""
    # Get the base URL
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:7071/api"

    print(f"Testing endpoints at: {base_url}")
    print()

    # Define endpoints to test
    endpoints = [
        # Direct endpoints
        "direct-accounts",
        "direct-roles",
        "accounts-test",
        "accounts-api",
        "roles-api",

        # ASGI endpoints
        "api/accounts",
        "api/roles",
        "accounts",
        "roles"
    ]

    # Test each endpoint
    results = []
    for endpoint in endpoints:
        result = test_endpoint(base_url, endpoint)
        results.append(result)
        print()
        time.sleep(1)  # Add a small delay between requests

    # Print summary
    print("Test results summary:")
    for result in results:
        print(result)

if __name__ == "__main__":
    main()
