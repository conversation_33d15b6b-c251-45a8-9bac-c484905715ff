# AtomSec SFDC API

This project contains the API for the AtomSec application, built with FastAPI and deployed as an Azure Function. It supports local development using Azurite for storage emulation.

## FastAPI with Azure Functions ASGI Integration

This project uses FastAPI with Azure Functions ASGI integration to combine the best of both worlds:

1. **FastAPI** for enhanced API features like automatic documentation, request validation, and dependency injection
2. **Azure Functions** for serverless scalability and integration with Azure services

FastAPI offers several advantages for API development:

1. **Automatic Documentation**: Interactive API documentation with Swagger UI and ReDoc
2. **Request Validation**: Automatic validation of request data using Pydantic models
3. **Dependency Injection**: Clean way to handle dependencies and share code
4. **Better Error Handling**: More structured error handling with HTTP exceptions
5. **Type Hints**: Better IDE support and code completion

For detailed information about the ASGI integration, see the [ASGI Integration Guide](ASGI_INTEGRATION.md).

## Project Setup

### Prerequisites
- Python 3.8-3.11
- pip
- virtualenv (recommended)
- Azure Functions Core Tools v4
- Azurite (for local storage emulation)

### Local Development Setup

1. Clone the repository
```bash
git clone <repository-url>
cd atomsec-func-sfdc
```

## Architecture

The project follows a modular, blueprint-based architecture that aligns with Azure best practices:

### Implementation Approach

This project uses a hybrid approach that combines:

1. **Azure Functions with Blueprints**: For most functionality, we use Azure Functions with blueprints for better organization.
2. **FastAPI with ASGI Integration**: For more complex API scenarios, we use FastAPI with Azure Functions ASGI integration.

#### When to Use Each Approach:

- **Azure Functions with Blueprints**: Use for simple endpoints with straightforward request/response patterns.
- **FastAPI with ASGI Integration**: Use for complex endpoints that require advanced features like:
  - Request validation with Pydantic models
  - Dependency injection
  - Automatic documentation
  - Complex routing

#### API Routing Conventions:

To ensure consistent API routing, we follow these conventions:

1. **Frontend Expectations**: The frontend expects endpoints with a single `/api/` prefix (e.g., `/api/accounts`).
2. **Backend Implementation**:
   - For Azure Functions, we define routes with a single `/api/` prefix in function_app.py.
   - For FastAPI, we define routes with a single `/api/` prefix for all endpoints.
3. **Proxy Functions**: We use proxy functions in function_app.py to route requests to the appropriate handlers.

### Directory Structure

```
atomsec-func-sfdc/
│
├── function_app.py              # Main entry point with function app definition
├── blueprints/                  # Organized function modules
│   ├── __init__.py              # Makes the directory a package
│   ├── profile_metadata.py      # Profile metadata functions
│   ├── security_health_check.py # Security health check functions
│   ├── auth.py                  # Authentication functions
│   └── general.py               # General utility functions
│
├── shared/                      # Shared code and utilities
│   ├── __init__.py              # Makes the directory a package
│   ├── azure_services.py        # Azure service connections
│   ├── data_access.py           # Data access repositories
│   ├── database_models.py       # Database model definitions
│   ├── user_repository.py       # User data access functions
│   └── utils.py                 # Utility functions
│
├── tests/                       # Test cases
│   ├── __init__.py
│   ├── test_profile_metadata.py
│   ├── test_security_health_check.py
│   └── test_user_repository.py
│
├── scripts/                     # Utility scripts
│   └── init_database.py         # Database initialization script
│
├── host.json                    # Host configuration
├── local.settings.json          # Local settings (not checked into source control)
├── requirements.txt             # Project dependencies
└── README.md                    # Project documentation
```

### Key Design Principles

1. **Blueprint-Based Organization**: Functions are organized into logical blueprints for better modularity and maintainability
2. **Centralized Configuration**: All Azure service connections are managed through the `shared/azure_services.py` module
3. **Repository Pattern**: Data access is abstracted through repository classes in `shared/data_access.py`
4. **Environment Awareness**: The code automatically detects if it's running locally or in Azure and configures itself accordingly
5. **Async Support**: Asynchronous functions for better performance and scalability
6. **Proper Error Handling**: Consistent error handling and logging throughout the application
7. **Database Models**: Structured database models for consistent data representation

## Salesforce Profile Best-Practices Comparison Process

This project includes robust logic to compare Salesforce profile permissions against best-practices, with detailed normalization and matching to ensure accurate results. Below is a step-by-step overview of how the process works:

### 1. Profile XML Extraction
- Profile XMLs are downloaded from Azure Blob Storage.
- Each profile XML is parsed to extract:
  - `<userLicense>` value (used to determine the user type for best-practice matching)
  - All `<userPermissions>` entries (each with a `name` and `enabled` value)

### 2. Best-Practices Loading
- Best-practices are loaded from an XML file, where each entry specifies:
  - `UserType` (the Salesforce user license or type)
  - `SalesforceSetting` (the permission name)
  - `StandardValue` (the recommended value, e.g., TRUE/FALSE)
  - Additional metadata (Description, OWASP, RiskTypeBasedOnSeverity, etc.)

### 3. Normalization Logic
- **Permission Names:**
  - All permission names are normalized by stripping whitespace, converting to lowercase, and removing spaces/underscores.
- **Values (StandardValue & ProfileValue):**
  - Both values are normalized by stripping whitespace and converting to lowercase.
  - Common synonyms are mapped:
    - Any of: `❌ false`, `x false`, `false`, `no`, `disabled`, `0` → `false`
    - Any of: `✅ true`, `tickmark/true`, `true`, `yes`, `enabled`, `1` → `true`
- **UserType:**
  - Both the profile's user license and the best-practice UserType are normalized (lowercase, no spaces).

### 4. Matching Logic
- For each profile:
  - For each best-practice entry:
    - Match if the normalized `UserType` (from best-practice) matches the normalized `userLicense` (from profile).
    - Match if the normalized `SalesforceSetting` matches a normalized permission name in the profile.
    - Compare the normalized `StandardValue` (best-practice) to the normalized `ProfileValue` (from profile XML).
    - The result is recorded as a JSON object with fields:
      - `SalesforceSetting`, `StandardValue`, `ProfileValue`, `Match` (True/False), `MissingInProfile` (True if not present), and metadata.

### 5. Edge Case Handling
- If `<userLicense>` is missing, blank, or null, it is normalized and matched to best-practices entries with `UserType` of `null`, `blank`, or a single space.
- All comparisons are case-insensitive and robust to whitespace or formatting differences.

### 6. Results Storage
- The results for each profile are stored as a JSON array in the `OrgValue` column of the `PoliciesResult` table.
- Each result includes all relevant comparison details and metadata for reporting and analysis.

### 7. Debugging & Logging
- Extensive debug logging is included to trace extracted values, normalization, and match decisions.
- Errors in XML parsing or value extraction are logged, and processing continues for other profiles.

---

**This process ensures that profile-to-best-practice comparisons are accurate, repeatable, and easy to maintain or extend. For any questions or to update the logic, refer to the `normalize_value`, `normalize_permission_name`, and profile comparison functions in the codebase.**

## Available Endpoints

- `/api/home`: Home page with links to all available endpoints
- `/api/health-score`: View Salesforce security health score data
- `/api/health-risks`: View Salesforce security health risks data
- `/api/integration/{tenant_url}/profiles`: View Salesforce profiles for a specific integration
- `/api/integration/{tenant_url}/permission-sets`: View Salesforce permission sets for a specific integration
- `/api/auth/signup`: User registration endpoint
- `/api/auth/login`: User login endpoint
- `/api/auth/token/refresh`: Token refresh endpoint
- `/api/health`: Health check endpoint
- `/api/info`: System information endpoint

## Local Development Setup

For detailed instructions on setting up and running the application locally, see the [Local Development Guide](references/local_development_guide.md).

### Quick Start

1. Clone the repository and navigate to the project directory

2. Create and activate a Python virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows use `venv\Scripts\activate`
```

3. Install dependencies
```bash
pip install -e .[dev]
pip install -r requirements.txt
```

### Running Tests

To run tests locally:
```bash
python -m pytest tests/ -v
```

### Deployment Configuration

The project uses Azure DevOps for CI/CD with the following key configurations:
- Python 3.11 runtime
- Editable package installation
- Comprehensive test reporting
- Slot deployment strategy

### Troubleshooting Common Issues

#### Import Errors
- Ensure you're using the editable package installation (`pip install -e .`)
- Check that `PYTHONPATH` includes the project root directory


```bash
cp config.py.template config.py
# Edit config.py with your Salesforce credentials
```

5. Initialize the database:

```bash
python scripts/init_database.py
```

6. Run the Azure Functions app with FastAPI integration:

```bash
# In one terminal
azurite --location .azurite --silent --blobPort 10100 --queuePort 10101 --tablePort 10102

# In another terminal
func start
```

Alternatively, in VS Code, use the "Attach to Python Functions" launch configuration.

7. Test the application:
   - FastAPI Documentation: `http://localhost:7071/api/docs`
   - Health Check: `http://localhost:7071/api/health`
   - Debug Health Check: `http://localhost:7071/api/debug-health`

## Deployment

### Azure DevOps Pipeline

The project includes an Azure DevOps pipeline configuration in `pipeline-func-sfdc-dev.yml` that handles:

1. Building the application
2. Running tests
3. Deploying to a staging slot
4. Swapping to production after successful deployment

### Manual Deployment

You can also deploy manually using the Azure Functions Core Tools:

```bash
func azure functionapp publish <APP_NAME>
```

### Deployment Verification Tests

The project includes tests to verify that functions are available after deployment. These tests check:

1. General function availability by making HTTP requests to key endpoints
2. Proxy function availability
3. Blueprint function availability

To run these tests manually after deployment, use the provided verification script:

```bash
# Run with default URL (https://func-atomsec-sfdc-dev.azurewebsites.net)
python scripts/verify_deployment.py

# Or specify a custom URL
python scripts/verify_deployment.py --url https://your-function-app-url.azurewebsites.net
```

The verification script will generate a JUnit XML report file (`deployment-verification-results.xml`) that can be used for test result reporting in CI/CD systems.

Alternatively, you can run the tests directly:

```bash
# Set environment variables
export RUN_DEPLOYMENT_TESTS=true
export FUNCTION_APP_URL=https://func-atomsec-sfdc-dev.azurewebsites.net

# Run the tests
python -m pytest tests/test_deployment_verification.py -v
```

These tests are also automatically run as part of the Azure DevOps pipeline after deployment.

## Troubleshooting

For detailed troubleshooting information, see the [Troubleshooting Guide](references/troubleshooting_guide.md).

### Function Discovery and function.json Files

This project uses the blueprint pattern for organizing Azure Functions, which provides a cleaner and more maintainable code structure. However, the Azure Portal and some deployment tools rely on function.json files to discover functions.

To address this, we:

1. **Don't include function.json files in source control** (except for special cases like queue triggers)
2. **Generate them before deployment** using a script
3. **Remove them after deployment** to keep the repository clean

#### Managing function.json Files

To remove existing function.json files (except for special cases):

```bash
# PowerShell
.\scripts\remove_function_json.ps1
```

To generate function.json files when needed:

```bash
# Generate function.json files
python scripts/generate_function_json.py

# Deploy the application with the generated function.json files
func azure functionapp publish <APP_NAME>
```

This approach gives us the best of both worlds - clean code organization with the blueprint pattern, and Azure Portal compatibility when needed.

### Common Issues

1. **Azurite not starting**: Check if ports 10100, 10101, and 10102 are available
2. **Authentication errors**: Run `az login` to authenticate with Azure CLI
3. **Missing dependencies**: Run `pip install -r requirements.txt` to install all dependencies
4. **Import errors**: Make sure you're running from the project root directory
5. **Salesforce authentication**: Ensure your `config.py` file has valid credentials
6. **Database initialization**: Run `python scripts/init_database.py` to initialize the database
7. **API routing issues**:
   - Check that the frontend is using the correct endpoint URLs with the `/api/` prefix
   - Verify that the backend has proxy functions defined for all endpoints
   - Make sure the WrapperFunction/__init__.py file correctly routes requests to the ASGI app
   - Check the logs for detailed request/response information

### Logs

- Azurite logs are stored in the `.azurite` directory
- Function logs are displayed in the terminal when running locally
- In Azure, check the Application Insights logs or the Function App logs in the Azure Portal

## User Authentication

The application includes a complete user authentication system with the following features:

- User registration with secure password storage
- User login with JWT token-based authentication
- Token refresh for extended sessions
- Role-based access control
- Password hashing with salt for security
- Support for multiple hashing algorithms

### Database Schema

The user authentication system uses the following database tables:

- `User_Account`: Stores user profile information
- `App_User_Login`: Stores user login credentials
- `HashingAlgorithm`: Stores available password hashing algorithms
- `Role`: Stores user roles
- `App_User_Roles`: Maps users to roles
- `Permission`: Stores available permissions
- `Role_Permissions`: Maps roles to permissions
- `App_User_Token`: Stores refresh tokens

## Additional Documentation

- [Local Development Guide](references/local_development_guide.md): Detailed instructions for setting up and running the application locally
- [Troubleshooting Guide](references/troubleshooting_guide.md): Solutions to common issues
- [Legacy Code Cleanup](references/legacy_code_cleanup.md): Information about the code cleanup process
- [Test Structure](references/test_structure.md): Documentation about the test structure

#### Test Environment
- Environment variables are set in `tests/conftest.py`
- Local development flag: `IS_LOCAL_DEV=true`

### Azure Function Deployment

The pipeline (`pipeline-func-sfdc-dev.yml`) handles:
- Dependency installation
- Package archiving
- Azure Function deployment
- Slot swapping

## Contributing

1. Create a new branch from `dev`
2. Make your changes
3. Run tests locally
4. Submit a pull request

## License

[Add your license information here]

## Adding a New Task Type

When adding a new task type to the system, you need to register it in multiple places to ensure it works correctly. This guide outlines all the necessary steps:

### 1. Create the Task Implementation

First, create the task implementation file in the `task_processor/tasks/` directory:

```python
# task_processor/tasks/your_task.py
def process_your_task(processor, task_id, org_id, user_id, params, execution_log_id=None):
    """
    Process your task by implementing the required functionality.
    """
    # Task implementation here
```

### 2. Register the Task Type in background_processor.py

Add the task type constant to the list of task types:

```python
# In shared/background_processor.py
TASK_TYPE_YOUR_TASK = "your_task"
```

Add the task type to the priority mapping:

```python
# In shared/background_processor.py, TASK_TYPE_PRIORITY_MAP
TASK_TYPE_YOUR_TASK: TASK_PRIORITY_MEDIUM,  # Or HIGH/LOW as appropriate
```

If your task should always be enabled (without requiring a rule in the Rule table), add it to the always-enabled list:

```python
# In _is_task_enabled_for_user_policy method
if task_type in [TASK_TYPE_SFDC_AUTHENTICATE, TASK_TYPE_METADATA_EXTRACTION, TASK_TYPE_YOUR_TASK]:
    return True
```

Add the task type to the execution type mapping for the SQL database:

```python
# In the execution_type_map dictionary
TASK_TYPE_YOUR_TASK: "YourTaskName"
```

### 3. Import and Register in function_app.py

Import the task type constant and processing function in function_app.py:

```python
from task_processor import (
    # Other imports...
    process_your_task,
    TASK_TYPE_YOUR_TASK
)
```

Add the task type handling in the task processor logic:

```python
# In the process_task_message function
elif task_type == TASK_TYPE_YOUR_TASK:
    logging.info(f"Processing Your Task: {task_id}")
    process_your_task(processor, task_id, org_id, user_id, params, execution_log_id)
```

### 4. Import in task_processor/__init__.py

Import your task processing function in task_processor/__init__.py:

```python
from .tasks.your_task import process_your_task
```

Add the task type constant:

```python
TASK_TYPE_YOUR_TASK = "your_task"
```

Add the task type handling in the task processor:

```python
elif task_type == TASK_TYPE_YOUR_TASK:
    logger.info(f"[MAIN] Calling process_your_task for {task_id}")
    process_your_task(processor, task_id, org_id, user_id, params, execution_log_id)
```

### 5. Create Task Enqueuing Function (Optional)

If you need a dedicated endpoint to enqueue your task, create a function in the appropriate blueprint:

```python
@bp.route(route="api/your-task-endpoint", auth_level=func.AuthLevel.ANONYMOUS, methods=["POST"])
def enqueue_your_task(req: func.HttpRequest) -> func.HttpResponse:
    # Parse request
    # ...
    
    # Enqueue task
    task_id = processor.enqueue_task(
        task_type=TASK_TYPE_YOUR_TASK,
        org_id=org_id,
        user_id=user_id,
        params=params
    )
    
    return func.HttpResponse(
        json.dumps({"task_id": task_id}),
        mimetype="application/json"
    )
```

### 6. Update Pipeline for Dependencies (If Needed)

If your task requires external dependencies (like PMD for code analysis), update the pipeline file to install these dependencies:

```yaml
# In pipeline-func-sfdc-dev.yml
- script: |
    # Install dependencies for your task
  displayName: 'Install dependencies for your task'
```

### 7. Verify Command Syntax for External Tools

If your task uses external tools (like PMD), verify the correct command syntax:

```python
# For PMD 7.x and newer, the 'check' subcommand is required:
pmd_command = base_pmd_command + [
    'check',  # Required subcommand in PMD 7.x
    '-d', source_code_path,
    '-R', ruleset_path,
    '-f', 'csv',
    '--report-file', results_path
]
```

Always test the command locally before deploying to ensure compatibility with the installed version.

By following these steps, you ensure that your new task is properly registered throughout the system and will work correctly when enqueued.