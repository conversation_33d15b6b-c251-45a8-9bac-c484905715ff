import logging
import azure.functions as func
import json
from datetime import datetime, timezone

def main(req: func.HttpRequest) -> func.HttpResponse:
    """
    Direct roles function that bypasses ASGI
    
    This function returns a hardcoded list of roles without using the ASGI middleware.
    """
    logging.info('DirectRolesFunction processed a request.')
    
    # Log detailed request information
    logging.info(f"Request URL: {req.url}")
    logging.info(f"Request method: {req.method}")
    logging.info(f"Request headers: {dict(req.headers)}")
    logging.info(f"Request params: {dict(req.params)}")
    logging.info(f"Request route_params: {dict(req.route_params)}")
    
    # Create a hardcoded list of roles
    roles = [
        {
            "RoleId": "1",
            "Rolename": "Admin",
            "Description": "Administrator role with full access"
        },
        {
            "RoleId": "2",
            "Rolename": "User",
            "Description": "Standard user role with limited access"
        }
    ]
    
    response_data = {
        "success": True,
        "statusCode": 200,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "data": roles
    }
    
    return func.HttpResponse(
        json.dumps(response_data),
        mimetype="application/json",
        status_code=200
    )
