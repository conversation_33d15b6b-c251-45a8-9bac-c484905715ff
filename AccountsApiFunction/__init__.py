import logging
import azure.functions as func
import json
from datetime import datetime, timezone
import random

def main(req: func.HttpRequest) -> func.HttpResponse:
    """
    Direct implementation of the accounts API
    
    This function implements the accounts API without using the ASGI middleware.
    """
    logging.info('AccountsApiFunction processed a request.')
    
    # Log detailed request information
    logging.info(f"Request URL: {req.url}")
    logging.info(f"Request method: {req.method}")
    logging.info(f"Request headers: {dict(req.headers)}")
    logging.info(f"Request params: {dict(req.params)}")
    logging.info(f"Request route_params: {dict(req.route_params)}")
    
    # Handle GET request
    if req.method == "GET":
        # Create a hardcoded list of accounts
        accounts = [
            {
                "ID": "1001",
                "Name": "Test Account 1",
                "CreatedAt": datetime.now(timezone.utc).isoformat(),
                "IsActive": True
            },
            {
                "ID": "1002",
                "Name": "Test Account 2",
                "CreatedAt": datetime.now(timezone.utc).isoformat(),
                "IsActive": True
            }
        ]
        
        response_data = {
            "success": True,
            "statusCode": 200,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data": accounts
        }
        
        return func.HttpResponse(
            json.dumps(response_data),
            mimetype="application/json",
            status_code=200
        )
    
    # Handle POST request
    elif req.method == "POST":
        try:
            # Parse request body
            req_body = req.get_json()
            name = req_body.get("name")
            
            if not name:
                response_data = {
                    "success": False,
                    "statusCode": 400,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "message": "Name is required"
                }
                
                return func.HttpResponse(
                    json.dumps(response_data),
                    mimetype="application/json",
                    status_code=400
                )
            
            # Generate account ID
            account_id = str(random.randint(1000, 9999))
            
            # Create account
            created_account = {
                "ID": account_id,
                "Name": name,
                "CreatedAt": datetime.now(timezone.utc).isoformat(),
                "IsActive": True
            }
            
            response_data = {
                "success": True,
                "statusCode": 201,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "data": created_account
            }
            
            return func.HttpResponse(
                json.dumps(response_data),
                mimetype="application/json",
                status_code=201
            )
        except ValueError:
            response_data = {
                "success": False,
                "statusCode": 400,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "message": "Invalid request body"
            }
            
            return func.HttpResponse(
                json.dumps(response_data),
                mimetype="application/json",
                status_code=400
            )
    
    # Handle unsupported methods
    else:
        response_data = {
            "success": False,
            "statusCode": 405,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "message": "Method not allowed"
        }
        
        return func.HttpResponse(
            json.dumps(response_data),
            mimetype="application/json",
            status_code=405
        )
