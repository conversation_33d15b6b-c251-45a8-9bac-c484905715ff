import os
from azure.storage.queue import QueueServiceClient

# Use the connection string from local.settings.json
connection_string = "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;QueueEndpoint=http://127.0.0.1:10001/devstoreaccount1;"

# Create a queue service client
queue_service = QueueServiceClient.from_connection_string(connection_string)

# List all queues
print("Listing all queues:")
queues = queue_service.list_queues()
for queue in queues:
    print(f"Queue: {queue.name}")
    
    # Get the queue client
    queue_client = queue_service.get_queue_client(queue.name)
    
    # Get queue properties
    properties = queue_client.get_queue_properties()
    print(f"  Approximate message count: {properties.approximate_message_count}")
    
    # Peek messages (up to 32)
    if properties.approximate_message_count > 0:
        messages = queue_client.peek_messages(max_messages=32)
        print(f"  Peeking at messages:")
        for msg in messages:
            print(f"    Message ID: {msg.id}")
            print(f"    Insertion time: {msg.inserted_on}")
            print(f"    Content: {msg.content}")
            print()
    
    print()
