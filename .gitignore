# Python virtual environment
.venv

# Conflicting modules
/asyncio/

# Python cache files
.python_packages
__pycache__
*.pyc
.pytest_cache

# Azurite local DB files
.azurite/
.azurite/__azurite_db_*.json
__blobstorage__/
__queuestorage__/

# Local settings and credentials
# local.settings.json
# config.py

# VS Code settings
.vscode/*
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

certs
static
# Documentation (keep in source control)
# references

# Generated function.json files
*/function.json
!task_processor/function.json
!WrapperFunction/function.json

logs/
function.log
best_practices.xml
.DS_Store
.DS_Store
.DS_Store
.DS_Store
func.log
SFDC-local-testing.logs
temp.txt
__pycache__/function_app.cpython-311.pyc
__azurite_db_table__.json
__azurite_db_blob__.json
