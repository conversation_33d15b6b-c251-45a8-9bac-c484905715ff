"""
Check Azurite Connection

This script checks if Azurite is running and accessible.
"""

import os
import sys
import logging
from azure.data.tables import TableServiceClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('check_azurite')

def check_azurite_connection():
    """Check if Azurite is running and accessible"""
    logger.info("Checking Azurite connection...")
    
    # Get connection string from environment variable or use default
    connection_string = os.environ.get(
        "AzureWebJobsStorage",
        "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;TableEndpoint=http://127.0.0.1:10002/devstoreaccount1;"
    )
    
    logger.info(f"Using connection string: {connection_string}")
    
    try:
        # Create table service client
        table_service = TableServiceClient.from_connection_string(connection_string)
        logger.info("Successfully created table service client")
        
        # List tables
        tables = list(table_service.list_tables())
        logger.info(f"Successfully listed tables: {[table.name for table in tables]}")
        
        # Create a test table
        test_table_name = "azuritetest"
        logger.info(f"Creating test table: {test_table_name}")
        
        try:
            table_client = table_service.create_table_if_not_exists(test_table_name)
            logger.info(f"Successfully created test table: {test_table_name}")
            
            # Insert a test entity
            entity = {
                "PartitionKey": "test",
                "RowKey": "1",
                "TestProperty": "TestValue"
            }
            
            logger.info(f"Inserting test entity: {entity}")
            table_client.upsert_entity(entity)
            logger.info("Successfully inserted test entity")
            
            # Query the test entity
            logger.info("Querying test entity")
            queried_entity = table_client.get_entity("test", "1")
            logger.info(f"Successfully queried test entity: {queried_entity}")
            
            # Delete the test entity
            logger.info("Deleting test entity")
            table_client.delete_entity("test", "1")
            logger.info("Successfully deleted test entity")
            
            # Delete the test table
            logger.info(f"Deleting test table: {test_table_name}")
            table_service.delete_table(test_table_name)
            logger.info(f"Successfully deleted test table: {test_table_name}")
        except Exception as table_error:
            logger.error(f"Error working with test table: {str(table_error)}")
        
        return True
    except Exception as e:
        logger.error(f"Error connecting to Azurite: {str(e)}")
        return False

def main():
    """Main function"""
    logger.info("Starting Azurite connection check...")
    
    # Check Azurite connection
    success = check_azurite_connection()
    
    if success:
        logger.info("Azurite connection check completed successfully")
    else:
        logger.error("Azurite connection check failed")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
