"""
Salesforce Utilities Module

This module provides utility functions for working with Salesforce using simple-salesforce.
It serves as a bridge between the existing codebase and the new SalesforceClient implementation.
"""

import logging
from typing import Dict, Any, Optional, List, Tuple, Union
import json
import datetime

# Import the SalesforceClient
from shared.salesforce_client import SalesforceClient
from shared.azure_services import get_secret

# Configure module-level logger
logger = logging.getLogger(__name__)

# Cache for Salesforce clients to avoid creating multiple instances
_salesforce_clients = {}


def get_salesforce_client(
    instance_url: Optional[str] = None,
    access_token: Optional[str] = None,
    integration_id: Optional[str] = None,
    environment: Optional[str] = "production",
    use_jwt: bool = False
) -> Optional[SalesforceClient]:
    """
    Get or create a Salesforce client

    This function provides a unified way to get a Salesforce client instance,
    either by using an existing access token or by retrieving credentials from Key Vault.

    Args:
        instance_url: Salesforce instance URL (optional if using integration_id)
        access_token: Salesforce access token (optional if using integration_id)
        integration_id: Integration ID to retrieve credentials from Key Vault
        environment: Environment (production or sandbox)
        use_jwt: Whether to use JWT Bearer flow instead of Client Credentials flow

    Returns:
        SalesforceClient: Initialized Salesforce client or None if error
    """
    global _salesforce_clients

    # Generate a cache key based on the parameters
    cache_key = f"{instance_url}:{integration_id}:{environment}:{use_jwt}"

    # Check if we already have a client for this key
    if cache_key in _salesforce_clients:
        logger.debug(f"Using cached Salesforce client for {cache_key}")
        return _salesforce_clients[cache_key]

    try:
        # Log input parameters for debugging
        logger.info(f"Creating Salesforce client with parameters: instance_url={instance_url}, "
                   f"access_token={'present' if access_token else 'missing'}, "
                   f"integration_id={integration_id}, environment={environment}")

        # If we have an access token and instance URL, use them directly
        if access_token and instance_url:
            logger.info(f"Creating Salesforce client with access token for {instance_url}")
            client = SalesforceClient(
                instance_url=instance_url,
                access_token=access_token
            )
            _salesforce_clients[cache_key] = client
            return client

        # If we have an integration ID, retrieve credentials from Key Vault
        if integration_id:
            logger.info(f"Creating Salesforce client for integration {integration_id}")

            # Get client credentials from Key Vault
            service_name = f"salesforce-{integration_id}"
            client_id_key = f"{service_name}-client-id"

            # Determine if this is a sandbox
            is_sandbox = environment.lower() == "sandbox"

            if use_jwt:
                # Get JWT Bearer flow credentials
                username_key = f"{service_name}-username"
                private_key_key = f"{service_name}-private-key"

                logger.info(f"Using JWT Bearer flow, retrieving secrets with keys: {client_id_key}, {username_key}, and {private_key_key}")

                client_id = get_secret(client_id_key)
                username = get_secret(username_key)
                private_key = get_secret(private_key_key)

                if not client_id:
                    logger.error(f"Failed to retrieve client ID for integration {integration_id} with key {client_id_key}")

                if not username:
                    logger.error(f"Failed to retrieve username for integration {integration_id} with key {username_key}")

                if not private_key:
                    logger.error(f"Failed to retrieve private key for integration {integration_id} with key {private_key_key}")

                if not client_id or not username or not private_key:
                    logger.error(f"Failed to retrieve JWT Bearer flow credentials for integration {integration_id}")
                    return None

                logger.info(f"Retrieved JWT Bearer flow credentials for integration {integration_id}, creating client with instance_url={instance_url}")

                # Create client with JWT Bearer flow credentials
                client = SalesforceClient(
                    instance_url=instance_url,
                    client_id=client_id,
                    is_sandbox=is_sandbox,
                    use_jwt=True,
                    username=username,
                    private_key=private_key
                )
            else:
                # Get Client Credentials flow credentials
                client_secret_key = f"{service_name}-client-secret"

                logger.info(f"Using Client Credentials flow, retrieving secrets with keys: {client_id_key} and {client_secret_key}")

                client_id = get_secret(client_id_key)
                client_secret = get_secret(client_secret_key)

                if not client_id:
                    logger.error(f"Failed to retrieve client ID for integration {integration_id} with key {client_id_key}")

                if not client_secret:
                    logger.error(f"Failed to retrieve client secret for integration {integration_id} with key {client_secret_key}")

                if not client_id or not client_secret:
                    logger.error(f"Failed to retrieve client credentials for integration {integration_id}")
                    return None

                logger.info(f"Retrieved Client Credentials flow credentials for integration {integration_id}, creating client with instance_url={instance_url}")

                # Create client with Client Credentials flow
                client = SalesforceClient(
                    instance_url=instance_url,
                    client_id=client_id,
                    client_secret=client_secret,
                    is_sandbox=is_sandbox
                )

            _salesforce_clients[cache_key] = client
            return client

        logger.error("Insufficient parameters to create Salesforce client: "
                    f"instance_url={instance_url}, access_token={'present' if access_token else 'missing'}, "
                    f"integration_id={integration_id}")
        return None
    except Exception as e:
        logger.error(f"Error creating Salesforce client: {str(e)}")
        return None


async def execute_salesforce_query(
    query: str,
    access_token: Optional[str] = None,
    instance_url: Optional[str] = None,
    integration_id: Optional[str] = None,
    environment: Optional[str] = "production",
    api_version: Optional[str] = None,  # Ignored, kept for compatibility
    include_deleted: bool = False,
    use_jwt: bool = False
) -> Optional[Dict[str, Any]]:
    """
    Execute a Salesforce API query

    This function is a drop-in replacement for the existing execute_salesforce_query function,
    but uses the SalesforceClient internally.

    Args:
        query: SOQL query to execute
        access_token: Salesforce access token (optional if using integration_id)
        instance_url: Salesforce instance URL (optional if using integration_id)
        integration_id: Integration ID to retrieve credentials from Key Vault
        environment: Environment (production or sandbox)
        api_version: API version (ignored, kept for compatibility)
        include_deleted: Whether to include deleted records
        use_jwt: Whether to use JWT Bearer flow instead of Client Credentials flow

    Returns:
        Dict: Query results or None if error
    """
    try:
        # Get Salesforce client
        client = get_salesforce_client(
            instance_url=instance_url,
            access_token=access_token,
            integration_id=integration_id,
            environment=environment,
            use_jwt=use_jwt
        )

        if not client:
            logger.error("Failed to get Salesforce client")
            return None

        # Execute query
        return client.query(query, include_deleted=include_deleted)
    except Exception as e:
        logger.error(f"Error executing Salesforce query: {str(e)}")
        return None


async def execute_salesforce_tooling_query(
    query: str,
    access_token: Optional[str] = None,
    instance_url: Optional[str] = None,
    integration_id: Optional[str] = None,
    environment: Optional[str] = "production",
    api_version: Optional[str] = None,  # Ignored, kept for compatibility
    use_jwt: bool = False
) -> Optional[Dict[str, Any]]:
    """
    Execute a Salesforce Tooling API query

    This function is a drop-in replacement for the existing execute_salesforce_tooling_query function,
    but uses the SalesforceClient internally.

    Args:
        query: SOQL query to execute
        access_token: Salesforce access token (optional if using integration_id)
        instance_url: Salesforce instance URL (optional if using integration_id)
        integration_id: Integration ID to retrieve credentials from Key Vault
        environment: Environment (production or sandbox)
        api_version: API version (ignored, kept for compatibility)
        use_jwt: Whether to use JWT Bearer flow instead of Client Credentials flow

    Returns:
        Dict: Query results or None if error
    """
    try:
        # Get Salesforce client
        client = get_salesforce_client(
            instance_url=instance_url,
            access_token=access_token,
            integration_id=integration_id,
            environment=environment,
            use_jwt=use_jwt
        )

        if not client:
            logger.error("Failed to get Salesforce client")
            return None

        # Execute query
        return client.tooling_query(query)
    except Exception as e:
        logger.error(f"Error executing Salesforce Tooling API query: {str(e)}")
        return None


def get_salesforce_access_token(
    integration_id: Optional[str] = None,
    tenant_url: Optional[str] = None,
    environment: Optional[str] = "production",
    use_jwt: bool = False
) -> Optional[Dict[str, str]]:
    """
    Get Salesforce access token

    This function is a drop-in replacement for the existing get_salesforce_access_token function,
    but uses the SalesforceClient internally.

    Args:
        integration_id: Integration ID to retrieve credentials from Key Vault
        tenant_url: Tenant URL
        environment: Environment (production or sandbox)
        use_jwt: Whether to use JWT Bearer flow instead of Client Credentials flow

    Returns:
        Dict[str, str]: Dictionary with access_token and instance_url, or None if error
    """
    try:
        logger.info(f"Getting Salesforce access token for integration_id={integration_id}, "
                   f"tenant_url={tenant_url}, environment={environment}")

        # Get Salesforce client
        client = get_salesforce_client(
            instance_url=tenant_url,
            integration_id=integration_id,
            environment=environment,
            use_jwt=use_jwt
        )

        if not client:
            logger.error(f"Failed to get Salesforce client for integration_id={integration_id}, "
                        f"tenant_url={tenant_url}, environment={environment}")
            return None

        # Check if we have access token and instance URL
        if not client.access_token or not client.instance_url:
            logger.error(f"Salesforce client missing access token or instance URL: "
                        f"access_token={'present' if client.access_token else 'missing'}, "
                        f"instance_url={'present' if client.instance_url else 'missing'}")
            return None

        logger.info(f"Successfully obtained Salesforce access token for {client.instance_url}")

        # Return access token and instance URL as a dictionary
        return {
            "access_token": client.access_token,
            "instance_url": client.instance_url
        }
    except Exception as e:
        logger.error(f"Error getting Salesforce access token: {str(e)}")
        return None


def test_salesforce_connection(
    client_id: str,
    client_secret: str,
    tenant_url: str,
    is_sandbox: bool = False
) -> Tuple[bool, Optional[str], Optional[Dict[str, Any]]]:
    """
    Test Salesforce connection with Client Credentials flow

    This function is a drop-in replacement for the existing test_salesforce_connection_jwt function,
    but uses the SalesforceClient internally.

    Args:
        client_id: Salesforce client ID
        client_secret: Salesforce client secret
        tenant_url: Salesforce tenant URL
        is_sandbox: Whether the tenant is a sandbox

    Returns:
        Tuple[bool, str, Dict]: Success status, error message (if any), and connection details
    """
    try:
        # Create a temporary client for testing
        client = SalesforceClient(
            instance_url=tenant_url,
            client_id=client_id,
            client_secret=client_secret,
            is_sandbox=is_sandbox
        )

        # Test connection
        return client.test_client_credentials_flow()
    except Exception as e:
        logger.error(f"Error testing Salesforce connection: {str(e)}")
        return False, str(e), None


def test_jwt_bearer_connection(
    client_id: str,
    username: str,
    private_key: str,
    tenant_url: str,
    is_sandbox: bool = False
) -> Tuple[bool, Optional[str], Optional[Dict[str, Any]]]:
    """
    Test Salesforce connection with JWT Bearer flow

    Args:
        client_id: Salesforce client ID (Connected App Consumer Key)
        username: Salesforce username
        private_key: Private key for JWT signing
        tenant_url: Salesforce tenant URL
        is_sandbox: Whether the tenant is a sandbox

    Returns:
        Tuple[bool, str, Dict]: Success status, error message (if any), and connection details
    """
    try:
        # Import the JWT test function
        from shared.salesforce_jwt_auth import test_jwt_bearer_flow

        # Test JWT Bearer flow
        return test_jwt_bearer_flow(
            client_id=client_id,
            tenant_url=tenant_url,
            username=username,
            private_key=private_key,
            is_sandbox=is_sandbox
        )
    except Exception as e:
        logger.error(f"Error testing Salesforce JWT Bearer connection: {str(e)}")
        return False, str(e), None
