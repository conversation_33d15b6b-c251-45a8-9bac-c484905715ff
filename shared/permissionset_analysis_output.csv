Name,Description,Match,OWASP,PermissionSetValue-UserPermissions,RiskTypeBasedOnSeverity,SalesforceSetting,StandardValue,UserType
API_Access_JWT,"Grants the user ability to upload, edit, delete, and manage files and
                attachments in Salesforce. This includes files linked to records and stored in
                Content or Notes & Attachments.",False,A1: Broken Access Control,true,Informational,ApiEnabled,FALSE,blank
Experience_Profile_Manager,Allows users to export data weekly.,False,A1: Broken Access Control,true,Low,AccessCMC,FALSE,salesforce
Experience_Profile_Manager,Allows users to run reports.,False,A1: Broken Access Control,true,High,RunReports,FALSE,salesforce
