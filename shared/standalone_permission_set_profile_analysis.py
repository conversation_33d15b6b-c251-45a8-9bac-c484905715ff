import os
import json
import logging
import xml.etree.ElementTree as ET
import re
from datetime import datetime
from typing import List, Dict, Any
import subprocess
import csv

# Configure logger
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# --- Helper Functions ---
def normalize_value(val):
    if val is None:
        return None
    val = val.strip().lower()
    if val in ['❌ false', 'x false', 'false', 'no', 'disabled', '0']:
        return 'false'
    if val in ['✅ true', 'tickmark/true', 'true', 'yes', 'enabled', '1']:
        return 'true'
    return val

def normalize_key(key):
    if key is None:
        return None
    return key.strip().lower().replace(' ', '')

def normalize_permission_name(name):
    if not name:
        return ''
    return name.strip().lower().replace(' ', '').replace('_', '')

def normalize_user_type(val):
    return (val or '').strip().lower().replace(' ', '')

def extract_user_license_from_xml(xml_bytes):
    try:
        if isinstance(xml_bytes, bytes):
            xml_str = xml_bytes.decode('utf-8', errors='ignore')
        else:
            xml_str = xml_bytes
        match = re.search(r'<userLicense>(.*?)</userLicense>', xml_str, re.DOTALL)
        if match:
            return match.group(1).strip()
        else:
            logger.debug("No <userLicense> tag found in XML.")
    except Exception as e:
        logger.error(f"Error extracting userLicense with regex: {e}")
        logger.info(f"[DEBUG] Error extracting userLicense. XML preview: {xml_bytes[:200] if isinstance(xml_bytes, bytes) else str(xml_bytes)[:200]}")
    return ''

def extract_license_from_permissionset_xml(xml_bytes):
    try:
        if isinstance(xml_bytes, bytes):
            xml_str = xml_bytes.decode('utf-8', errors='ignore')
        else:
            xml_str = xml_bytes
        # Try both lowercase and uppercase for <license> tag
        match = re.search(r'<license>(.*?)</license>', xml_str, re.DOTALL | re.IGNORECASE)
        if match:
            return match.group(1).strip()
        else:
            logger.debug("No <license> tag found in XML.")
    except Exception as e:
        logger.error(f"Error extracting license with regex: {e}")
        logger.info(f"[DEBUG] Error extracting license. XML preview: {xml_bytes[:200] if isinstance(xml_bytes, bytes) else str(xml_bytes)[:200]}")
    return ''

def parse_profile_permissions(xml_bytes):
    perms = []
    try:
        root = ET.fromstring(xml_bytes)
        for perm in root.iter():
            if perm.tag.endswith('userPermissions'):
                name_elem = None
                enabled_elem = None
                for child in perm:
                    if child.tag.endswith('name'):
                        name_elem = child
                    elif child.tag.endswith('enabled'):
                        enabled_elem = child
                if name_elem is not None and enabled_elem is not None:
                    perms.append({
                        'name': name_elem.text.strip() if name_elem.text else '',
                        'enabled': enabled_elem.text.strip() if enabled_elem.text else ''
                    })
    except ET.ParseError as e:
        logger.error(f"[ERROR] XML parsing error in profile: {e}")
        logger.info(f"[DEBUG] XML parsing error. XML preview: {xml_bytes[:200] if isinstance(xml_bytes, bytes) else str(xml_bytes)[:200]}")
    except Exception as e:
        logger.error(f"[ERROR] Error parsing userPermissions: {e}")
        logger.info(f"[DEBUG] Error parsing userPermissions. XML preview: {xml_bytes[:200] if isinstance(xml_bytes, bytes) else str(xml_bytes)[:200]}")
    return perms

def parse_permissionset_permissions(xml_bytes):
    perms = []
    try:
        root = ET.fromstring(xml_bytes)
        for perm in root.iter():
            if perm.tag.endswith('userPermissions'):
                name_elem = None
                enabled_elem = None
                for child in perm:
                    if child.tag.endswith('name'):
                        name_elem = child
                    elif child.tag.endswith('enabled'):
                        enabled_elem = child
                if name_elem is not None and enabled_elem is not None:
                    perms.append({
                        'name': name_elem.text.strip() if name_elem.text else '',
                        'enabled': enabled_elem.text.strip() if enabled_elem.text else ''
                    })
    except ET.ParseError as e:
        logger.error(f"[ERROR] XML parsing error in permissionset: {e}")
        logger.info(f"[DEBUG] XML parsing error. XML preview: {xml_bytes[:200] if isinstance(xml_bytes, bytes) else str(xml_bytes)[:200]}")
    except Exception as e:
        logger.error(f"[ERROR] Error parsing userPermissions: {e}")
        logger.info(f"[DEBUG] Error parsing userPermissions. XML preview: {xml_bytes[:200] if isinstance(xml_bytes, bytes) else str(xml_bytes)[:200]}")
    return perms

def load_best_practices_xml(xml_path):
    tree = ET.parse(xml_path)
    root = tree.getroot()
    best_practices = []
    for usertype_elem in root.findall('UserType'):
        usertype = usertype_elem.attrib.get('name', '')
        for practice in usertype_elem.findall('Practice'):
            bp = {child.tag: child.text for child in practice}
            bp['UserType'] = usertype  # Add UserType from attribute
            best_practices.append(bp)
    return best_practices

def sfdx_query(soql, org_alias="myOrgAlias"):
    cmd = [
        "sfdx", "force:data:soql:query",
        "-q", soql,
        "-u", org_alias,
        "--json"
    ]
    result = subprocess.run(cmd, capture_output=True, text=True)
    if result.returncode == 0:
        return json.loads(result.stdout)
    else:
        logger.error(f"SFDX error: {result.stderr}")
        return None

def permission_set_assignments_for_all_profiles_fast(org_alias="myOrgAlias", output_csv="permission_sets_per_profile.csv"):
    # Use a single SOQL query to get profile and permission set assignments directly
    soql_psa = "SELECT Assignee.Profile.Name, Assignee.ProfileId, PermissionSet.Name FROM PermissionSetAssignment"
    psa_result = sfdx_query(soql_psa, org_alias)
    psa_records = psa_result.get('result', {}).get('records', []) if psa_result else []
    logger.info(f"Fetched {len(psa_records)} permission set assignments (with profile info).")

    # Aggregate counts per (ProfileId, ProfileName, PermissionSetName)
    assignment_counts = {}
    for psa in psa_records:
        assignee = psa.get('Assignee')
        permission_set = psa.get('PermissionSet')
        profile_id = assignee.get('ProfileId') if assignee else None
        profile = assignee.get('Profile') if assignee else None
        profile_name = profile.get('Name') if profile else None
        ps_name = permission_set.get('Name') if permission_set else None
        if profile_id and profile_name and ps_name:
            key = (profile_id, profile_name, ps_name)
            assignment_counts[key] = assignment_counts.get(key, 0) + 1
    profile_ids_in_output = set([k[0] for k in assignment_counts.keys()])
    logger.info(f"ProfileIds in output: {profile_ids_in_output}")
    logger.info(f"ProfileId to Name mapping in output: { {pid: k[1] for k in assignment_counts.keys() for pid in [k[0]]} }")

    # Write to CSV
    with open(output_csv, "w", newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(["ProfileId", "ProfileName", "PermissionSetName", "AssignmentCount"])
        for (profile_id, profile_name, ps_name), count in assignment_counts.items():
            writer.writerow([profile_id, profile_name, ps_name, count])
    logger.info(f"Permission set assignments for all profiles written to {output_csv} (fast method)")

def write_results_to_csv(results: dict, filename: str):
    # Flatten the results for CSV output
    with open(filename, "w", newline='') as csvfile:
        if not results:
            return
        # Find all unique keys for the header
        all_keys = set()
        for obj_list in results.values():
            for obj in obj_list:
                all_keys.update(obj.keys())
        fieldnames = ["Name"] + sorted(all_keys)
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        for name, obj_list in results.items():
            for obj in obj_list:
                row = {"Name": name}
                row.update(obj)
                writer.writerow(row)

def process_profiles_from_local(folder_path: str, best_practices_path: str) -> dict:
    """Analyze local profile XMLs against best-practices and return results as a dict."""
    logger.info(f"Processing profiles from {folder_path}")
    if not os.path.exists(folder_path):
        logger.error(f"Profiles folder does not exist: {folder_path}")
        return {}
    try:
        best_practices = load_best_practices_xml(best_practices_path)
    except Exception as e:
        logger.error(f"Failed to load best-practices XML: {e}")
        return {}
    if not best_practices:
        logger.error("Best-practices XML is empty or invalid.")
        return {}
    logger.info("Best-practices SalesforceSetting values (raw and normalized):")
    for bp in best_practices:
        raw_setting = bp.get('SalesforceSetting')
        norm_setting = normalize_permission_name(raw_setting)
        logger.info(f"  - Raw: '{raw_setting}', Normalized: '{norm_setting}'")
    results = {}
    for fname in os.listdir(folder_path):
        if not fname.endswith('.profile'):
            continue
        profile_name = fname.replace('.profile', '')
        fpath = os.path.join(folder_path, fname)
        try:
            with open(fpath, 'rb') as f:
                xml_bytes = f.read()
        except Exception as e:
            logger.error(f"Failed to read profile file {fpath}: {e}")
            continue
        user_license = extract_user_license_from_xml(xml_bytes)
        logger.info(f"[DEBUG] Profile '{profile_name}': user_license='{user_license}'")
        user_permissions = parse_profile_permissions(xml_bytes)
        logger.info(f"[DEBUG] Profile '{profile_name}': user_permissions={user_permissions}")
        raw_names = [p['name'] for p in user_permissions]
        norm_names = [normalize_permission_name(p['name']) for p in user_permissions]
        logger.info(f"[DEBUG] Profile '{profile_name}': permission names (raw): {raw_names}")
        logger.info(f"[DEBUG] Profile '{profile_name}': permission names (normalized): {norm_names}")
        results_arr = []
        for bp in best_practices:
            bp_user_type = normalize_user_type(bp.get('UserType'))
            profile_user_type = normalize_user_type(user_license)
            if (bp_user_type == profile_user_type) or (not profile_user_type and bp_user_type == 'blank'):
                bp_setting = (bp.get('SalesforceSetting') or '').strip()
                bp_standard_value = (bp.get('StandardValue') or '').strip()
                normalized_bp_setting = normalize_permission_name(bp_setting)
                logger.info(f"[DEBUG] Comparing: profile '{profile_name}' setting '{bp_setting}' (normalized: '{normalized_bp_setting}') against all normalized permission names: {norm_names}")
                found = False
                for p in user_permissions:
                    if normalize_permission_name(p['name']) == normalized_bp_setting:
                        logger.info(f"[DEBUG] MATCH FOUND: '{bp_setting}' matches permission '{p['name']}' in profile '{profile_name}'")
                        found = True
                if not found:
                    logger.info(f"[DEBUG] NO MATCH: '{bp_setting}' not found in profile '{profile_name}' permissions.")
                profile_perm = next((p for p in user_permissions if normalize_permission_name(p['name']) == normalized_bp_setting), None)
                profile_value = profile_perm['enabled'] if profile_perm else ''
                match = normalize_value(profile_value) == normalize_value(bp_standard_value)
                results_arr.append({
                    'SalesforceSetting': bp_setting,
                    'StandardValue': bp_standard_value,
                    'ProfileValue': profile_value,
                    'Match': match,
                    'MissingInProfile': profile_perm is None,
                    'Description': bp.get('Description'),
                    'OWASP': bp.get('OWASP'),
                    'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity')
                })
        logger.info(f"[DEBUG] Profile '{profile_name}': results={results_arr}")
        results[profile_name] = results_arr
    return results

def process_permissionsets_from_local(folder_path: str, best_practices_path: str) -> dict:
    """Analyze local permission set XMLs against best-practices and return results as a dict. Also writes debug info to CSV."""
    logger.info(f"Processing permission sets from {folder_path}")
    if not os.path.exists(folder_path):
        logger.error(f"Permission sets folder does not exist: {folder_path}")
        return {}
    try:
        best_practices = load_best_practices_xml(best_practices_path)
    except Exception as e:
        logger.error(f"Failed to load best-practices XML: {e}")
        return {}
    if not best_practices:
        logger.error("Best-practices XML is empty or invalid.")
        return {}
    debug_log_path = "permission_set_debug_log.csv"
    try:
        debug_csvfile = open(debug_log_path, "w", newline='')
    except Exception as e:
        logger.error(f"Failed to open debug log file {debug_log_path}: {e}")
        return {}
    debug_writer = csv.writer(debug_csvfile)
    debug_writer.writerow(["PermissionSetName", "RawLicense", "NormalizedLicense", "RawUserType", "NormalizedUserType", "MatchNote"])
    results = {}
    for fname in os.listdir(folder_path):
        if not fname.endswith('.permissionset'):
            continue
        ps_name = fname.replace('.permissionset', '')
        fpath = os.path.join(folder_path, fname)
        try:
            with open(fpath, 'rb') as f:
                xml_bytes = f.read()
        except Exception as e:
            logger.error(f"Failed to read permissionset file {fpath}: {e}")
            continue
        license_val = extract_license_from_permissionset_xml(xml_bytes)
        normalized_license = normalize_user_type(license_val)
        debug_writer.writerow([ps_name, license_val, normalized_license, '', '', 'Raw license and normalized license'])
        logger.info(f"[DEBUG] PermissionSet '{ps_name}': Raw license value: '{license_val}', Normalized: '{normalized_license}'")
        logger.info(f"[DEBUG] All UserTypes in best-practices (raw and normalized):")
        for bp in best_practices:
            raw_ut = bp.get('UserType')
            norm_ut = normalize_user_type(raw_ut)
            debug_writer.writerow([ps_name, license_val, normalized_license, raw_ut, norm_ut, 'Best-practices UserType'])
            logger.info(f"  - Raw: '{raw_ut}', Normalized: '{norm_ut}'")
        relevant_bps = [bp for bp in best_practices if normalize_user_type(bp.get('UserType')) == normalized_license]
        matched_user_type = normalized_license if relevant_bps else 'blank'
        if relevant_bps:
            debug_writer.writerow([ps_name, license_val, normalized_license, '', '', f'Matched UserType: {matched_user_type}'])
        else:
            debug_writer.writerow([ps_name, license_val, normalized_license, '', '', 'No match, fallback to blank'])
        if not relevant_bps:
            # Fallback to blank
            relevant_bps = [bp for bp in best_practices if normalize_user_type(bp.get('UserType')) == 'blank']
        logger.info(f"[DEBUG] PermissionSet '{ps_name}': Using UserType='{matched_user_type}' for best-practices matching.")
        if not relevant_bps:
            logger.info(f"[DEBUG] PermissionSet '{ps_name}': No best-practices found for UserType='{matched_user_type}', skipping.")
            continue
        results_arr = []
        try:
            user_permissions = parse_permissionset_permissions(xml_bytes)
        except Exception as e:
            logger.error(f"Failed to parse permissionset XML for {ps_name}: {e}")
            continue
        raw_names = [p['name'] for p in user_permissions]
        norm_names = [normalize_permission_name(p['name']) for p in user_permissions]
        for bp in relevant_bps:
            bp_setting = (bp.get('SalesforceSetting') or '').strip()
            bp_standard_value = (bp.get('StandardValue') or '').strip()
            normalized_bp_setting = normalize_permission_name(bp_setting)
            logger.info(f"[DEBUG] Comparing: permissionset '{ps_name}' setting '{bp_setting}' (normalized: '{normalized_bp_setting}') against all normalized permission names: {norm_names}")
            ps_perm = next((p for p in user_permissions if normalize_permission_name(p['name']) == normalized_bp_setting), None)
            if ps_perm is not None:
                ps_value = ps_perm['enabled']
                match = normalize_value(ps_value) == normalize_value(bp_standard_value)
                if not match:
                    results_arr.append({
                        'SalesforceSetting': bp_setting,
                        'StandardValue': bp_standard_value,
                        'PermissionSetValue-UserPermissions': ps_value,
                        'Match': False,
                        'UserType': matched_user_type,
                        'Description': bp.get('Description'),
                        'OWASP': bp.get('OWASP'),
                        'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity')
                    })
        logger.info(f"[DEBUG] PermissionSet '{ps_name}': results={results_arr}")
        if results_arr:
            results[ps_name] = results_arr
    debug_csvfile.close()
    return results

if __name__ == "__main__":
    org_alias = "myOrgAlias"  # <-- Define org_alias at the top so it is available for all function calls
    # Example usage for local testing
    profiles_folder = "./profiles"
    permissionsets_folder = "./permissionsets"
    best_practices_path = "./best_practices/Profiles_PermissionSetRisks-BestPractice.xml"
    
    print("--- PROFILE ANALYSIS ---")
    profile_results = process_profiles_from_local(profiles_folder, best_practices_path)
    print(json.dumps(profile_results, indent=2))
    with open("profile_analysis_output.json", "w") as f:
        json.dump(profile_results, f, indent=2)
    write_results_to_csv(profile_results, "profile_analysis_output.csv")

    print("--- PERMISSION SET ANALYSIS ---")
    permissionset_results = process_permissionsets_from_local(permissionsets_folder, best_practices_path)
    print(json.dumps(permissionset_results, indent=2))
    with open("permissionset_analysis_output.json", "w") as f:
        json.dump(permissionset_results, f, indent=2)
    write_results_to_csv(permissionset_results, "permissionset_analysis_output.csv")

    # --- SFDX Permission Set Assignments for All Active Profiles (FAST) ---
    permission_set_assignments_for_all_profiles_fast(org_alias, output_csv="permission_sets_per_profile.csv") 