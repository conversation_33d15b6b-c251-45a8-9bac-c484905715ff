{"API_Access_JWT": [{"SalesforceSetting": "A<PERSON><PERSON><PERSON><PERSON>", "StandardValue": "FALSE", "PermissionSetValue-UserPermissions": "true", "Match": false, "UserType": "blank", "Description": "Grants the user ability to upload, edit, delete, and manage files and\n                attachments in Salesforce. This includes files linked to records and stored in\n                Content or Notes & Attachments.", "OWASP": "A1: Broken Access Control", "RiskTypeBasedOnSeverity": "Informational"}], "Experience_Profile_Manager": [{"SalesforceSetting": "AccessCMC", "StandardValue": "FALSE", "PermissionSetValue-UserPermissions": "true", "Match": false, "UserType": "salesforce", "Description": "Allows users to export data weekly.", "OWASP": "A1: Broken Access Control", "RiskTypeBasedOnSeverity": "Low"}, {"SalesforceSetting": "RunReports", "StandardValue": "FALSE", "PermissionSetValue-UserPermissions": "true", "Match": false, "UserType": "salesforce", "Description": "Allows users to run reports.", "OWASP": "A1: Broken Access Control", "RiskTypeBasedOnSeverity": "High"}]}