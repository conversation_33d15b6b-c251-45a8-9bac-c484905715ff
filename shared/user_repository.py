"""
User Repository Module

This module provides repository classes for user-related data access operations.
It implements the repository pattern to abstract data access logic from business logic.

Best practices implemented:
- Repository pattern for data access abstraction
- Proper error handling and logging
- Support for both local development and production environments
- Consistent interface across different storage types
"""

import os
import logging
import hashlib
import secrets
import base64
from datetime import datetime, date
from typing import Dict, List, Any, Optional, Tuple, Union

from shared.azure_services import is_local_dev
from shared.data_access import TableStorageRepository, SqlDatabaseRepository
from shared.database_models import UserAccount, UserLogin, HashingAlgorithm, Role, UserRole

# Configure module-level logger
logger = logging.getLogger(__name__)

# Global repository instances (lazy initialized)
_user_account_table_repo = None
_user_login_table_repo = None
_hashing_algorithm_table_repo = None
_user_role_table_repo = None

_user_account_sql_repo = None
_user_login_sql_repo = None
_hashing_algorithm_sql_repo = None
_user_role_sql_repo = None


# Repository initialization functions
def get_user_account_table_repo():
    """Lazy initialize the user account table repository"""
    global _user_account_table_repo
    if _user_account_table_repo is None:
        try:
            _user_account_table_repo = TableStorageRepository(table_name="UserAccount")
            logger.info("Initialized user account table repository")
        except Exception as e:
            logger.error(f"Failed to initialize user account table repository: {str(e)}")
            _user_account_table_repo = None
    return _user_account_table_repo


def get_user_login_table_repo():
    """Lazy initialize the user login table repository"""
    global _user_login_table_repo
    if _user_login_table_repo is None:
        try:
            _user_login_table_repo = TableStorageRepository(table_name="UserLogin")
            logger.info("Initialized user login table repository")
        except Exception as e:
            logger.error(f"Failed to initialize user login table repository: {str(e)}")
            _user_login_table_repo = None
    return _user_login_table_repo


def get_hashing_algorithm_table_repo():
    """Lazy initialize the hashing algorithm table repository"""
    global _hashing_algorithm_table_repo
    if _hashing_algorithm_table_repo is None:
        try:
            _hashing_algorithm_table_repo = TableStorageRepository(table_name="HashingAlgorithm")
            logger.info("Initialized hashing algorithm table repository")

            # Ensure default algorithms exist
            ensure_default_hashing_algorithms()
        except Exception as e:
            logger.error(f"Failed to initialize hashing algorithm table repository: {str(e)}")
            _hashing_algorithm_table_repo = None
    return _hashing_algorithm_table_repo


def get_user_role_table_repo():
    """Lazy initialize the user role table repository"""
    global _user_role_table_repo
    if _user_role_table_repo is None:
        try:
            _user_role_table_repo = TableStorageRepository(table_name="UserRole")
            logger.info("Initialized user role table repository")
        except Exception as e:
            logger.error(f"Failed to initialize user role table repository: {str(e)}")
            _user_role_table_repo = None
    return _user_role_table_repo


def get_user_account_sql_repo():
    """Lazy initialize the user account SQL repository"""
    global _user_account_sql_repo
    if _user_account_sql_repo is None and not is_local_dev():
        try:
            _user_account_sql_repo = SqlDatabaseRepository(table_name="User_Account")
            logger.info("Initialized user account SQL repository")
        except Exception as e:
            logger.error(f"Failed to initialize user account SQL repository: {str(e)}")
            _user_account_sql_repo = None
    return _user_account_sql_repo


def get_user_login_sql_repo():
    """Lazy initialize the user login SQL repository"""
    global _user_login_sql_repo
    if _user_login_sql_repo is None and not is_local_dev():
        try:
            _user_login_sql_repo = SqlDatabaseRepository(table_name="App_User_Login")
            logger.info("Initialized user login SQL repository")
        except Exception as e:
            logger.error(f"Failed to initialize user login SQL repository: {str(e)}")
            _user_login_sql_repo = None
    return _user_login_sql_repo


def get_hashing_algorithm_sql_repo():
    """Lazy initialize the hashing algorithm SQL repository"""
    global _hashing_algorithm_sql_repo
    if _hashing_algorithm_sql_repo is None and not is_local_dev():
        try:
            _hashing_algorithm_sql_repo = SqlDatabaseRepository(table_name="HashingAlgorithm")
            logger.info("Initialized hashing algorithm SQL repository")

            # Ensure default algorithms exist
            ensure_default_hashing_algorithms()
        except Exception as e:
            logger.error(f"Failed to initialize hashing algorithm SQL repository: {str(e)}")
            _hashing_algorithm_sql_repo = None
    return _hashing_algorithm_sql_repo


def get_user_role_sql_repo():
    """Lazy initialize the user role SQL repository"""
    global _user_role_sql_repo
    if _user_role_sql_repo is None and not is_local_dev():
        try:
            _user_role_sql_repo = SqlDatabaseRepository(table_name="App_User_Roles")
            logger.info("Initialized user role SQL repository")
        except Exception as e:
            logger.error(f"Failed to initialize user role SQL repository: {str(e)}")
            _user_role_sql_repo = None
    return _user_role_sql_repo


# Helper functions
def ensure_default_hashing_algorithms():
    """Ensure default hashing algorithms exist in the database"""
    try:
        # Default algorithms
        default_algorithms = [
            {"HashAlgorithmId": 1, "AlgorithmName": "SHA256"},
            {"HashAlgorithmId": 2, "AlgorithmName": "PBKDF2"},
            {"HashAlgorithmId": 3, "AlgorithmName": "Bcrypt"}
        ]

        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_hashing_algorithm_table_repo()
            if not repo:
                logger.error("Hashing algorithm table repository not available")
                return

            # Check if algorithms exist
            for algo in default_algorithms:
                algo_id = str(algo["HashAlgorithmId"])
                filter_query = f"RowKey eq '{algo_id}'"
                entities = repo.query_entities(filter_query)

                if not entities:
                    # Create algorithm entity
                    entity = {
                        "PartitionKey": "hashing_algorithm",
                        "RowKey": algo_id,
                        "AlgorithmName": algo["AlgorithmName"]
                    }
                    repo.insert_entity(entity)
                    logger.info(f"Created default hashing algorithm: {algo['AlgorithmName']}")
        else:
            # Use SQL Database for production
            repo = get_hashing_algorithm_sql_repo()
            if not repo:
                logger.error("Hashing algorithm SQL repository not available")
                return

            # Check if algorithms exist
            for algo in default_algorithms:
                query = "SELECT COUNT(*) FROM HashingAlgorithm WHERE HashAlgorithmId = ?"
                params = (algo["HashAlgorithmId"],)
                results = repo.execute_query(query, params)

                if not results or results[0][0] == 0:
                    # Create algorithm
                    query = "INSERT INTO HashingAlgorithm (HashAlgorithmId, AlgorithmName) VALUES (?, ?)"
                    params = (algo["HashAlgorithmId"], algo["AlgorithmName"])
                    repo.execute_non_query(query, params)
                    logger.info(f"Created default hashing algorithm: {algo['AlgorithmName']}")
    except Exception as e:
        logger.error(f"Error ensuring default hashing algorithms: {str(e)}")


def generate_salt() -> str:
    """Generate a random salt for password hashing"""
    return secrets.token_hex(16)


def hash_password(password: str, salt: str, algorithm_id: int = 1) -> str:
    """
    Hash a password using the specified algorithm

    Args:
        password: Password to hash
        salt: Salt to use for hashing
        algorithm_id: Hashing algorithm ID (1=SHA256, 2=PBKDF2, 3=Bcrypt)

    Returns:
        str: Hashed password
    """
    # Import hashlib here to ensure it's available in the function scope
    import hashlib

    if algorithm_id == 1:
        # SHA256
        return hashlib.sha256((password + salt).encode()).hexdigest()
    elif algorithm_id == 2:
        # PBKDF2 (more secure)
        # Import these modules here to ensure they're available in the function scope
        from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
        from cryptography.hazmat.primitives import hashes
        from cryptography.hazmat.backends import default_backend
        import base64

        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt.encode(),
            iterations=100000,
            backend=default_backend()
        )
        return base64.b64encode(kdf.derive(password.encode())).decode()
    elif algorithm_id == 3:
        # Bcrypt (most secure)
        import bcrypt
        return bcrypt.hashpw(password.encode(), salt.encode()).decode()
    else:
        # Default to SHA256
        return hashlib.sha256((password + salt).encode()).hexdigest()


def verify_password(password: str, hashed_password: str, salt: str, algorithm_id: int = 1) -> bool:
    """
    Verify a password against a hashed password

    Args:
        password: Password to verify
        hashed_password: Hashed password to compare against
        salt: Salt used for hashing
        algorithm_id: Hashing algorithm ID

    Returns:
        bool: True if password matches, False otherwise
    """
    return hash_password(password, salt, algorithm_id) == hashed_password


# User Account Repository functions
def create_user_account(
    email: str,
    first_name: str = "",
    middle_name: str = "",
    last_name: str = "",
    dob: Optional[Union[date, str]] = None,
    contact: Optional[str] = None,
    state: str = "",
    country: str = "",
    organization: str = ""
) -> Optional[int]:
    """
    Create a new user account

    Args:
        email: User email
        first_name: User first name
        middle_name: User middle name
        last_name: User last name
        dob: Date of birth (date object or ISO format string)
        contact: Contact information
        state: State
        country: Country
        organization: User organization

    Returns:
        int: User ID if successful, None otherwise
    """
    try:
        # Convert string date to date object if needed
        if isinstance(dob, str) and dob:
            try:
                dob = date.fromisoformat(dob)
            except ValueError:
                logger.warning(f"Invalid date format for DoB: {dob}, setting to None")
                dob = None

        # Create user account object
        user_account = UserAccount(
            Email=email,
            FirstName=first_name,
            MiddleName=middle_name,
            LastName=last_name,
            DoB=dob,
            Contact=contact,
            State=state,
            Country=country,
            Organization=organization
        )

        # Store original email for logging
        original_email = email

        # Normalize email to lowercase for case-insensitive comparison
        normalized_email = email.lower() if email else ""

        if is_local_dev():
            # Use Azure Table Storage for local development
            user_repo = get_user_account_table_repo()
            if not user_repo:
                logger.error("User account table repository not available")
                return None

            # Check if user already exists (case-insensitive)
            entities = user_repo.query_entities("PartitionKey eq 'user_account'")

            # Find matching entity with case-insensitive comparison
            matching_entities = [e for e in entities if e.get("RowKey", "").lower() == normalized_email]

            if not matching_entities:
                # Try one more approach - get all entities and filter
                all_entities = user_repo.query_entities("")
                matching_entities = [e for e in all_entities if e.get("Email", "").lower() == normalized_email]

            if matching_entities:
                logger.warning(f"User account already exists: {original_email}")
                return None

            # Generate a user ID (for local development)
            import random
            user_id = random.randint(1000, 9999)
            user_account.UserId = user_id

            # Insert user account
            entity = user_account.to_table_entity()
            entity["UserId"] = user_id

            success = user_repo.insert_entity(entity)
            if not success:
                logger.error(f"Failed to insert user account: {email}")
                return None

            logger.info(f"Created user account: {email} with ID {user_id}")
            return user_id
        else:
            # Use SQL Database for production
            user_repo = get_user_account_sql_repo()
            if not user_repo:
                logger.error("User account SQL repository not available")
                return None

            # Check if user already exists (case-insensitive)
            query = "SELECT UserId FROM User_Account WHERE LOWER(Email) = ?"
            params = (normalized_email,)
            results = user_repo.execute_query(query, params)

            if results:
                logger.warning(f"User account already exists: {original_email}")
                return results[0][0]  # Return existing user ID

            # Insert user account
            query = """
            INSERT INTO User_Account (FirstName, MiddleName, LastName, DoB, Email, Contact, State, Country, Organization)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?);
            SELECT SCOPE_IDENTITY();
            """
            params = (
                user_account.FirstName,
                user_account.MiddleName,
                user_account.LastName,
                user_account.DoB,
                user_account.Email,
                user_account.Contact,
                user_account.State,
                user_account.Country,
                user_account.Organization
            )

            # Execute query and get inserted ID
            results = user_repo.execute_query(query, params)
            if not results:
                logger.error(f"Failed to insert user account: {email}")
                return None

            user_id = int(results[0][0])
            logger.info(f"Created user account: {email} with ID {user_id}")
            return user_id
    except Exception as e:
        logger.error(f"Error creating user account: {str(e)}")
        return None


def get_user_account_by_email(email: str) -> Optional[UserAccount]:
    """
    Get a user account by email

    Args:
        email: User email

    Returns:
        UserAccount: User account object or None if not found
    """
    try:
        # Store original email for logging
        original_email = email

        # Normalize email to lowercase for case-insensitive comparison
        normalized_email = email.lower() if email else ""

        if is_local_dev():
            # Use Azure Table Storage for local development
            user_repo = get_user_account_table_repo()
            if not user_repo:
                logger.error("User account table repository not available")
                return None

            # Get all user entities and filter case-insensitively
            entities = user_repo.query_entities("PartitionKey eq 'user_account'")

            # Find matching entity with case-insensitive comparison
            matching_entities = [e for e in entities if e.get("RowKey", "").lower() == normalized_email]

            if not matching_entities:
                # Try one more approach - get all entities and filter
                all_entities = user_repo.query_entities("")
                matching_entities = [e for e in all_entities if e.get("Email", "").lower() == normalized_email]

                if not matching_entities:
                    logger.warning(f"User account not found: {original_email}")
                    return None

            entity = matching_entities[0]

            # Convert DoB string to date object if present
            dob = None
            if entity.get("DoB"):
                try:
                    dob = date.fromisoformat(entity["DoB"])
                except ValueError:
                    logger.warning(f"Invalid date format for DoB: {entity['DoB']}")

            # Create user account object
            user_account = UserAccount(
                UserId=entity.get("UserId"),
                FirstName=entity.get("FirstName", ""),
                MiddleName=entity.get("MiddleName", ""),
                LastName=entity.get("LastName", ""),
                DoB=dob,
                Email=entity.get("Email", ""),
                Contact=entity.get("Contact", ""),
                State=entity.get("State", ""),
                Country=entity.get("Country", "")
            )

            return user_account
        else:
            # Use SQL Database for production
            user_repo = get_user_account_sql_repo()
            if not user_repo:
                logger.error("User account SQL repository not available")
                return None

            # Query by email with case-insensitive comparison
            query = "SELECT * FROM User_Account WHERE LOWER(Email) = ?"
            params = (normalized_email,)

            results = user_repo.execute_query(query, params)

            if not results:
                logger.warning(f"User account not found: {original_email}")
                return None

            # Convert SQL result to UserAccount object
            result = results[0]

            # Create user account object
            user_account = UserAccount(
                UserId=result[0],  # UserId
                FirstName=result[1],  # FirstName
                MiddleName=result[2],  # MiddleName
                LastName=result[3],  # LastName
                DoB=result[4],  # DoB
                Email=result[5],  # Email
                Contact=result[6],  # Contact
                State=result[7],  # State
                Country=result[8]  # Country
            )

            return user_account
    except Exception as e:
        logger.error(f"Error getting user account: {str(e)}")
        return None


def get_user_account_by_id(user_id: int) -> Optional[UserAccount]:
    """
    Get a user account by ID

    Args:
        user_id: User ID

    Returns:
        UserAccount: User account object or None if not found
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            user_repo = get_user_account_table_repo()
            if not user_repo:
                logger.error("User account table repository not available")
                return None

            # Query all entities (not efficient, but works for local dev)
            entities = user_repo.query_entities("")

            # Find entity with matching user ID
            matching_entities = [e for e in entities if e.get("UserId") == user_id]

            if not matching_entities:
                logger.warning(f"User account not found with ID: {user_id}")
                return None

            entity = matching_entities[0]

            # Convert DoB string to date object if present
            dob = None
            if entity.get("DoB"):
                try:
                    dob = date.fromisoformat(entity["DoB"])
                except ValueError:
                    logger.warning(f"Invalid date format for DoB: {entity['DoB']}")

            # Create user account object
            user_account = UserAccount(
                UserId=entity.get("UserId"),
                FirstName=entity.get("FirstName", ""),
                MiddleName=entity.get("MiddleName", ""),
                LastName=entity.get("LastName", ""),
                DoB=dob,
                Email=entity.get("Email", ""),
                Contact=entity.get("Contact", ""),
                State=entity.get("State", ""),
                Country=entity.get("Country", "")
            )

            return user_account
        else:
            # Use SQL Database for production
            user_repo = get_user_account_sql_repo()
            if not user_repo:
                logger.error("User account SQL repository not available")
                return None

            # Query by user ID
            query = "SELECT * FROM User_Account WHERE UserId = ?"
            params = (user_id,)

            results = user_repo.execute_query(query, params)

            if not results:
                logger.warning(f"User account not found with ID: {user_id}")
                return None

            # Convert SQL result to UserAccount object
            result = results[0]

            # Create user account object
            user_account = UserAccount(
                UserId=result[0],  # UserId
                FirstName=result[1],  # FirstName
                MiddleName=result[2],  # MiddleName
                LastName=result[3],  # LastName
                DoB=result[4],  # DoB
                Email=result[5],  # Email
                Contact=result[6],  # Contact
                State=result[7],  # State
                Country=result[8]  # Country
            )

            return user_account
    except Exception as e:
        logger.error(f"Error getting user account by ID: {str(e)}")
        return None


# User Login Repository functions
def create_user_login(
    user_id: int,
    username: str,
    password: str,
    algorithm_id: int = 1
) -> bool:
    """
    Create a new user login

    Args:
        user_id: User ID (foreign key to User_Account)
        username: Username (typically email)
        password: Plain text password (will be hashed)
        algorithm_id: Hashing algorithm ID

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Generate salt and hash password
        salt = generate_salt()
        hashed_password = hash_password(password, salt, algorithm_id)

        # Store the original username for display purposes
        original_username = username

        # Normalize username to lowercase for case-insensitive comparison
        normalized_username = username.lower() if username else ""

        # Create user login object with the original username
        # This preserves the case for display but ensures lookups are case-insensitive
        user_login = UserLogin(
            UserId=user_id,
            Username=original_username,
            PasswordHash=hashed_password,
            PasswordSalt=salt,
            HashAlgorithmId=algorithm_id
        )

        if is_local_dev():
            # Use Azure Table Storage for local development
            login_repo = get_user_login_table_repo()
            if not login_repo:
                logger.error("User login table repository not available")
                return False

            # Check if login already exists (case-insensitive)
            entities = login_repo.query_entities("PartitionKey eq 'user_login'")

            # Find matching entity with case-insensitive comparison
            matching_entities = [e for e in entities if e.get("RowKey", "").lower() == normalized_username]

            if not matching_entities:
                # Try one more approach - get all entities and filter
                all_entities = login_repo.query_entities("")
                matching_entities = [e for e in all_entities if e.get("Username", "").lower() == normalized_username]

            if matching_entities:
                logger.warning(f"User login already exists: {original_username}")
                return False

            # Insert user login
            entity = user_login.to_table_entity()

            success = login_repo.insert_entity(entity)
            if not success:
                logger.error(f"Failed to insert user login: {original_username}")
                return False

            logger.info(f"Created user login for user ID {user_id}: {original_username}")
            return True
        else:
            # Use SQL Database for production
            login_repo = get_user_login_sql_repo()
            if not login_repo:
                logger.error("User login SQL repository not available")
                return False

            # Check if login already exists (case-insensitive)
            query = "SELECT COUNT(*) FROM App_User_Login WHERE UserId = ? OR LOWER(Username) = ?"
            params = (user_id, normalized_username)
            results = login_repo.execute_query(query, params)

            if results and results[0][0] > 0:
                logger.warning(f"User login already exists for user ID {user_id} or username {original_username}")
                return False

            # Insert user login
            query = """
            INSERT INTO App_User_Login (UserId, Username, PasswordHash, PasswordSalt, HashAlgorithmId)
            VALUES (?, ?, ?, ?, ?)
            """
            params = (
                user_login.UserId,
                user_login.Username,  # Store original username for display
                user_login.PasswordHash,
                user_login.PasswordSalt,
                user_login.HashAlgorithmId
            )

            success = login_repo.execute_non_query(query, params)
            if not success:
                logger.error(f"Failed to insert user login: {original_username}")
                return False

            logger.info(f"Created user login for user ID {user_id}: {original_username}")
            return True
    except Exception as e:
        logger.error(f"Error creating user login: {str(e)}")
        return False


def get_user_login(username: str) -> Optional[UserLogin]:
    """
    Get a user login by username

    Args:
        username: Username (typically email)

    Returns:
        UserLogin: User login object or None if not found
    """
    try:
        # Normalize username to lowercase for case-insensitive comparison
        normalized_username = username.lower() if username else ""

        if is_local_dev():
            # Use Azure Table Storage for local development
            login_repo = get_user_login_table_repo()
            if not login_repo:
                logger.error("User login table repository not available")
                return None

            # Query all users and filter case-insensitively
            # Since Azure Table Storage doesn't support case-insensitive queries directly
            entities = login_repo.query_entities("PartitionKey eq 'user_login'")

            # Find matching entity with case-insensitive comparison
            matching_entities = [e for e in entities if e.get("RowKey", "").lower() == normalized_username]

            if not matching_entities:
                # Try one more approach - get all entities and filter
                all_entities = login_repo.query_entities("")
                matching_entities = [e for e in all_entities if e.get("Username", "").lower() == normalized_username]

                if not matching_entities:
                    logger.warning(f"User login not found: {username}")
                    return None

            entity = matching_entities[0]

            # Create user login object
            user_login = UserLogin(
                UserId=entity.get("UserId"),
                Username=entity.get("Username", ""),
                PasswordHash=entity.get("PasswordHash", ""),
                PasswordSalt=entity.get("PasswordSalt", ""),
                HashAlgorithmId=entity.get("HashAlgorithmId", 1)
            )

            return user_login
        else:
            # Use SQL Database for production
            login_repo = get_user_login_sql_repo()
            if not login_repo:
                logger.error("User login SQL repository not available")
                return None

            # Query by username with case-insensitive comparison using LOWER function
            query = "SELECT * FROM App_User_Login WHERE LOWER(Username) = ?"
            params = (normalized_username,)

            results = login_repo.execute_query(query, params)

            if not results:
                logger.warning(f"User login not found: {username}")
                return None

            # Convert SQL result to UserLogin object
            result = results[0]

            # Create user login object
            user_login = UserLogin(
                UserId=result[0],  # UserId
                Username=result[1],  # Username
                PasswordHash=result[2],  # PasswordHash
                PasswordSalt=result[3],  # PasswordSalt
                HashAlgorithmId=result[4]  # HashAlgorithmId
            )

            return user_login
    except Exception as e:
        logger.error(f"Error getting user login: {str(e)}")
        return None


def authenticate_user(username: str, password: str) -> Optional[int]:
    """
    Authenticate a user with username and password

    Args:
        username: Username (typically email)
        password: Plain text password

    Returns:
        int: User ID if authentication successful, None otherwise
    """
    try:
        # Store original username for logging
        original_username = username

        # Get user login (get_user_login already handles case-insensitive lookup)
        user_login = get_user_login(username)
        if not user_login:
            logger.warning(f"User login not found for authentication: {original_username}")
            return None

        # Verify password
        if not verify_password(
            password,
            user_login.PasswordHash,
            user_login.PasswordSalt,
            user_login.HashAlgorithmId
        ):
            logger.warning(f"Invalid password for user: {original_username}")
            return None

        logger.info(f"User authenticated successfully: {original_username}")
        return user_login.UserId
    except Exception as e:
        logger.error(f"Error authenticating user: {str(e)}")
        return None


# Combined user creation function
def update_last_login(email: str) -> bool:
    """
    Update the last login timestamp for a user

    Args:
        email: User email

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Store original email for logging
        original_email = email

        # Normalize email to lowercase for case-insensitive comparison
        normalized_email = email.lower() if email else ""

        if is_local_dev():
            # Use Azure Table Storage for local development
            user_repo = get_user_account_table_repo()
            if not user_repo:
                logger.error("User account table repository not available")
                return False

            # Get all user entities and filter case-insensitively
            entities = user_repo.query_entities("PartitionKey eq 'user_account'")

            # Find matching entity with case-insensitive comparison
            matching_entities = [e for e in entities if e.get("RowKey", "").lower() == normalized_email]

            if not matching_entities:
                # Try one more approach - get all entities and filter
                all_entities = user_repo.query_entities("")
                matching_entities = [e for e in all_entities if e.get("Email", "").lower() == normalized_email]

                if not matching_entities:
                    logger.warning(f"User not found: {original_email}")
                    return False

            user_entity = matching_entities[0]

            # Update last login
            user_entity["LastLogin"] = datetime.now().isoformat()

            return user_repo.update_entity(user_entity)
        else:
            # Use SQL Database for production
            user_repo = get_user_account_sql_repo()
            if not user_repo:
                logger.error("User account SQL repository not available")
                return False

            # Update last login in SQL with case-insensitive comparison
            query = "UPDATE User_Account SET LastLogin = ? WHERE LOWER(Email) = ?"
            params = (datetime.now().isoformat(), normalized_email)

            return user_repo.execute_non_query(query, params)
    except Exception as e:
        logger.error(f"Error updating last login: {str(e)}")
        return False


def get_user_login_by_username(username: str) -> Optional[UserLogin]:
    """
    Get a user login by username (alias for get_user_login)
    This function performs a case-insensitive lookup.

    Args:
        username: Username (typically email)

    Returns:
        UserLogin: User login object or None if not found
    """
    # get_user_login already handles case-insensitive lookup
    return get_user_login(username)


def get_user_roles(email: str) -> List[Dict[str, Any]]:
    """
    Get roles for a user by email

    Args:
        email: User email

    Returns:
        List[Dict[str, Any]]: List of role objects with RoleId, Rolename, and Description
    """
    try:
        # First get the user account to get the user ID
        user = get_user_account_by_email(email)
        if not user:
            logger.warning(f"User not found for role lookup: {email}")
            return []

        user_id = user.UserId

        if is_local_dev():
            # Use Azure Table Storage for local development
            role_repo = get_user_role_table_repo()
            if not role_repo:
                logger.error("User role table repository not available")
                return []

            # Query all user roles
            filter_query = f"PartitionKey eq 'user_role' AND UserId eq '{user_id}'"
            entities = role_repo.query_entities(filter_query)

            if not entities:
                logger.info(f"No roles found for user: {email}")
                return []

            # Get role details for each role ID
            roles = []
            for entity in entities:
                role_id = entity.get("RoleId")
                if role_id:
                    # This is a simplified approach for local dev
                    # In a real app, you would query the Role table
                    role_name = ""
                    description = ""

                    # Map role IDs to names based on default roles
                    if role_id == 1:
                        role_name = "System Admin"
                        description = "Full access to all system features including user management"
                    elif role_id == 2:
                        role_name = "Account Admin"
                        description = "Can manage users and integrations within their account"
                    elif role_id == 3:
                        role_name = "Integration Manager"
                        description = "Can add and manage integrations"
                    elif role_id == 4:
                        role_name = "Viewer"
                        description = "Read-only access to integrations and their data"

                    roles.append({
                        "RoleId": role_id,
                        "Rolename": role_name,
                        "Description": description
                    })

            return roles
        else:
            # Use SQL Database for production
            user_repo = get_user_role_sql_repo()
            if not user_repo:
                logger.error("User role SQL repository not available")
                return []

            # Query roles for user
            query = """
            SELECT r.RoleId, r.Rolename, r.Description
            FROM App_User_Roles ur
            JOIN Role r ON ur.RoleId = r.RoleId
            WHERE ur.UserId = ?
            """
            params = (user_id,)

            results = user_repo.execute_query(query, params)
            if not results:
                logger.info(f"No roles found for user: {email}")
                return []

            # Convert SQL results to role objects
            roles = []
            for result in results:
                roles.append({
                    "RoleId": result[0],
                    "Rolename": result[1],
                    "Description": result[2]
                })

            return roles
    except Exception as e:
        logger.error(f"Error getting user roles: {str(e)}")
        return []


def is_user_admin(email: str) -> bool:
    """
    Check if a user is an admin (System Admin or Account Admin)

    Args:
        email: User email

    Returns:
        bool: True if user is an admin, False otherwise
    """
    try:
        # Get user roles
        roles = get_user_roles(email)

        # Check if user has admin role
        for role in roles:
            role_name = role.get("Rolename", "").lower()
            if "admin" in role_name:
                return True

        return False
    except Exception as e:
        logger.error(f"Error checking if user is admin: {str(e)}")
        return False


def create_user(
    email: str,
    password: str,
    first_name: str = "",
    middle_name: str = "",
    last_name: str = "",
    dob: Optional[Union[date, str]] = None,
    contact: Optional[str] = None,
    state: str = "",
    country: str = "",
    organization: str = "",
    algorithm_id: int = 1
) -> Optional[int]:
    """
    Create a new user (account and login)

    Args:
        email: User email
        password: Plain text password
        first_name: User first name
        middle_name: User middle name
        last_name: User last name
        dob: Date of birth
        contact: Contact information
        state: State
        country: Country
        organization: User organization
        algorithm_id: Hashing algorithm ID

    Returns:
        int: User ID if successful, None otherwise
    """
    try:
        # Create user account
        user_id = create_user_account(
            email=email,
            first_name=first_name,
            middle_name=middle_name,
            last_name=last_name,
            dob=dob,
            contact=contact,
            state=state,
            country=country,
            organization=organization
        )

        if not user_id:
            logger.error(f"Failed to create user account: {email}")
            return None

        # Create user login
        success = create_user_login(
            user_id=user_id,
            username=email,  # Use email as username
            password=password,
            algorithm_id=algorithm_id
        )

        if not success:
            logger.error(f"Failed to create user login: {email}")
            # TODO: Consider rolling back user account creation
            return None

        logger.info(f"Created user (account and login): {email} with ID {user_id}")
        return user_id
    except Exception as e:
        logger.error(f"Error creating user: {str(e)}")
        return None
