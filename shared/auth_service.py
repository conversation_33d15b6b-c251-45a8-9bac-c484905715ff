import logging
import requests
import time # For JWT 'exp' claim
from datetime import datetime, timedelta, timezone # For JWT 'exp' claim
from typing import Dict, Any, Optional, Tuple

from simple_salesforce import Salesforce, SalesforceAuthenticationFailed
import jwt # PyJWT library for creating the assertion

logger = logging.getLogger(__name__)

def authenticate_salesforce_integration(
    auth_flow_type: str,
    tenant_url: str, 
    environment: Optional[str],
    client_id: str,
    client_secret: Optional[str] = None,
    jwt_username: Optional[str] = None,
    private_key: Optional[str] = None # This should be the raw private key string
) -> Tuple[bool, Optional[str], Optional[Salesforce], Optional[Dict[str, Any]]]:
    """
    Centralized function to authenticate with Salesforce using either JWT or Client Credentials.

    Args:
        auth_flow_type (str): 'jwt' or 'client_credentials'.
        tenant_url (str): The Salesforce MyDomain URL (e.g., https://mydomain.my.salesforce.com) 
                          or a generic login URL for Client Credentials token endpoint.
        environment (str): 'production', 'sandbox', or similar. Used for JWT domain.
        client_id (str): Salesforce connected app client ID.
        client_secret (str, optional): Salesforce connected app client secret. Required for CC.
        jwt_username (str, optional): Salesforce username for JWT flow. Required for JWT.
        private_key (str, optional): RSA private key string for JWT flow. Required for JWT.

    Returns:
        Tuple[bool, Optional[str], Optional[Salesforce], Optional[Dict[str, Any]]]:
            - success (bool): True if authentication was successful.
            - error_message (str | None): Error message if authentication failed.
            - sf_instance (Salesforce | None): Authenticated simple-salesforce instance.
            - connection_details (dict | None): Dict with 'access_token' and 'instance_url' (FQDN).
    """
    sf = None
    connection_details_dict = {}
    auth_success = False
    error_message_str = "Authentication failed."
    auth_step_log = [] 

    processed_tenant_url = tenant_url.rstrip('/')
    if not processed_tenant_url.startswith('https://') and not processed_tenant_url.startswith('http://'):
        processed_tenant_url = f"https://{processed_tenant_url}"

    if auth_flow_type == "jwt":
        auth_step_log.append(f"Attempting JWT. User: {jwt_username}, ClientID: {client_id[:5]}..., Env: {environment}")
        if not all([jwt_username, client_id, private_key]):
            error_message_str = "Missing username, client_id, or private_key for JWT flow."
            logger.error(f"[AuthService] {error_message_str}")
            return False, error_message_str, None, None

        try:
            jwt_audience = "https://login.salesforce.com"
            if environment and "sandbox" in environment.lower():
                jwt_audience = "https://test.salesforce.com"
            
            logger.info(f"[AuthService] Using audience for JWT: {jwt_audience} (derived from env: {environment})")
            auth_step_log.append(f"JWT audience: {jwt_audience}")

            # Create JWT assertion manually
            claim = {
                'iss': client_id,
                'sub': jwt_username,
                'aud': jwt_audience,
                'exp': datetime.now(tz=timezone.utc) + timedelta(minutes=3) # Typically 3-5 minutes expiry
            }
            assertion = jwt.encode(claim, private_key, algorithm='RS256')
            auth_step_log.append("JWT assertion created.")

            token_url = f"{jwt_audience}/services/oauth2/token"
            payload = {
                'grant_type': 'urn:ietf:params:oauth:grant-type:jwt-bearer',
                'assertion': assertion
            }
            auth_step_log.append(f"JWT: Posting to token URL: {token_url}")

            response = requests.post(token_url, data=payload, timeout=20)
            response.raise_for_status()
            token_data = response.json()

            if 'access_token' in token_data and 'instance_url' in token_data:
                sf_instance_url_from_token = token_data['instance_url'].rstrip('/')
                sf_session_id = token_data['access_token']
                sf = Salesforce(instance_url=sf_instance_url_from_token, session_id=sf_session_id)
                auth_success = True
                connection_details_dict["access_token"] = sf.session_id
                connection_details_dict["instance_url"] = sf.base_url # Correct FQDN from simple-salesforce
                auth_step_log.append(f"JWT Auth via manual token exchange successful. Instance: {sf.base_url}")
            else:
                error_message_str = f"JWT token exchange failed: 'access_token' or 'instance_url' not in response. Response: {token_data}"
                auth_step_log.append(f"JWT Token Error: {error_message_str}")

        except requests.exceptions.RequestException as req_err:
            error_message_str = f"JWT token request error: {str(req_err)}. Response: {req_err.response.text if req_err.response else 'No response'}"
            auth_step_log.append(f"JWT Request Error: {str(req_err)}")
        except SalesforceAuthenticationFailed as e_auth: # Should not happen if instantiating with session_id
            error_message_str = f"JWT Authentication Failed with simple-salesforce (post-token): {str(e_auth)}"
            auth_step_log.append(f"JWT Simple-SF Init Error: {str(e_auth)}")
        except Exception as e_jwt:
            error_message_str = f"Unexpected error during JWT auth: {str(e_jwt)}"
            auth_step_log.append(f"JWT Unexpected Error: {str(e_jwt)}")
        finally:
            logger.info(f"[AuthService] JWT Auth steps: {'; '.join(auth_step_log)}")
            if not auth_success: logger.error(f"[AuthService] Final JWT Error: {error_message_str}")

    elif auth_flow_type == "client_credentials":
        auth_step_log.append(f"Attempting Client Credentials. ClientID: {client_id[:5]}..., Token Base: {processed_tenant_url}")
        if not all([client_id, client_secret, processed_tenant_url]):
            error_message_str = "Missing client_id, client_secret, or tenant_url for Client Credentials flow."
            logger.error(f"[AuthService] {error_message_str}")
            return False, error_message_str, None, None

        token_url = f"{processed_tenant_url}/services/oauth2/token"
        payload = {
            'grant_type': 'client_credentials',
            'client_id': client_id,
            'client_secret': client_secret
        }
        auth_step_log.append(f"Client Credentials: Posting to token URL: {token_url}")
        try:
            response = requests.post(token_url, data=payload, timeout=20)
            response.raise_for_status() 
            token_data = response.json()
            
            if 'access_token' in token_data and 'instance_url' in token_data:
                sf_instance_url_from_token = token_data['instance_url'].rstrip('/')
                sf_session_id = token_data['access_token']
                sf = Salesforce(instance_url=sf_instance_url_from_token, session_id=sf_session_id)
                auth_success = True
                connection_details_dict["access_token"] = sf.session_id
                connection_details_dict["instance_url"] = sf.base_url
                auth_step_log.append(f"Client Credentials token exchange successful. Instance: {sf.base_url}")
            else:
                error_message_str = f"Client Credentials token exchange failed: 'access_token' or 'instance_url' not in response. Response: {token_data}"
                auth_step_log.append(f"Client Credentials Token Error: {error_message_str}")
        except requests.exceptions.RequestException as req_err:
            error_message_str = f"Client Credentials token request error: {str(req_err)}. Response: {req_err.response.text if req_err.response else 'No response'}"
            auth_step_log.append(f"Client Credentials Request Error: {str(req_err)}")
        except Exception as e_cc:
            error_message_str = f"Unexpected error during Client Credentials flow: {str(e_cc)}"
            auth_step_log.append(f"Client Credentials Unexpected Error: {str(e_cc)}")
        finally:
            logger.info(f"[AuthService] Client Credentials Auth steps: {'; '.join(auth_step_log)}")
            if not auth_success: logger.error(f"[AuthService] Final CC Error: {error_message_str}")
            
    else:
        error_message_str = f"Unsupported auth_flow_type: {auth_flow_type}"
        logger.error(f"[AuthService] {error_message_str}")

    if auth_success and sf:
        return True, None, sf, connection_details_dict
    else:
        return False, error_message_str, None, None 