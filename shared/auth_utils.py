"""
Authentication Utilities Module

This module provides utilities for authentication and authorization.

Best practices implemented:
- JWT-based authentication
- Proper error handling and logging
- Reusable authentication functions
"""

import logging
import jwt
from typing import Dict, Any, Optional
import azure.functions as func
from fastapi import Request

# Import shared modules
from shared.config import get_jwt_config
from shared.user_repository import get_user_account_by_email

# Configure module-level logger
logger = logging.getLogger(__name__)

# JWT configuration
_jwt_config = None

def get_jwt_secret():
    """Get JWT secret from configuration"""
    global _jwt_config
    if _jwt_config is None:
        _jwt_config = get_jwt_config()
    return _jwt_config["secret"]

def get_jwt_algorithm():
    """Get JWT algorithm from configuration"""
    global _jwt_config
    if _jwt_config is None:
        _jwt_config = get_jwt_config()
    return _jwt_config["algorithm"]

def decode_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Decode a JWT token

    Args:
        token: JWT token to decode

    Returns:
        Dict: Decoded token payload or None if invalid
    """
    try:
        payload = jwt.decode(token, get_jwt_secret(), algorithms=[get_jwt_algorithm()])
        return payload
    except jwt.ExpiredSignatureError:
        logger.warning("Token expired")
        return None
    except jwt.InvalidTokenError as e:
        logger.warning(f"Invalid token: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Error decoding token: {str(e)}")
        return None

def get_token_from_header(req: func.HttpRequest) -> Optional[str]:
    """
    Extract token from Authorization header

    Args:
        req: HTTP request

    Returns:
        str: Token or None if not found
    """
    auth_header = req.headers.get("Authorization")
    if not auth_header:
        return None

    parts = auth_header.split()
    if len(parts) != 2 or parts[0].lower() != "bearer":
        return None

    return parts[1]

def get_current_user(req: Request) -> Optional[Dict[str, Any]]:
    """
    Get current user from request

    Args:
        req: HTTP request

    Returns:
        Dict: User information or None if not authenticated
    """
    # Check if we're in production - return None to indicate auth should be handled elsewhere
    from shared.config import is_local_dev

    if not is_local_dev():
        logger.info("Production environment: get_current_user disabled - use platform authentication")
        return None

    # Development only: Process JWT tokens
    logger.info("Development environment: Processing JWT token authentication")

    # Get token from header
    token = get_token_from_header(req)
    logger.info(f"[AUTH DEBUG] Token from header: {token}")
    if not token:
        logger.warning("No token found in request")
        return None

    # Decode token
    payload = decode_token(token)
    logger.info(f"[AUTH DEBUG] Decoded payload: {payload}")
    if not payload:
        logger.warning("Invalid token")
        return None

    # Extract user information
    email = payload.get("sub")
    logger.info(f"[AUTH DEBUG] Email from token: {email}")
    if not email:
        logger.warning("No email found in token")
        return None

    # Fetch user from DB to get user ID
    user_account = get_user_account_by_email(email)
    logger.info(f"[AUTH DEBUG] User account from DB: {user_account}")
    if not user_account:
        logger.warning("No user found in DB for email")
        return None

    # Return user information with ID
    user_info = {
        "email": email,
        "id": user_account.UserId  # UserId is the unique user identifier
    }
    logger.info(f"[AUTH DEBUG] Returning user info: {user_info}")
    return user_info

def require_auth(original_func):
    """
    Decorator to require authentication

    Args:
        original_func: Function to decorate

    Returns:
        Function: Decorated function
    """
    def wrapper(req):
        # Check if we're in production - disable authentication checks in production
        # In production, frontend uses platform authentication and shouldn't call SFDC endpoints
        from shared.config import is_local_dev

        if not is_local_dev():
            logger.warning(f'SFDC endpoint {original_func.__name__} called in production - this should not happen')
            logger.warning('Frontend should use platform authentication and DB service instead')
            # Import here to avoid circular imports
            import azure.functions as func_module
            import json
            return func_module.HttpResponse(
                json.dumps({
                    "success": False,
                    "statusCode": 503,
                    "error": "SFDC service endpoints are disabled in production. Use platform authentication and DB service."
                }),
                mimetype="application/json",
                status_code=503
            )

        # Development only: Check authentication
        logger.info(f'Development environment: Checking authentication for {original_func.__name__}')
        current_user = get_current_user(req)
        if not current_user:
            logger.warning(f'Authentication failed for {original_func.__name__}')
            # Import here to avoid circular imports
            import azure.functions as func_module
            import json
            return func_module.HttpResponse(
                json.dumps({"success": False, "statusCode": 401, "error": "Unauthorized"}),
                mimetype="application/json",
                status_code=401
            )

        # Add user to request
        setattr(req, "user", current_user)
        logger.info(f'Authentication successful for {original_func.__name__}, user: {current_user.get("email", "unknown")}')

        # Call original function
        return original_func(req)

    # Copy function attributes to wrapper
    import functools
    functools.update_wrapper(wrapper, original_func)

    return wrapper
