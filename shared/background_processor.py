"""
Background Processor Module

This module provides functionality for processing long-running tasks in the background.
It uses Azure Functions Durable Entities to manage task state and Azure Queue Storage
for task execution.
"""

import json
import logging
import azure.functions as func
from azure.storage.queue import QueueClient, QueueMessage, BinaryBase64EncodePolicy
from azure.core.exceptions import ResourceExistsError
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timedelta
import uuid

from shared.config import get_storage_connection_string
from shared.common import is_local_dev
from shared.data_access import TableStorageRepository, SqlDatabaseRepository
from shared.data_access import get_policy_table_repo, get_rule_table_repo

# Configure module-level logger
logger = logging.getLogger(__name__)

# Task status constants
TASK_STATUS_PENDING = "pending"
TASK_STATUS_RUNNING = "running"
TASK_STATUS_COMPLETED = "completed"
TASK_STATUS_FAILED = "failed"
TASK_STATUS_RETRY = "retry"
TASK_STATUS_CANCELLED = "cancelled"

# Task types
TASK_TYPE_OVERVIEW = "overview"
TASK_TYPE_HEALTH_CHECK = "health_check"
TASK_TYPE_PROFILES = "profiles"
TASK_TYPE_PROFILES_PERMISSION_SETS = "profiles_permission_sets"
TASK_TYPE_PERMISSION_SETS = "permission_sets"
# TASK_TYPE_GUEST_USER_RISKS = "guest_user_risks" // Removed Guest User Risks
# TASK_TYPE_PMD_ISSUES = "pmd_issues" # Removed PMD Issues
TASK_TYPE_DATA_EXPORT = "data_export"
TASK_TYPE_REPORT_GENERATION = "report_generation"
TASK_TYPE_SCHEDULED_SCAN = "scheduled_scan"
TASK_TYPE_NOTIFICATION = "notification"
TASK_TYPE_METADATA_EXTRACTION = "metadata_extraction"
TASK_TYPE_SFDC_AUTHENTICATE = "sfdc_authenticate"
TASK_TYPE_PMD_APEX_SECURITY = "pmd_apex_security"

# Task priorities
TASK_PRIORITY_HIGH = "high"
TASK_PRIORITY_MEDIUM = "medium"
TASK_PRIORITY_LOW = "low"

# Queue names for different priorities
QUEUE_PRIORITY_HIGH = "task-queue-high"
QUEUE_PRIORITY_MEDIUM = "task-queue-medium"
QUEUE_PRIORITY_LOW = "task-queue-low"

# Task type to priority mapping
TASK_TYPE_PRIORITY_MAP = {
    # High priority tasks - critical for security monitoring
    TASK_TYPE_HEALTH_CHECK: TASK_PRIORITY_HIGH,
    # TASK_TYPE_GUEST_USER_RISKS: TASK_PRIORITY_HIGH, // Removed Guest User Risks
    TASK_TYPE_NOTIFICATION: TASK_PRIORITY_HIGH,
    TASK_TYPE_METADATA_EXTRACTION: TASK_PRIORITY_HIGH,
    TASK_TYPE_SFDC_AUTHENTICATE: TASK_PRIORITY_HIGH,

    # Medium priority tasks - important but not as time-critical
    TASK_TYPE_PROFILES: TASK_PRIORITY_MEDIUM,
    # TASK_TYPE_PMD_ISSUES: TASK_PRIORITY_MEDIUM, # Removed PMD Issues
    TASK_TYPE_PMD_APEX_SECURITY: TASK_PRIORITY_MEDIUM, # Added PMD Apex Security
    TASK_TYPE_OVERVIEW: TASK_PRIORITY_MEDIUM,
    TASK_TYPE_SCHEDULED_SCAN: TASK_PRIORITY_MEDIUM,

    # Low priority tasks - useful but can be processed when resources are available
    TASK_TYPE_DATA_EXPORT: TASK_PRIORITY_LOW,
    TASK_TYPE_REPORT_GENERATION: TASK_PRIORITY_LOW
}

# Add import for policy rule setting helper
default_policy_rule_setting_table_name = "PolicyRuleSetting"

class BackgroundProcessor:
    """Background processor for handling long-running tasks"""

    def __init__(self):
        """Initialize the background processor"""
        try:
            # Get storage connection string
            self.connection_string = get_storage_connection_string()
            if not self.connection_string:
                logger.error("Failed to get storage connection string")
            else:
                logger.info(f"Successfully got storage connection string with length: {len(self.connection_string)}")
        except Exception as e:
            logger.error(f"Error getting storage connection string: {str(e)}")
            import traceback
            logger.error(f"Connection string error traceback: {traceback.format_exc()}")
            self.connection_string = None

        self.default_queue_name = "task-queue"
        self.priority_queue_map = {
            TASK_PRIORITY_HIGH: QUEUE_PRIORITY_HIGH,
            TASK_PRIORITY_MEDIUM: QUEUE_PRIORITY_MEDIUM,
            TASK_PRIORITY_LOW: QUEUE_PRIORITY_LOW
        }
        self.task_status_table_name = "TaskStatus"
        self.max_retries = 3  # Maximum number of retries for failed tasks

        # Initialize repositories
        self._task_status_repo = None
        self._execution_log_repo = None

        # Initialize queues
        self._queue_clients = {}

        # Log initialization
        logger.info("BackgroundProcessor initialized")

    def get_task_status_repo(self):
        """Get the task status repository"""
        if self._task_status_repo is None:
            try:
                self._task_status_repo = TableStorageRepository(table_name=self.task_status_table_name)
                logger.info("Initialized task status repository")
            except Exception as e:
                logger.error(f"Failed to initialize task status repository: {str(e)}")
                self._task_status_repo = None
        return self._task_status_repo

    def get_execution_log_repo(self):
        """Get the execution log repository"""
        if self._execution_log_repo is None and not is_local_dev():
            try:
                self._execution_log_repo = SqlDatabaseRepository(table_name="App_ExecutionLog")
                logger.info("Initialized execution log repository")
            except Exception as e:
                logger.error(f"Failed to initialize execution log repository: {str(e)}")
                self._execution_log_repo = None
        return self._execution_log_repo

    def get_queue_client(self, queue_name: str) -> QueueClient:
        """
        Get a queue client for the specified queue name

        Args:
            queue_name: Name of the queue

        Returns:
            QueueClient: Queue client
        """
        if queue_name not in self._queue_clients:
            try:
                logger.info(f"Creating queue client for queue: {queue_name}")

                # Check if connection string is available
                if not self.connection_string:
                    logger.error("Storage connection string is not available")
                    raise ValueError("Storage connection string is not available")

                # Log connection string availability (without revealing the actual string)
                logger.info(f"Storage connection string is available with length: {len(self.connection_string)}")

                # Use BinaryBase64EncodePolicy to handle binary data
                queue_client = QueueClient.from_connection_string(
                    conn_str=self.connection_string,
                    queue_name=queue_name,
                    message_encode_policy=BinaryBase64EncodePolicy()
                )
                logger.info(f"Queue client created for queue: {queue_name}")

                # Create the queue if it doesn't exist
                try:
                    logger.info(f"Attempting to create queue: {queue_name}")
                    queue_client.create_queue()
                    logger.info(f"Queue {queue_name} created successfully")
                except ResourceExistsError:
                    logger.info(f"Queue {queue_name} already exists")
                except Exception as create_error:
                    logger.error(f"Error creating queue {queue_name}: {str(create_error)}")
                    import traceback
                    logger.error(f"Queue creation error traceback: {traceback.format_exc()}")
                    # Continue anyway, as the queue might still be accessible

                # Verify the queue exists by getting properties
                try:
                    properties = queue_client.get_queue_properties()
                    logger.info(f"Queue {queue_name} properties retrieved: {properties.approximate_message_count} messages")
                except Exception as prop_error:
                    logger.error(f"Error getting queue properties for {queue_name}: {str(prop_error)}")
                    import traceback
                    logger.error(f"Queue properties error traceback: {traceback.format_exc()}")
                    # Continue anyway, as we'll catch any issues when sending messages

                # Store the queue client in the cache
                self._queue_clients[queue_name] = queue_client
                logger.info(f"Queue client for {queue_name} stored in cache")

                # Test the queue client with a simple operation
                try:
                    # List the messages without removing them (peek)
                    messages = list(queue_client.peek_messages(max_messages=1))
                    logger.info(f"Queue {queue_name} peek test successful. Found {len(messages)} messages.")
                except Exception as peek_error:
                    logger.warning(f"Queue {queue_name} peek test failed: {str(peek_error)}")
                    # Continue anyway, as we'll catch any issues when sending messages

            except Exception as e:
                logger.error(f"Error initializing queue client for {queue_name}: {str(e)}")
                import traceback
                logger.error(f"Queue client initialization error traceback: {traceback.format_exc()}")

                # Return a dummy client that logs errors instead of failing
                from unittest.mock import MagicMock
                mock_client = MagicMock()
                # Use a lambda that ignores arguments but logs the error
                mock_client.send_message.side_effect = lambda *_args, **_kwargs: logger.error(f"Mock queue client for {queue_name} cannot send messages")
                self._queue_clients[queue_name] = mock_client
                logger.info(f"Created mock queue client for {queue_name}")

        return self._queue_clients[queue_name]

    def _is_task_enabled_for_user_policy(self, task_type: str, user_id: str, org_id: str) -> bool:
        """
        Returns True if the task should be enqueued for this user/org, based on Policy/Rule tables.
        Always returns True for sfdc_authenticate and metadata_extraction.
        """
        if task_type in [TASK_TYPE_SFDC_AUTHENTICATE, TASK_TYPE_METADATA_EXTRACTION, TASK_TYPE_PMD_APEX_SECURITY]:
            return True
        try:
            policy_repo = get_policy_table_repo()
            rule_repo = get_rule_table_repo()
            # Find all policies for this integration
            policies = policy_repo.query_entities(f"IntegrationId eq '{org_id}'")
            for policy in policies:
                policy_id = policy.get("PolicyId")
                if not policy_id:
                    continue
                # Build OData filter string for Rule table
                filter_query = f"PolicyId eq '{policy_id}' and TaskType eq '{task_type}' and Enabled eq 1"
                rules = rule_repo.query_entities(filter_query)
                if rules:
                    return True
            return False
        except Exception as e:
            logger.error(f"Error checking if task {task_type} is enabled for user {user_id}, org {org_id}: {e}")
            return False

    def enqueue_task(self, task_type: str, org_id: str, user_id: str,
                     params: Optional[Dict[str, Any]] = None,
                     priority: Optional[str] = None,
                     scheduled_time: Optional[datetime] = None,
                     force: bool = False) -> Optional[str]:
        """
        Enqueue a task for background processing, with global idempotency.

        Args:
            task_type: Type of task (overview, health_check, profiles, etc.)
            org_id: Organization ID
            user_id: User ID
            params: Additional parameters for the task
            priority: Task priority (high, medium, low). If None, priority will be determined based on task type.
            scheduled_time: Time to schedule the task for execution
            force: If True, bypass idempotency check and always enqueue

        Returns:
            str: Task ID if successful, None otherwise
        """
        # Policy check: Only enqueue if enabled for user/org, except for always-on tasks
        if not self._is_task_enabled_for_user_policy(task_type, user_id, org_id):
            logger.info(f"Skipping enqueue for {task_type} for user {user_id} and org {org_id}: not enabled in Policy/Rule tables.")
            return None

        # Global idempotency check
        if not force:
            latest_task = self.get_latest_task_for_org(org_id, task_type)
            if latest_task and latest_task["status"] in (TASK_STATUS_PENDING, TASK_STATUS_RUNNING):
                logger.info(f"[Idempotency] Skipping enqueue for {task_type} ({org_id}): existing task {latest_task['task_id']} is {latest_task['status']}")
                return latest_task["task_id"]

        try:
            logger.info(f"Starting to enqueue task of type {task_type} for org {org_id}")

            # Validate required parameters
            if not task_type:
                logger.error("Task type is required")
                return None

            if not org_id:
                logger.error("Organization ID is required")
                return None

            if not user_id:
                logger.error("User ID is required")
                return None

            # Determine priority based on task type if not explicitly provided
            if priority is None:
                # Use the task type to priority mapping, defaulting to medium if not found
                priority = TASK_TYPE_PRIORITY_MAP.get(task_type, TASK_PRIORITY_MEDIUM)
                logger.info(f"Auto-assigned priority '{priority}' to task type '{task_type}'")

            # Generate a task ID using UUID for better uniqueness
            task_id = f"{task_type}-{org_id}-{uuid.uuid4().hex[:8]}"
            logger.info(f"Generated task ID: {task_id}")

            # Generate a unique ExecutionLogId (UUID) for linking
            execution_log_id = str(uuid.uuid4())
            logger.info(f"Generated fallback ExecutionLogId: {execution_log_id}")

            # Get current time
            current_time = datetime.now()

            # Create task data
            task_data = {
                "task_id": task_id,
                "task_type": task_type,
                "org_id": org_id,
                "user_id": user_id,
                "params": params or {},
                "created_at": current_time.isoformat(),
                "status": TASK_STATUS_PENDING,
                "priority": priority,
                "retry_count": 0,
                "scheduled_time": scheduled_time.isoformat() if scheduled_time else None,
                "execution_log_id": execution_log_id  # Always present
            }
            logger.info(f"Created task data for task {task_id}")

            # Create task status entity
            task_status_repo = self.get_task_status_repo()
            if task_status_repo:
                logger.info(f"Got task status repository: {task_status_repo}")
                task_status_entity = {
                    "PartitionKey": "task",
                    "RowKey": task_id,
                    "TaskType": task_type,
                    "OrgId": org_id,
                    "UserId": user_id,
                    "Params": json.dumps(params or {}),
                    "CreatedAt": current_time.isoformat(),
                    "Status": TASK_STATUS_PENDING,
                    "Progress": 0,
                    "Message": "Task queued",
                    "Result": "",
                    "CompletedAt": "",
                    "ExecutionLogId": execution_log_id,  # Always set
                    "Priority": priority,
                    "RetryCount": 0,
                    "ScheduledTime": scheduled_time.isoformat() if scheduled_time else ""
                }

                try:
                    success = task_status_repo.insert_entity(task_status_entity)
                    if success:
                        logger.info(f"Successfully created task status entity for task {task_id}")
                    else:
                        logger.error(f"Failed to create task status entity for task {task_id}")
                except Exception as insert_error:
                    logger.error(f"Error inserting task status entity: {str(insert_error)}")
                    import traceback
                    logger.error(f"Insert error traceback: {traceback.format_exc()}")
            else:
                logger.warning("Task status repository not available, continuing without creating task status entity")

            # Create execution log entry in production (optional, for SQL DB)
            db_execution_log_id = None
            if not is_local_dev():
                execution_log_repo = self.get_execution_log_repo()
                if execution_log_repo:
                    logger.info(f"Got execution log repository: {execution_log_repo}")
                    # Map task type to execution type
                    execution_type_map = {
                        TASK_TYPE_OVERVIEW: "Overview",
                        TASK_TYPE_HEALTH_CHECK: "HealthCheck",
                        TASK_TYPE_PROFILES: "ProfilePermissions",
                        TASK_TYPE_DATA_EXPORT: "DataExport",
                        TASK_TYPE_REPORT_GENERATION: "ReportGeneration",
                        TASK_TYPE_SCHEDULED_SCAN: "ScheduledScan",
                        TASK_TYPE_NOTIFICATION: "Notification",
                        TASK_TYPE_METADATA_EXTRACTION: "MetadataExtraction",
                        TASK_TYPE_SFDC_AUTHENTICATE: "SalesforceAuthentication",
                        TASK_TYPE_PMD_APEX_SECURITY: "PMDApexSecurity"
                    }

                    execution_type = execution_type_map.get(task_type, task_type)
                    logger.info(f"Mapped task type {task_type} to execution type {execution_type}")

                    # Insert execution log
                    query = """
                    INSERT INTO App_ExecutionLog (OrgId, ExecutionType, Status, StartTime, ExecutedBy, Priority)
                    VALUES (?, ?, ?, ?, ?, ?)
                    """
                    query_params = (
                        org_id,
                        execution_type,
                        TASK_STATUS_PENDING,
                        current_time.isoformat(),
                        user_id,
                        priority
                    )

                    try:
                        success = execution_log_repo.execute_non_query(query, query_params)
                        if success:
                            logger.info(f"Successfully inserted execution log for task {task_id}")

                            # Get the ID of the inserted execution log
                            query = """
                            SELECT TOP 1 Id FROM App_ExecutionLog
                            WHERE OrgId = ? AND ExecutionType = ? AND ExecutedBy = ?
                            ORDER BY StartTime DESC
                            """
                            results = execution_log_repo.execute_query(query, (org_id, execution_type, user_id))

                            if results and len(results) > 0:
                                db_execution_log_id = str(results[0][0])
                                logger.info(f"Got DB execution log ID: {db_execution_log_id}")

                                # Update task status entity with DB execution log ID
                                if task_status_repo:
                                    try:
                                        task_status_repo.update_entity({
                                            "PartitionKey": "task",
                                            "RowKey": task_id,
                                            "ExecutionLogId": db_execution_log_id
                                        })
                                        logger.info(f"Updated task status entity with DB execution log ID: {db_execution_log_id}")
                                        # Also update in task_data for downstream consumers
                                        task_data["execution_log_id"] = db_execution_log_id
                                    except Exception as update_error:
                                        logger.error(f"Error updating task status entity with DB execution log ID: {str(update_error)}")
                        else:
                            logger.error(f"Failed to insert execution log for task {task_id}")
                    except Exception as exec_log_error:
                        logger.error(f"Error inserting execution log: {str(exec_log_error)}")
                        import traceback
                        logger.error(f"Execution log error traceback: {traceback.format_exc()}")

            # Add task to queue
            logger.info(f"Added execution log ID to task data: {task_data['execution_log_id']}")

            # Determine which queue to use based on priority
            queue_name = self.priority_queue_map.get(priority, self.default_queue_name)
            logger.info(f"Using queue {queue_name} for priority {priority}")

            try:
                queue_client = self.get_queue_client(queue_name)
                logger.info(f"Got queue client for queue {queue_name}: {queue_client}")
            except Exception as queue_client_error:
                logger.error(f"Error getting queue client for queue {queue_name}: {str(queue_client_error)}")
                import traceback
                logger.error(f"Queue client error traceback: {traceback.format_exc()}")

                # Return task ID anyway so the API can return a success response
                # The task will be marked as failed in the database
                if task_status_repo:
                    try:
                        task_status_repo.update_entity({
                            "PartitionKey": "task",
                            "RowKey": task_id,
                            "Status": TASK_STATUS_FAILED,
                            "Message": f"Failed to get queue client: {str(queue_client_error)}"
                        })
                        logger.info(f"Updated task status entity to failed due to queue client error")
                    except Exception as update_error:
                        logger.error(f"Error updating task status entity: {str(update_error)}")

                return task_id

            # Calculate visibility timeout if scheduled time is provided
            visibility_timeout = None
            if scheduled_time and scheduled_time > current_time:
                # Calculate seconds until scheduled time
                visibility_timeout = int((scheduled_time - current_time).total_seconds())
                logger.info(f"Calculated visibility timeout: {visibility_timeout} seconds")

            # Add the message to the queue
            # Use consistent JSON encoding for queue messages
            message_content = json.dumps(task_data)
            logger.info(f"Preparing to enqueue task {task_id} to queue {queue_name} with priority {priority}")

            # With BinaryBase64EncodePolicy, we can send the message as bytes
            # and it will be properly encoded
            try:
                # Log queue client type for debugging
                logger.info(f"Queue client type: {type(queue_client)}")

                if visibility_timeout:
                    logger.info(f"Sending message with visibility timeout: {visibility_timeout}")
                    queue_client.send_message(
                        message_content.encode('utf-8'),  # Send as bytes
                        visibility_timeout=visibility_timeout
                    )
                    logger.info(f"Scheduled task {task_id} for execution at {scheduled_time.isoformat() if scheduled_time else 'now'}")
                else:
                    logger.info(f"Sending message without visibility timeout")
                    queue_client.send_message(message_content.encode('utf-8'))  # Send as bytes
                    logger.info(f"Enqueued task {task_id} with priority {priority}")

                # Log the message format for debugging
                logger.debug(f"Enqueued message format: bytes with BinaryBase64EncodePolicy, content length: {len(message_content)}")

                # Verify the message was sent by checking the queue
                try:
                    properties = queue_client.get_queue_properties()
                    logger.info(f"Queue {queue_name} now has {properties.approximate_message_count} messages")
                except Exception as prop_error:
                    logger.warning(f"Could not verify message count after enqueueing: {str(prop_error)}")

                # Successfully enqueued the task
                return task_id

            except Exception as queue_error:
                logger.error(f"Error sending message to queue {queue_name}: {str(queue_error)}")
                # Log more details about the error
                import traceback
                logger.error(f"Queue error traceback: {traceback.format_exc()}")

                # Try to create a fallback task status entry to indicate the issue
                try:
                    if task_status_repo:
                        task_status_repo.update_entity({
                            "PartitionKey": "task",
                            "RowKey": task_id,
                            "Status": TASK_STATUS_FAILED,
                            "Message": f"Failed to enqueue task: {str(queue_error)}"
                        })
                        logger.info(f"Created fallback task status entry for failed task {task_id}")
                except Exception as fallback_error:
                    logger.error(f"Error creating fallback task status: {str(fallback_error)}")

                # Don't re-raise, return the task ID anyway so the API can return a success response
                # The task will be marked as failed in the database
                return task_id

        except Exception as e:
            logger.error(f"Error enqueuing task: {str(e)}")
            import traceback
            logger.error(f"Enqueue task error traceback: {traceback.format_exc()}")
            return None

    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the status of a task

        Args:
            task_id: Task ID

        Returns:
            Dict[str, Any]: Task status if found, None otherwise
        """
        try:
            task_status_repo = self.get_task_status_repo()
            if not task_status_repo:
                logger.error("Task status repository not available")
                return None

            # Get task status entity
            task_status_entity = task_status_repo.get_entity("task", task_id)

            if not task_status_entity:
                logger.warning(f"No task status found for task {task_id}")
                return None

            # Convert entity to dictionary
            task_status = {
                "task_id": task_status_entity.get("RowKey"),
                "task_type": task_status_entity.get("TaskType"),
                "org_id": task_status_entity.get("OrgId"),
                "user_id": task_status_entity.get("UserId"),
                "params": json.loads(task_status_entity.get("Params", "{}")),
                "created_at": task_status_entity.get("CreatedAt"),
                "status": task_status_entity.get("Status"),
                "progress": task_status_entity.get("Progress"),
                "message": task_status_entity.get("Message"),
                "result": task_status_entity.get("Result"),
                "completed_at": task_status_entity.get("CompletedAt"),
                "execution_log_id": task_status_entity.get("ExecutionLogId")
            }

            return task_status
        except Exception as e:
            logger.error(f"Error getting task status: {str(e)}")
            return None

    def update_task_status(self, task_id: str, status: str, progress: int = None,
                         message: str = None, result: str = None, error: str = None) -> bool:
        """
        Update the status of a task

        Args:
            task_id: Task ID
            status: New status
            progress: Progress percentage (0-100)
            message: Status message
            result: Task result
            error: Error message if task failed

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            task_status_repo = self.get_task_status_repo()
            if not task_status_repo:
                logger.error("Task status repository not available")
                return False

            # Get task status entity
            task_status_entity = task_status_repo.get_entity("task", task_id)

            if not task_status_entity:
                logger.warning(f"No task status found for task {task_id}")
                return False

            # Update entity
            update_entity = {
                "PartitionKey": "task",
                "RowKey": task_id,
                "Status": status
            }

            if progress is not None:
                update_entity["Progress"] = progress

            if message is not None:
                update_entity["Message"] = message

            if result is not None:
                update_entity["Result"] = result

            if error is not None:
                update_entity["Error"] = error

            # Handle task completion or failure
            if status == TASK_STATUS_FAILED:
                # Check if we should retry the task
                retry_count = task_status_entity.get("RetryCount", 0)
                if retry_count < self.max_retries:
                    # Increment retry count
                    retry_count += 1
                    update_entity["RetryCount"] = retry_count
                    update_entity["Status"] = TASK_STATUS_RETRY
                    update_entity["Message"] = f"Retrying task (attempt {retry_count} of {self.max_retries})"

                    # Re-enqueue the task with a delay
                    self._retry_task(task_status_entity, retry_count)
                else:
                    # Max retries reached, mark as failed
                    update_entity["CompletedAt"] = datetime.now().isoformat()
                    update_entity["Message"] = f"Task failed after {retry_count} retries: {message or error or 'Unknown error'}"

                    # Update execution log
                    self._update_execution_log(task_status_entity, "Failed")

            elif status == TASK_STATUS_COMPLETED:
                update_entity["CompletedAt"] = datetime.now().isoformat()

                # Update execution log
                self._update_execution_log(task_status_entity, "Completed")

            elif status == TASK_STATUS_CANCELLED:
                update_entity["CompletedAt"] = datetime.now().isoformat()

                # Update execution log
                self._update_execution_log(task_status_entity, "Cancelled")

            # Update the task status entity
            task_status_repo.update_entity(update_entity)
            logger.info(f"Updated task status for task {task_id} to {status}")

            return True
        except Exception as e:
            logger.error(f"Error updating task status: {str(e)}")
            return False

    def _update_execution_log(self, task_status_entity: Dict[str, Any], status: str) -> None:
        """
        Update the execution log for a task

        Args:
            task_status_entity: Task status entity
            status: New status
        """
        execution_log_id = task_status_entity.get("ExecutionLogId")
        if execution_log_id and not is_local_dev():
            execution_log_repo = self.get_execution_log_repo()
            if execution_log_repo:
                # Update execution log
                query = """
                UPDATE App_ExecutionLog
                SET Status = ?, EndTime = ?
                WHERE Id = ?
                """
                params = (
                    status,
                    datetime.now().isoformat(),
                    execution_log_id
                )

                execution_log_repo.execute_non_query(query, params)

    def _retry_task(self, task_status_entity: Dict[str, Any], retry_count: int) -> None:
        """
        Retry a failed task

        Args:
            task_status_entity: Task status entity
            retry_count: Current retry count
        """
        try:
            # Extract task data
            task_id = task_status_entity.get("RowKey")
            task_type = task_status_entity.get("TaskType")
            org_id = task_status_entity.get("OrgId")
            user_id = task_status_entity.get("UserId")
            params = json.loads(task_status_entity.get("Params", "{}"))
            priority = task_status_entity.get("Priority", TASK_PRIORITY_MEDIUM)
            execution_log_id = task_status_entity.get("ExecutionLogId")

            # Create task data for re-enqueueing
            task_data = {
                "task_id": task_id,
                "task_type": task_type,
                "org_id": org_id,
                "user_id": user_id,
                "params": params,
                "created_at": datetime.now().isoformat(),
                "status": TASK_STATUS_PENDING,
                "priority": priority,
                "retry_count": retry_count,
                "execution_log_id": execution_log_id
            }

            # Calculate delay based on retry count (exponential backoff)
            delay_seconds = min(30, 2 ** retry_count)  # Max delay of 30 seconds

            # Determine which queue to use based on priority
            queue_name = self.priority_queue_map.get(priority, self.default_queue_name)
            queue_client = self.get_queue_client(queue_name)

            # Add the message to the queue with a visibility timeout
            # Use consistent JSON encoding for queue messages
            message_content = json.dumps(task_data)

            # With BinaryBase64EncodePolicy, we can send the message as bytes
            # and it will be properly encoded
            try:
                queue_client.send_message(
                    message_content.encode('utf-8'),  # Send as bytes
                    visibility_timeout=delay_seconds
                )
                logger.info(f"Re-enqueued task {task_id} for retry (attempt {retry_count} of {self.max_retries}) with delay of {delay_seconds} seconds")

                # Log the message format for debugging
                logger.debug(f"Enqueued retry message format: bytes with BinaryBase64EncodePolicy, content: {message_content}")
            except Exception as queue_error:
                logger.error(f"Error sending retry message to queue: {str(queue_error)}")
                raise  # Re-raise the exception to be caught by the outer try-except
        except Exception as e:
            logger.error(f"Error retrying task: {str(e)}")

    def get_latest_task_for_org(self, org_id: str, task_type: str) -> Optional[Dict[str, Any]]:
        """
        Get the latest task for an organization and task type

        Args:
            org_id: Organization ID
            task_type: Task type

        Returns:
            Dict[str, Any]: Task status if found, None otherwise
        """
        try:
            task_status_repo = self.get_task_status_repo()
            if not task_status_repo:
                logger.error("Task status repository not available")
                return None

            # Query for the latest task
            filter_query = f"PartitionKey eq 'task' and OrgId eq '{org_id}' and TaskType eq '{task_type}'"
            entities = task_status_repo.query_entities(filter_query)

            if not entities:
                logger.warning(f"No tasks found for organization {org_id} and type {task_type}")
                return None

            # Sort by created_at in descending order
            entities.sort(key=lambda e: e.get("CreatedAt", ""), reverse=True)

            # Convert entity to dictionary
            task_status = {
                "task_id": entities[0].get("RowKey"),
                "task_type": entities[0].get("TaskType"),
                "org_id": entities[0].get("OrgId"),
                "user_id": entities[0].get("UserId"),
                "params": json.loads(entities[0].get("Params", "{}")),
                "created_at": entities[0].get("CreatedAt"),
                "status": entities[0].get("Status"),
                "progress": entities[0].get("Progress"),
                "message": entities[0].get("Message"),
                "result": entities[0].get("Result"),
                "completed_at": entities[0].get("CompletedAt"),
                "execution_log_id": entities[0].get("ExecutionLogId")
            }

            return task_status
        except Exception as e:
            logger.error(f"Error getting latest task: {str(e)}")
            return None

    def get_pending_tasks(self) -> List[Dict[str, Any]]:
        """
        Get all pending tasks

        Returns:
            List[Dict[str, Any]]: List of pending tasks
        """
        try:
            task_status_repo = self.get_task_status_repo()
            if not task_status_repo:
                logger.error("Task status repository not available")
                return []

            # Query for pending tasks
            filter_query = f"PartitionKey eq 'task' and Status eq '{TASK_STATUS_PENDING}'"
            entities = task_status_repo.query_entities(filter_query)

            if not entities:
                logger.info("No pending tasks found")
                return []

            # Convert entities to dictionaries
            pending_tasks = []
            for entity in entities:
                task_status = {
                    "task_id": entity.get("RowKey"),
                    "task_type": entity.get("TaskType"),
                    "org_id": entity.get("OrgId"),
                    "user_id": entity.get("UserId"),
                    "params": json.loads(entity.get("Params", "{}")),
                    "created_at": entity.get("CreatedAt"),
                    "status": entity.get("Status"),
                    "progress": entity.get("Progress"),
                    "message": entity.get("Message"),
                    "result": entity.get("Result"),
                    "completed_at": entity.get("CompletedAt"),
                    "execution_log_id": entity.get("ExecutionLogId")
                }

                pending_tasks.append(task_status)

            return pending_tasks
        except Exception as e:
            logger.error(f"Error getting pending tasks: {str(e)}")
            return []

    def schedule_task(self, task_type: str, org_id: str, user_id: str,
                      scheduled_time: datetime,
                      params: Optional[Dict[str, Any]] = None,
                      priority: Optional[str] = None) -> Optional[str]:
        """
        Schedule a task for future execution

        Args:
            task_type: Type of task
            org_id: Organization ID
            user_id: User ID
            scheduled_time: Time to schedule the task for execution
            params: Additional parameters for the task
            priority: Task priority. If None, priority will be determined based on task type.

        Returns:
            str: Task ID if successful, None otherwise
        """
        # Validate scheduled time
        if scheduled_time <= datetime.now():
            logger.warning("Scheduled time must be in the future")
            return None

        # Enqueue the task with a scheduled time
        return self.enqueue_task(
            task_type=task_type,
            org_id=org_id,
            user_id=user_id,
            params=params,
            priority=priority,
            scheduled_time=scheduled_time
        )

    def schedule_recurring_task(self, task_type: str, org_id: str, user_id: str,
                               schedule_type: str,
                               params: Optional[Dict[str, Any]] = None,
                               priority: Optional[str] = None) -> Optional[str]:
        """
        Schedule a recurring task

        Args:
            task_type: Type of task
            org_id: Organization ID
            user_id: User ID
            schedule_type: Type of schedule (daily, weekly, monthly)
            params: Additional parameters for the task
            priority: Task priority. If None, priority will be determined based on task type.

        Returns:
            str: Task ID if successful, None otherwise
        """
        # Calculate the next scheduled time based on schedule type
        now = datetime.now()

        if schedule_type == "daily":
            # Schedule for tomorrow at the same time
            scheduled_time = now.replace(day=now.day+1, hour=3, minute=0, second=0, microsecond=0)
        elif schedule_type == "weekly":
            # Schedule for next week on the same day
            scheduled_time = now + timedelta(days=7)
            scheduled_time = scheduled_time.replace(hour=3, minute=0, second=0, microsecond=0)
        elif schedule_type == "monthly":
            # Schedule for next month on the same day
            if now.month == 12:
                scheduled_time = now.replace(year=now.year+1, month=1, day=1, hour=3, minute=0, second=0, microsecond=0)
            else:
                scheduled_time = now.replace(month=now.month+1, day=1, hour=3, minute=0, second=0, microsecond=0)
        else:
            logger.warning(f"Unknown schedule type: {schedule_type}")
            return None

        # Add schedule information to params
        task_params = params or {}
        task_params["schedule_type"] = schedule_type
        task_params["is_recurring"] = True

        # Enqueue the task with a scheduled time
        return self.enqueue_task(
            task_type=task_type,
            org_id=org_id,
            user_id=user_id,
            params=task_params,
            priority=priority,
            scheduled_time=scheduled_time
        )

    def cancel_task(self, task_id: str) -> bool:
        """
        Cancel a pending or running task

        Args:
            task_id: Task ID

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            task_status_repo = self.get_task_status_repo()
            if not task_status_repo:
                logger.error("Task status repository not available")
                return False

            # Get task status entity
            task_status_entity = task_status_repo.get_entity("task", task_id)

            if not task_status_entity:
                logger.warning(f"No task status found for task {task_id}")
                return False

            # Check if the task can be cancelled
            status = task_status_entity.get("Status")
            if status not in [TASK_STATUS_PENDING, TASK_STATUS_RUNNING, TASK_STATUS_RETRY]:
                logger.warning(f"Cannot cancel task {task_id} with status {status}")
                return False

            # Update task status to cancelled
            return self.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_CANCELLED,
                message="Task cancelled by user"
            )
        except Exception as e:
            logger.error(f"Error cancelling task: {str(e)}")
            return False

    def get_tasks_by_org(self, org_id: str, include_completed: bool = False) -> List[Dict[str, Any]]:
        """
        Get all tasks for an organization

        Args:
            org_id: Organization ID
            include_completed: Whether to include completed tasks

        Returns:
            List[Dict[str, Any]]: List of tasks
        """
        try:
            task_status_repo = self.get_task_status_repo()
            if not task_status_repo:
                logger.error("Task status repository not available")
                return []

            # Query for tasks
            filter_query = f"PartitionKey eq 'task' and OrgId eq '{org_id}'"
            if not include_completed:
                filter_query += f" and Status ne '{TASK_STATUS_COMPLETED}' and Status ne '{TASK_STATUS_FAILED}' and Status ne '{TASK_STATUS_CANCELLED}'"

            entities = task_status_repo.query_entities(filter_query)

            if not entities:
                logger.info(f"No tasks found for organization {org_id}")
                return []

            # Convert entities to dictionaries
            tasks = []
            for entity in entities:
                task = {
                    "task_id": entity.get("RowKey"),
                    "task_type": entity.get("TaskType"),
                    "org_id": entity.get("OrgId"),
                    "user_id": entity.get("UserId"),
                    "params": json.loads(entity.get("Params", "{}")),
                    "created_at": entity.get("CreatedAt"),
                    "status": entity.get("Status"),
                    "progress": entity.get("Progress"),
                    "message": entity.get("Message"),
                    "result": entity.get("Result"),
                    "completed_at": entity.get("CompletedAt"),
                    "execution_log_id": entity.get("ExecutionLogId"),
                    "priority": entity.get("Priority", TASK_PRIORITY_MEDIUM),
                    "retry_count": entity.get("RetryCount", 0),
                    "scheduled_time": entity.get("ScheduledTime", "")
                }

                tasks.append(task)

            # Sort by created_at in descending order
            tasks.sort(key=lambda t: t.get("created_at", ""), reverse=True)

            return tasks
        except Exception as e:
            logger.error(f"Error getting tasks by organization: {str(e)}")
            return []

    def clean_old_tasks(self, days: int = 7) -> bool:
        """
        Clean up old completed or failed tasks

        Args:
            days: Number of days to keep tasks

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            task_status_repo = self.get_task_status_repo()
            if not task_status_repo:
                logger.error("Task status repository not available")
                return False

            # Calculate cutoff date
            cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()

            # Query for old completed or failed tasks
            filter_query = f"PartitionKey eq 'task' and (Status eq '{TASK_STATUS_COMPLETED}' or Status eq '{TASK_STATUS_FAILED}' or Status eq '{TASK_STATUS_CANCELLED}') and CompletedAt lt '{cutoff_date}'"
            entities = task_status_repo.query_entities(filter_query)

            if not entities:
                logger.info(f"No old tasks found to clean up")
                return True

            # Delete old tasks
            for entity in entities:
                task_status_repo.delete_entity("task", entity.get("RowKey"))

            logger.info(f"Cleaned up {len(entities)} old tasks")

            return True
        except Exception as e:
            logger.error(f"Error cleaning old tasks: {str(e)}")
            return False