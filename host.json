{"version": "2.0", "logging": {"applicationInsights": {"samplingSettings": {"isEnabled": true, "excludedTypes": "Request"}}, "logLevel": {"default": "Information", "Worker.Rpc": "Warning", "Function": "Information", "Host.Triggers.Queue": "Warning", "Azure.Storage": "Warning", "Azure.Core": "Warning"}}, "extensionBundle": {"id": "Microsoft.Azure.Functions.ExtensionBundle", "version": "[4.*, 5.0.0)"}, "functionTimeout": "00:10:00", "concurrency": {"dynamicConcurrencyEnabled": false, "snapshotPersistenceEnabled": false}, "extensions": {"http": {"routePrefix": "api", "maxOutstandingRequests": 200, "maxConcurrentRequests": 100, "dynamicThrottlesEnabled": true}, "queues": {"maxPollingInterval": "00:00:30", "visibilityTimeout": "00:05:00", "batchSize": 16, "maxDequeueCount": 3, "newBatchThreshold": 8}, "blobs": {"maxDegreeOfParallelism": 4}}}