#!/usr/bin/env python
"""
Deployment Verification Script

This script runs tests to verify that functions are available after deployment.
It checks:
1. General function availability by making HTTP requests to key endpoints
2. Proxy function availability
3. Blueprint function availability

Usage:
    python verify_deployment.py [--url URL]

Options:
    --url URL    The base URL of the deployed function app
                 Default: https://func-atomsec-sfdc-dev.azurewebsites.net
"""

import os
import sys
import argparse
import subprocess

def main():
    """Run deployment verification tests"""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Verify function app deployment')
    parser.add_argument('--url', default='https://func-atomsec-sfdc-dev.azurewebsites.net',
                        help='The base URL of the deployed function app')
    args = parser.parse_args()

    # Set environment variables for the tests
    os.environ['RUN_DEPLOYMENT_TESTS'] = 'true'
    os.environ['FUNCTION_APP_URL'] = args.url

    # Set Python path to include the current directory
    current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if 'PYTHONPATH' in os.environ:
        os.environ['PYTHONPATH'] = f"{os.environ['PYTHONPATH']}:{current_dir}"
    else:
        os.environ['PYTHONPATH'] = current_dir

    print(f"Verifying deployment at {args.url}...")
    print(f"Using Python path: {os.environ.get('PYTHONPATH')}")

    # Run the tests
    test_command = [
        'python', '-m', 'pytest',
        'tests/test_deployment_verification.py',
        '-v',
        '--junitxml=deployment-verification-results.xml'  # Generate JUnit XML report
    ]

    try:
        # Run the tests and capture the output
        result = subprocess.run(test_command, check=True, capture_output=True, text=True)
        print(result.stdout)

        # Check if the test results file was generated
        results_file = os.path.join(os.getcwd(), 'deployment-verification-results.xml')
        if os.path.exists(results_file):
            print(f"\nTest results saved to: {results_file}")

        if result.returncode == 0:
            print("\n✅ Deployment verification successful!")
            return 0
        else:
            print("\n❌ Deployment verification failed!")
            return 1
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Error running tests: {e}")
        print(e.stdout)
        print(e.stderr)
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
