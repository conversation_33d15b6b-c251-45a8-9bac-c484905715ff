#!/usr/bin/env python
"""
Generate function.json files for all functions in the application

This script analyzes the function_app.py file and all blueprint files to identify
functions and generate corresponding function.json files. This helps the Azure Portal
discover the functions.
"""

import os
import json
import re
import sys

def ensure_directory(directory):
    """Ensure a directory exists"""
    if not os.path.exists(directory):
        os.makedirs(directory)

def generate_function_json(function_name, route, methods, output_dir, is_blueprint=False):
    """Generate a function.json file for a function"""
    # Create the function directory
    function_dir = os.path.join(output_dir, function_name)
    ensure_directory(function_dir)

    # Create the function.json file
    function_json = {
        "scriptFile": "../function_app.py",
        "bindings": [
            {
                "authLevel": "anonymous",
                "type": "httpTrigger",
                "direction": "in",
                "name": "req",
                "methods": methods if methods else ["get"],
                "route": route
            },
            {
                "type": "http",
                "direction": "out",
                "name": "$return"
            }
        ]
    }

    # Write the function.json file
    with open(os.path.join(function_dir, "function.json"), "w") as f:
        json.dump(function_json, f, indent=2)

    # Create the __init__.py file
    if is_blueprint:
        # For blueprint functions
        blueprint_name, func_name = function_name.split('_', 1)
        init_content = f'''import azure.functions as func
import logging
from function_app import {blueprint_name}_bp

# This file is required for Azure Functions to discover the function
# The actual implementation is in the blueprint file

def main(req: func.HttpRequest) -> func.HttpResponse:
    """Azure Functions HTTP trigger function

    This function is a wrapper for the blueprint function.
    """
    return {blueprint_name}_bp.{func_name}(req)
'''
    else:
        # For proxy functions
        init_content = f'''import azure.functions as func
import logging
from function_app import app

# This file is required for Azure Functions to discover the function
# The actual implementation is in function_app.py

def main(req: func.HttpRequest) -> func.HttpResponse:
    """Azure Functions HTTP trigger function

    This function is a wrapper for the proxy function.
    """
    return app.{function_name}(req)
'''

    with open(os.path.join(function_dir, "__init__.py"), "w") as f:
        f.write(init_content)

    print(f"Generated function.json and __init__.py for {function_name}")

def generate_queue_trigger_function_json(function_name, arg_name, queue_name, output_dir):
    """Generate a function.json file for a queue trigger function"""
    # Create the function directory
    function_dir = os.path.join(output_dir, function_name)
    ensure_directory(function_dir)

    # Create the function.json file
    function_json = {
        "scriptFile": "../function_app.py",
        "bindings": [
            {
                "type": "queueTrigger",
                "direction": "in",
                "name": arg_name,
                "queueName": queue_name,
                "connection": "AzureWebJobsStorage"
            }
        ]
    }

    # Write the function.json file
    with open(os.path.join(function_dir, "function.json"), "w") as f:
        json.dump(function_json, f, indent=2)

    # Create the __init__.py file
    init_content = f'''import azure.functions as func
import logging
from function_app import app

# This file is required for Azure Functions to discover the function
# The actual implementation is in function_app.py

def main(msg: func.QueueMessage) -> None:
    """Azure Functions Queue trigger function

    This function is a wrapper for the queue trigger function.
    """
    return app.{function_name}(msg)
'''

    with open(os.path.join(function_dir, "__init__.py"), "w") as f:
        f.write(init_content)

    print(f"Generated function.json and __init__.py for queue trigger function {function_name}")

def extract_proxy_functions(file_path):
    """Extract proxy functions from function_app.py"""
    functions = []

    with open(file_path, "r") as f:
        content = f.read()

    # Find all @app.route decorators
    route_pattern = r'@app\.route\(route="([^"]+)"(?:,\s*methods=(\[[^\]]+\]))?\)'
    function_pattern = r'def\s+([a-zA-Z0-9_]+)\s*\('

    # Find all matches
    for match in re.finditer(route_pattern, content):
        route = match.group(1)
        methods_str = match.group(2)

        # Find the function name
        function_pos = content.find("def", match.end())
        if function_pos > 0:
            function_match = re.search(function_pattern, content[function_pos:function_pos+100])
            if function_match:
                function_name = function_match.group(1)

                # Parse methods
                methods = None
                if methods_str:
                    methods = []
                    for method in methods_str.strip("[]").split(","):
                        method = method.strip().strip('"\'')
                        methods.append(method.lower())

                functions.append((function_name, route, methods))

    # Find all queue trigger functions
    queue_trigger_pattern = r'@app\.queue_trigger\(arg_name="([^"]+)",\s*queue_name="([^"]+)"'

    for match in re.finditer(queue_trigger_pattern, content):
        arg_name = match.group(1)
        queue_name = match.group(2)

        # Find the function name
        function_pos = content.find("def", match.end())
        if function_pos > 0:
            function_match = re.search(function_pattern, content[function_pos:function_pos+100])
            if function_match:
                function_name = function_match.group(1)

                # Generate function.json for queue trigger
                generate_queue_trigger_function_json(function_name, arg_name, queue_name, os.path.dirname(file_path))

    return functions

def extract_blueprint_functions(blueprint_dir):
    """Extract functions from blueprint files"""
    functions = []

    # Get all Python files in the blueprint directory
    for file_name in os.listdir(blueprint_dir):
        if file_name.endswith(".py") and not file_name.startswith("__"):
            file_path = os.path.join(blueprint_dir, file_name)

            with open(file_path, "r") as f:
                content = f.read()

            # Find the blueprint name
            bp_match = re.search(r'bp\s*=\s*func\.Blueprint\(\)', content)
            if not bp_match:
                continue

            # Find all @bp.route decorators
            route_pattern = r'@bp\.route\(route="([^"]+)"(?:,\s*methods=(\[[^\]]+\]))?\)'
            function_pattern = r'def\s+([a-zA-Z0-9_]+)\s*\('

            # Find all matches
            for match in re.finditer(route_pattern, content):
                route = match.group(1)
                methods_str = match.group(2)

                # Find the function name
                function_pos = content.find("def", match.end())
                if function_pos > 0:
                    function_match = re.search(function_pattern, content[function_pos:function_pos+100])
                    if function_match:
                        function_name = function_match.group(1)

                        # Parse methods
                        methods = None
                        if methods_str:
                            methods = []
                            for method in methods_str.strip("[]").split(","):
                                method = method.strip().strip('"\'')
                                methods.append(method.lower())

                        # Use the blueprint file name as a prefix for the function name
                        blueprint_prefix = file_name.replace(".py", "")
                        prefixed_function_name = f"{blueprint_prefix}_{function_name}"

                        functions.append((prefixed_function_name, route, methods))

    return functions

def generate_root_init(project_root):
    """Generate the root __init__.py file"""
    init_content = '''# This file is required for Azure Functions to discover the functions
# The actual implementation is in function_app.py
'''

    with open(os.path.join(project_root, "__init__.py"), "w") as f:
        f.write(init_content)

    print("Generated root __init__.py file")

def main():
    """Main function"""
    # Get the project root directory
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    print(f"Project root: {project_root}")

    # Generate the root __init__.py file
    generate_root_init(project_root)

    # Extract proxy functions from function_app.py
    function_app_path = os.path.join(project_root, "function_app.py")
    proxy_functions = extract_proxy_functions(function_app_path)

    # Extract blueprint functions
    blueprint_dir = os.path.join(project_root, "blueprints")
    blueprint_functions = extract_blueprint_functions(blueprint_dir)

    # Combine all functions
    all_functions = proxy_functions + blueprint_functions

    # Generate function.json files
    for function_name, route, methods in proxy_functions:
        generate_function_json(function_name, route, methods, project_root, is_blueprint=False)

    for function_name, route, methods in blueprint_functions:
        generate_function_json(function_name, route, methods, project_root, is_blueprint=True)

    print(f"Generated function.json files for {len(all_functions)} functions")

if __name__ == "__main__":
    main()
