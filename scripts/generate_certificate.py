"""
Generate a self-signed certificate and private key for Salesforce JWT authentication

This script generates a self-signed certificate and private key that can be used
for Salesforce JWT authentication. The certificate is saved in PEM format and
can be uploaded to a Salesforce Connected App.

Usage:
    python generate_certificate.py
"""

import os
from cryptography import x509
from cryptography.x509.oid import NameO<PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.primitives.serialization import (
    Encoding, PrivateFormat, NoEncryption, pkcs12, BestAvailableEncryption
)
import datetime

def generate_certificate(output_dir="certs", common_name="AtomSec", org_name="AtomSec", 
                        country="US", validity_days=365):
    """
    Generate a self-signed certificate and private key
    
    Args:
        output_dir: Directory to save the certificate and private key
        common_name: Common name for the certificate
        org_name: Organization name for the certificate
        country: Country code for the certificate
        validity_days: Validity period in days
        
    Returns:
        Tuple[str, str]: Paths to the certificate and private key files
    """
    # Create output directory if it doesn't exist
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        
    # Generate private key
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=2048
    )
    
    # Generate certificate
    subject = issuer = x509.Name([
        x509.NameAttribute(NameOID.COUNTRY_NAME, country),
        x509.NameAttribute(NameOID.ORGANIZATION_NAME, org_name),
        x509.NameAttribute(NameOID.COMMON_NAME, common_name)
    ])
    
    cert = x509.CertificateBuilder().subject_name(
        subject
    ).issuer_name(
        issuer
    ).public_key(
        private_key.public_key()
    ).serial_number(
        x509.random_serial_number()
    ).not_valid_before(
        datetime.datetime.utcnow()
    ).not_valid_after(
        datetime.datetime.utcnow() + datetime.timedelta(days=validity_days)
    ).add_extension(
        x509.SubjectAlternativeName([x509.DNSName(common_name)]),
        critical=False
    ).sign(private_key, hashes.SHA256())
    
    # Write certificate to file
    cert_path = os.path.join(output_dir, "server.crt")
    with open(cert_path, "wb") as f:
        f.write(cert.public_bytes(Encoding.PEM))
    
    # Write private key to file
    key_path = os.path.join(output_dir, "server.key")
    with open(key_path, "wb") as f:
        f.write(private_key.private_bytes(
            Encoding.PEM,
            PrivateFormat.PKCS8,
            NoEncryption()
        ))
    
    # Create a PKCS12 file for import into browsers or keystores
    pfx_path = os.path.join(output_dir, "server.pfx")
    pfx_password = b"atomsec"  # Change this to a secure password
    pfx_data = pkcs12.serialize_key_and_certificates(
        name=common_name.encode(),
        key=private_key,
        cert=cert,
        cas=None,
        encryption_algorithm=BestAvailableEncryption(pfx_password)
    )
    with open(pfx_path, "wb") as f:
        f.write(pfx_data)
    
    print(f"Certificate generated and saved to {cert_path}")
    print(f"Private key generated and saved to {key_path}")
    print(f"PKCS12 file generated and saved to {pfx_path} (password: {pfx_password.decode()})")
    
    return cert_path, key_path, pfx_path

if __name__ == "__main__":
    generate_certificate()
