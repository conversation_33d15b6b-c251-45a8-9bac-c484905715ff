#!/usr/bin/env python
"""
Verify Function Discovery Script

This script verifies that functions are properly discovered by:
1. Generating function.json files
2. Listing all functions that would be discovered
3. Comparing with expected functions

Usage:
    python verify_function_discovery.py

This is useful for troubleshooting function discovery issues.
"""

import os
import sys
import json
import re
import subprocess
from pathlib import Path

def get_project_root():
    """Get the project root directory"""
    return os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

def list_function_directories(project_root):
    """List all function directories in the project"""
    function_dirs = []
    
    # Check all directories in the project root
    for item in os.listdir(project_root):
        item_path = os.path.join(project_root, item)
        
        # Check if it's a directory and has a function.json file
        if os.path.isdir(item_path) and os.path.exists(os.path.join(item_path, "function.json")):
            function_dirs.append(item)
    
    return function_dirs

def extract_expected_functions(project_root):
    """Extract expected functions from function_app.py and blueprints"""
    expected_functions = []
    
    # Check function_app.py for @app.route decorators
    function_app_path = os.path.join(project_root, "function_app.py")
    with open(function_app_path, "r") as f:
        content = f.read()
    
    # Find all @app.route decorators
    route_pattern = r'@app\.route\(route="([^"]+)"(?:,\s*methods=(\[[^\]]+\]))?\)'
    function_pattern = r'def\s+([a-zA-Z0-9_]+)\s*\('
    
    for match in re.finditer(route_pattern, content):
        function_pos = content.find("def", match.end())
        if function_pos > 0:
            function_match = re.search(function_pattern, content[function_pos:function_pos+100])
            if function_match:
                function_name = function_match.group(1)
                expected_functions.append(function_name)
    
    # Find all queue trigger functions
    queue_trigger_pattern = r'@app\.queue_trigger\(arg_name="([^"]+)",\s*queue_name="([^"]+)"'
    
    for match in re.finditer(queue_trigger_pattern, content):
        function_pos = content.find("def", match.end())
        if function_pos > 0:
            function_match = re.search(function_pattern, content[function_pos:function_pos+100])
            if function_match:
                function_name = function_match.group(1)
                expected_functions.append(function_name)
    
    # Check blueprint files
    blueprint_dir = os.path.join(project_root, "blueprints")
    for file_name in os.listdir(blueprint_dir):
        if file_name.endswith(".py") and not file_name.startswith("__"):
            file_path = os.path.join(blueprint_dir, file_name)
            
            with open(file_path, "r") as f:
                content = f.read()
            
            # Find the blueprint name
            bp_match = re.search(r'bp\s*=\s*func\.Blueprint\(\)', content)
            if not bp_match:
                continue
            
            # Find all @bp.route decorators
            route_pattern = r'@bp\.route\(route="([^"]+)"(?:,\s*methods=(\[[^\]]+\]))?\)'
            
            for match in re.finditer(route_pattern, content):
                function_pos = content.find("def", match.end())
                if function_pos > 0:
                    function_match = re.search(function_pattern, content[function_pos:function_pos+100])
                    if function_match:
                        function_name = function_match.group(1)
                        blueprint_prefix = file_name.replace(".py", "")
                        prefixed_function_name = f"{blueprint_prefix}_{function_name}"
                        expected_functions.append(prefixed_function_name)
    
    return expected_functions

def generate_function_json_files():
    """Generate function.json files using the script"""
    project_root = get_project_root()
    script_path = os.path.join(project_root, "scripts", "generate_function_json.py")
    
    print("Generating function.json files...")
    result = subprocess.run([sys.executable, script_path], capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"Error generating function.json files: {result.stderr}")
        return False
    
    print(result.stdout)
    return True

def main():
    """Main function"""
    project_root = get_project_root()
    
    # Generate function.json files
    if not generate_function_json_files():
        print("Failed to generate function.json files")
        return 1
    
    # List function directories
    function_dirs = list_function_directories(project_root)
    print(f"Found {len(function_dirs)} function directories:")
    for function_dir in sorted(function_dirs):
        print(f"  - {function_dir}")
    
    # Extract expected functions
    expected_functions = extract_expected_functions(project_root)
    print(f"\nExpected {len(expected_functions)} functions:")
    for function_name in sorted(expected_functions):
        print(f"  - {function_name}")
    
    # Compare
    missing_functions = set(expected_functions) - set(function_dirs)
    extra_functions = set(function_dirs) - set(expected_functions)
    
    if missing_functions:
        print(f"\nWARNING: {len(missing_functions)} functions are missing:")
        for function_name in sorted(missing_functions):
            print(f"  - {function_name}")
    
    if extra_functions:
        print(f"\nINFO: {len(extra_functions)} extra function directories found:")
        for function_name in sorted(extra_functions):
            print(f"  - {function_name}")
    
    if not missing_functions:
        print("\nAll expected functions have function.json files!")
        return 0
    else:
        print("\nSome functions are missing function.json files.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
