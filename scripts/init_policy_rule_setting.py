"""
<PERSON><PERSON><PERSON> to initialize PolicyRuleSetting table with test data for Azurite/Azure Table Storage.
Enables or disables specific tasks for a user/integration.
"""

import os
from datetime import datetime
from shared.data_access import get_policy_rule_setting_table_repo

# Example test data (customize as needed)
TEST_USER_ID = "test-user-1"
TEST_INTEGRATION_ID = "test-integration-1"
TEST_POLICY_ID = "policy-1"

# Tasks to enable/disable (besides sfdc_authenticate and metadata_extraction, which are always enabled)
TASKS = [
    ("health_check", 1),  # enabled
    ("profiles_permission_sets", 1),  # enabled
    ("overview", 0),  # disabled
    ("data_export", 0),  # disabled
]

def main():
    repo = get_policy_rule_setting_table_repo()
    if not repo:
        print("Could not get PolicyRuleSetting table repo.")
        return
    for task_type, enabled in TASKS:
        entity = {
            "PartitionKey": TEST_POLICY_ID,
            "RowKey": f"{TEST_USER_ID}-{TEST_INTEGRATION_ID}-{task_type}",
            "PolicyId": TEST_POLICY_ID,
            "UserId": TEST_USER_ID,
            "IntegrationId": TEST_INTEGRATION_ID,
            "TaskType": task_type,
            "Enabled": int(enabled),
            "CreatedAt": datetime.now().isoformat(),
        }
        success = repo.insert_entity(entity)
        print(f"Inserted/updated PolicyRuleSetting for task '{task_type}' (enabled={enabled}): {success}")

if __name__ == "__main__":
    main() 