"""
Integration Router

This module provides a FastAPI router for integration-related endpoints.
It consolidates the following endpoints:
- GET /api/integrations
- GET /api/integration/{tenant_url}/guest-user-risks
- GET /api/integration/{tenant_url}/health-check
- GET /api/integration/{tenant_url}/overview
- GET /api/integration/{tenant_url}/pmd-issues
- GET /api/integration/{tenant_url}/profiles
"""

import logging
import json
from datetime import datetime
from typing import Dict, Any, List, Optional, Union

from fastapi import APIRouter, Depends, HTTPException, Query, Path, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

# Import shared modules
from shared.config import is_local_dev
from shared.utils import create_json_response, handle_exception
from shared.auth_utils import require_auth, get_current_user
from shared.data_access import (
    get_table_storage_repository
)
from blueprints.integration_tabs import (
    get_integration_sql_repo,
    get_overview_repo as get_overview_sql_repo,
    get_health_check_repo as get_health_check_sql_repo,
    get_profile_permissions_repo as get_profiles_sql_repo,
    # get_guest_user_risks_repo, // Removed Guest User Risks
    # get_pmd_issues_repo // Removed PMD Issues
)

# Import background processor
from shared.background_processor import BackgroundProcessor
from shared.background_processor import (
    TASK_TYPE_OVERVIEW,
    TASK_TYPE_HEALTH_CHECK,
    TASK_TYPE_PROFILES,
)

# Configure logging
logger = logging.getLogger('integration_router')
logger.setLevel(logging.INFO)

# Create router
# Use a prefix without /api/ since the frontend proxy already adds /api/
router = APIRouter(prefix="/integration", tags=["Integration"])

# Define response models
class IntegrationResponse(BaseModel):
    id: str
    name: str
    tenantUrl: str
    type: str
    description: Optional[str] = None
    environment: Optional[str] = None
    isActive: bool
    lastScan: Optional[str] = None
    createdAt: Optional[str] = None
    userEmail: Optional[str] = None
    healthScore: Optional[str] = None

class IntegrationsResponse(BaseModel):
    integrations: List[IntegrationResponse]
    count: int

class DataStatusResponse(BaseModel):
    dataStatus: str
    message: Optional[str] = None
    timestamp: str
    tenantUrl: str
    taskId: Optional[str] = None

# Function get_integration_by_tenant_url has been removed in favor of direct integration ID lookup

# Define routes
@router.get("/integrations", response_model=IntegrationsResponse)
async def get_integrations(
    include_inactive: bool = Query(False, description="Include inactive integrations"),
    integration_type: Optional[str] = Query(None, description="Filter by integration type"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get all integrations for the current user's account
    """
    logger.info("Getting integrations...")

    try:
        # Check if we're in local development mode
        integrations = []
        if is_local_dev():
            # For local development, try to get integrations from Azure Table Storage
            try:
                integration_repo = get_table_storage_repository("Integrations")
                if not integration_repo:
                    logger.error("Integration table repository not available")
                    return {"integrations": [], "count": 0}

                # Build filter query
                filter_parts = ["PartitionKey eq 'integration'"]

                if not include_inactive:
                    filter_parts.append("IsActive eq true")

                if integration_type:
                    filter_parts.append(f"Type eq '{integration_type}'")

                filter_query = " and ".join(filter_parts)
                logger.info(f"Querying with filter: {filter_query}")

                # Query for integrations
                entities = integration_repo.query_entities(filter_query)
                logger.info(f"Found {len(entities) if entities else 0} integrations")

                # Convert to list of dictionaries
                for entity in entities or []:
                    integration = {
                        "id": entity.get("RowKey"),
                        "name": entity.get("Name", ""),
                        "tenantUrl": entity.get("TenantUrl", ""),
                        "type": entity.get("Type", "Salesforce"),
                        "description": entity.get("Description", ""),
                        "environment": entity.get("Environment", "production"),
                        "isActive": entity.get("IsActive", True),
                        "lastScan": entity.get("LastScan", ""),
                        "createdAt": entity.get("CreatedAt", ""),
                        "userEmail": entity.get("UserEmail", ""),
                        "healthScore": entity.get("HealthScore", "0")
                    }
                    integrations.append(integration)
            except Exception as e:
                logger.error(f"Error getting integrations from Azure Table Storage: {str(e)}")
                return {"integrations": [], "count": 0}
        else:
            # Use SQL Database for production
            integration_repo = get_integration_sql_repo()
            if not integration_repo:
                logger.error("Integration SQL repository not available")
                raise HTTPException(status_code=500, detail="Integration repository not available")

            # Build SQL query
            query = """
            SELECT Id, Name, TenantUrl, Type, Description, Environment, IsActive, LastScan, CreatedAt, UserEmail, HealthScore
            FROM App_Integration
            """

            where_clauses = []
            params = []

            if not include_inactive:
                where_clauses.append("IsActive = ?")
                params.append(True)

            if integration_type:
                where_clauses.append("Type = ?")
                params.append(integration_type)

            if where_clauses:
                query += " WHERE " + " AND ".join(where_clauses)

            # Execute query
            results = integration_repo.execute_query(query, tuple(params) if params else None)

            # Convert to list of dictionaries
            integrations = []
            for row in results:
                integration = {
                    "id": row[0],
                    "name": row[1],
                    "tenantUrl": row[2],
                    "type": row[3],
                    "description": row[4],
                    "environment": row[5],
                    "isActive": row[6],
                    "lastScan": row[7],
                    "createdAt": row[8],
                    "userEmail": row[9],
                    "healthScore": row[10] if len(row) > 10 and row[10] is not None else "0"
                }
                integrations.append(integration)

        # Return integrations
        return {
            "integrations": integrations,
            "count": len(integrations)
        }
    except Exception as e:
        logger.error(f"Error in get_integrations: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{integration_id}/overview")
async def get_integration_overview(
    integration_id: str = Path(..., description="Integration ID"),
    force_refresh: bool = Query(False, description="Force refresh data"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get integration overview
    """
    logger.info("Getting integration overview...")
    logger.info(f"Integration ID: {integration_id}")
    logger.info(f"Force refresh: {force_refresh}")

    try:
        # Get integration by ID
        if is_local_dev():
            # For local development, try to get integration from Azure Table Storage
            try:
                integration_repo = get_table_storage_repository("Integrations")
                if not integration_repo:
                    logger.error("Integration table repository not available")
                    raise HTTPException(status_code=500, detail="Integration repository not available")

                # Get integration by ID
                integration = integration_repo.get_entity("integration", integration_id)
                if not integration:
                    logger.warning(f"Integration not found for ID: {integration_id}")
                    raise HTTPException(status_code=404, detail=f"Integration not found for ID: {integration_id}")

                # Convert to dictionary
                integration = {
                    "id": integration.get("RowKey"),
                    "name": integration.get("Name", ""),
                    "tenantUrl": integration.get("TenantUrl", ""),
                    "type": integration.get("Type", "Salesforce"),
                    "description": integration.get("Description", ""),
                    "environment": integration.get("Environment", "production"),
                    "isActive": integration.get("IsActive", True),
                    "lastScan": integration.get("LastScan", ""),
                    "createdAt": integration.get("CreatedAt", ""),
                    "userEmail": integration.get("UserEmail", "")
                }
                logger.info(f"Found integration: {integration}")
            except Exception as e:
                logger.error(f"Error getting integration from Azure Table Storage: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Error getting integration: {str(e)}")
        else:
            # Use SQL Database for production
            integration_repo = get_integration_sql_repo()
            if not integration_repo:
                logger.error("Integration SQL repository not available")
                raise HTTPException(status_code=500, detail="Integration repository not available")

            # Get integration by ID
            query = """
            SELECT Id, Name, TenantUrl, Type, Description, Environment, IsActive, LastScan, CreatedAt, UserEmail
            FROM App_Organization
            WHERE Id = ?
            """
            results = integration_repo.execute_query(query, (integration_id,))

            if not results or len(results) == 0:
                logger.warning(f"Integration not found for ID: {integration_id}")
                raise HTTPException(status_code=404, detail=f"Integration not found for ID: {integration_id}")

            # Convert to dictionary
            integration = {
                "id": results[0][0],
                "name": results[0][1],
                "tenantUrl": results[0][2],
                "type": results[0][3],
                "description": results[0][4],
                "environment": results[0][5],
                "isActive": results[0][6],
                "lastScan": results[0][7].isoformat() if results[0][7] else None,
                "createdAt": results[0][8].isoformat() if results[0][8] else None,
                "userEmail": results[0][9]
            }
            logger.info(f"Found integration: {integration}")

        # Check if integration is active
        if not integration.get("isActive", False):
            raise HTTPException(status_code=400, detail="Integration is not active")

        # Try to get data from database first
        if not force_refresh:
            # Get integration ID
            integration_id = integration.get("id")

            # Query the database for overview data
            if is_local_dev():
                # For local development, try to get data from Azure Table Storage
                try:
                    # Initialize the Overview table repository
                    overview_repo = get_table_storage_repository("Overview")
                    if not overview_repo:
                        logger.error("Overview table repository not available")
                        return {
                            "dataStatus": "empty",
                            "message": "No overview data available. Please sync to fetch data.",
                            "timestamp": datetime.now().isoformat(),
                            "tenantUrl": integration.get("tenantUrl", "")
                        }

                    # Query for the latest overview data for this organization
                    filter_query = f"PartitionKey eq '{integration_id}'"
                    logger.info(f"Querying Overview table with filter: {filter_query}")
                    entities = overview_repo.query_entities(filter_query)
                    logger.info(f"Found {len(entities) if entities else 0} overview entities")

                    if not entities or len(entities) == 0:
                        # No data found, return a response indicating data needs to be fetched
                        logger.info(f"No overview data found for integration {integration_id}")
                        return {
                            "dataStatus": "empty",
                            "message": "No overview data available. Please sync to fetch data.",
                            "timestamp": datetime.now().isoformat(),
                            "tenantUrl": integration.get("tenantUrl", "")
                        }

                    # Sort entities by timestamp (RowKey) to get the latest
                    sorted_entities = sorted(entities, key=lambda x: x.get('RowKey', ''), reverse=True)
                    latest_entity = sorted_entities[0]
                    logger.info(f"Latest entity RowKey: {latest_entity.get('RowKey')}")

                    # Create the response data from the entity
                    overview_data = {
                        "dataStatus": "available",
                        "healthScore": latest_entity.get('HealthScore', 0),
                        "totalProfiles": latest_entity.get('TotalProfiles', 0),
                        "totalPermissionSets": latest_entity.get('TotalPermissionSets', 0),
                        "totalRisks": latest_entity.get('TotalRisks', 0),
                        "highRisks": latest_entity.get('HighRisks', 0),
                        "mediumRisks": latest_entity.get('MediumRisks', 0),
                        "lowRisks": latest_entity.get('LowRisks', 0),
                        "lastUpdated": latest_entity.get('LastUpdated', datetime.now().isoformat())
                    }
                    logger.info(f"Retrieved overview data from Azure Table Storage for organization {integration_id}")
                except Exception as e:
                    logger.error(f"Error retrieving overview data from Azure Table Storage: {str(e)}")
                    # Return empty data response
                    return {
                        "dataStatus": "empty",
                        "message": "No overview data available. Please sync to fetch data.",
                        "timestamp": datetime.now().isoformat(),
                        "tenantUrl": integration.get("tenantUrl", "")
                    }
            else:
                # Use SQL Database for production
                overview_repo = get_overview_sql_repo()
                if not overview_repo:
                    logger.error("Overview SQL repository not available")
                    raise HTTPException(status_code=500, detail="Overview repository not available")

                # Query for the latest overview data
                query = """
                SELECT o.HealthScore, o.TotalProfiles, o.TotalPermissionSets, o.TotalRisks,
                       o.HighRisks, o.MediumRisks, o.LowRisks, o.LastUpdated
                FROM App_Overview o
                INNER JOIN App_ExecutionLog e ON o.ExecutionLogId = e.Id
                WHERE o.OrgId = ? AND e.Status = 'Completed'
                ORDER BY e.StartTime DESC
                """
                logger.info(f"Executing SQL query: {query} with params: ({integration_id},)")
                results = overview_repo.execute_query(query, (integration_id,))
                logger.info(f"Query returned {len(results) if results else 0} results")

                if not results or len(results) == 0:
                    # No data found, return a response indicating data needs to be fetched
                    return {
                        "dataStatus": "empty",
                        "message": "No overview data available. Please sync to fetch data.",
                        "timestamp": datetime.now().isoformat(),
                        "tenantUrl": integration.get("tenantUrl", "")
                    }

                # Extract data from the query results
                health_score, total_profiles, total_permission_sets, total_risks, high_risks, medium_risks, low_risks, last_updated = results[0]
                logger.info(f"Extracted data: HealthScore={health_score}, TotalProfiles={total_profiles}, etc.")

                # Create the response data
                overview_data = {
                    "dataStatus": "available",
                    "healthScore": health_score,
                    "totalProfiles": total_profiles,
                    "totalPermissionSets": total_permission_sets,
                    "totalRisks": total_risks,
                    "highRisks": high_risks,
                    "mediumRisks": medium_risks,
                    "lowRisks": low_risks,
                    "lastUpdated": last_updated.isoformat() if isinstance(last_updated, datetime) else last_updated
                }
                logger.info(f"Created overview data response")

            # Return the overview data
            logger.info("Returning overview data response")
            return overview_data

        # If force refresh or no data in database, trigger a background fetch
        logger.info("Triggering background fetch for overview data")

        # Create a background processor
        # processor = BackgroundProcessor()
        # logger.info("Created BackgroundProcessor instance")

        # Enqueue a task
        # logger.info(f"Enqueueing task: type={TASK_TYPE_OVERVIEW}, org_id={integration.get('id')}, user_id={current_user.get('id')}")
        # task_id = processor.enqueue_task(
        #     task_type=TASK_TYPE_OVERVIEW,
        #     org_id=integration.get("id"),
        #     user_id=current_user.get("id")
        # )

        # if not task_id:
        #     raise HTTPException(status_code=500, detail="Failed to enqueue task")

        # Return pending status
        response_data = {
            # "dataStatus": "pending",
            # "message": "The overview data will be available shortly. Please refresh to check if data is available.",
            "dataStatus": "empty",
            "message": "Overview data is not available. Please perform a full rescan if needed.",
            "timestamp": datetime.now().isoformat(),
            "tenantUrl": integration.get("tenantUrl", ""),
            # "taskId": task_id
            "taskId": None # Or an empty string
        }

        return response_data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_integration_overview: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{integration_id}/health-check")
async def get_integration_health_check(
    integration_id: str = Path(..., description="Integration ID"),
    force_refresh: bool = Query(False, description="Force refresh data"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get integration health check
    """
    logger.info("Getting integration health check...")
    logger.info(f"Integration ID: {integration_id}")
    logger.info(f"Force refresh: {force_refresh}")

    try:
        # Get integration by ID
        if is_local_dev():
            # For local development, try to get integration from Azure Table Storage
            try:
                integration_repo = get_table_storage_repository("Integrations")
                if not integration_repo:
                    logger.error("Integration table repository not available")
                    raise HTTPException(status_code=500, detail="Integration repository not available")

                # Get integration by ID
                integration = integration_repo.get_entity("integration", integration_id)
                if not integration:
                    logger.warning(f"Integration not found for ID: {integration_id}")
                    raise HTTPException(status_code=404, detail=f"Integration not found for ID: {integration_id}")

                # Convert to dictionary
                integration = {
                    "id": integration.get("RowKey"),
                    "name": integration.get("Name", ""),
                    "tenantUrl": integration.get("TenantUrl", ""),
                    "type": integration.get("Type", "Salesforce"),
                    "description": integration.get("Description", ""),
                    "environment": integration.get("Environment", "production"),
                    "isActive": integration.get("IsActive", True),
                    "lastScan": integration.get("LastScan", ""),
                    "createdAt": integration.get("CreatedAt", ""),
                    "userEmail": integration.get("UserEmail", "")
                }
                logger.info(f"Found integration: {integration}")
            except Exception as e:
                logger.error(f"Error getting integration from Azure Table Storage: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Error getting integration: {str(e)}")
        else:
            # Use SQL Database for production
            integration_repo = get_integration_sql_repo()
            if not integration_repo:
                logger.error("Integration SQL repository not available")
                raise HTTPException(status_code=500, detail="Integration repository not available")

            # Get integration by ID
            query = """
            SELECT Id, Name, TenantUrl, Type, Description, Environment, IsActive, LastScan, CreatedAt, UserEmail
            FROM App_Organization
            WHERE Id = ?
            """
            results = integration_repo.execute_query(query, (integration_id,))

            if not results or len(results) == 0:
                logger.warning(f"Integration not found for ID: {integration_id}")
                raise HTTPException(status_code=404, detail=f"Integration not found for ID: {integration_id}")

            # Convert to dictionary
            integration = {
                "id": results[0][0],
                "name": results[0][1],
                "tenantUrl": results[0][2],
                "type": results[0][3],
                "description": results[0][4],
                "environment": results[0][5],
                "isActive": results[0][6],
                "lastScan": results[0][7].isoformat() if results[0][7] else None,
                "createdAt": results[0][8].isoformat() if results[0][8] else None,
                "userEmail": results[0][9]
            }
            logger.info(f"Found integration: {integration}")

        # Check if integration is active
        if not integration.get("isActive", False):
            raise HTTPException(status_code=400, detail="Integration is not active")

        # Try to get data from database first
        if not force_refresh:
            # Get integration ID
            integration_id = integration.get("id")

            # Query the database for health check data
            if is_local_dev():
                # For local development, try to get data from Azure Table Storage
                try:
                    # Initialize the HealthCheck table repository
                    health_check_repo = get_table_storage_repository("HealthCheck")
                    if not health_check_repo:
                        logger.error("HealthCheck table repository not available")
                        return {
                            "dataStatus": "empty",
                            "message": "No health check data available. Please sync to fetch data.",
                            "timestamp": datetime.now().isoformat(),
                            "tenantUrl": integration.get("tenantUrl", "")
                        }

                    # Get health score from the integration record
                    integration_repo = get_table_storage_repository("Integrations")
                    health_score = 0
                    if integration_repo:
                        integration_record = integration_repo.get_entity("integration", integration_id)
                        health_score = int(integration_record.get("HealthScore", 0)) if integration_record and "HealthScore" in integration_record else 0
                        logger.info(f"Retrieved health score from integration record: {health_score}")

                    # Query for health check risks for this organization
                    filter_query = f"PartitionKey eq '{integration_id}'"
                    logger.info(f"Querying HealthCheck table with filter: {filter_query}")
                    entities = health_check_repo.query_entities(filter_query)
                    logger.info(f"Found {len(entities) if entities else 0} health check entities")

                    # If we don't have any records, return a response indicating data needs to be fetched
                    if not entities or len(entities) == 0:
                        logger.info(f"No health check records found for integration {integration_id}")
                        return {
                            "dataStatus": "empty",
                            "message": "No health check data available. Please sync to fetch data.",
                            "timestamp": datetime.now().isoformat(),
                            "tenantUrl": integration.get("tenantUrl", "")
                        }

                    # Extract risks from the entities
                    risks = []
                    last_updated = None

                    for entity in entities:
                        risk = {
                            "riskType": entity.get("RiskType", "UNKNOWN_RISK"),
                            "description": entity.get("Description", "Unknown Risk"),
                            "impact": entity.get("Impact", "Unknown"),
                            "recommendation": entity.get("Recommendation", "Unknown")
                        }
                        risks.append(risk)

                        # Track the latest update time
                        entity_timestamp = entity.get("Timestamp")
                        if entity_timestamp and (last_updated is None or entity_timestamp > last_updated):
                            last_updated = entity_timestamp

                    # Create the response data
                    health_check_data = {
                        "dataStatus": "available",
                        "score": health_score,
                        "risks": risks,
                        "lastUpdated": last_updated.isoformat() if isinstance(last_updated, datetime) else (datetime.now().isoformat() if last_updated is None else last_updated)
                    }
                    logger.info(f"Created health check data response with {len(risks)} risks")
                except Exception as e:
                    logger.error(f"Error retrieving health check data from Azure Table Storage: {str(e)}")
                    # Return empty data response
                    return {
                        "dataStatus": "empty",
                        "message": "No health check data available. Please sync to fetch data.",
                        "timestamp": datetime.now().isoformat(),
                        "tenantUrl": integration.get("tenantUrl", "")
                    }
            else:
                # Use SQL Database for production
                health_check_repo = get_health_check_sql_repo()
                if not health_check_repo:
                    logger.error("Health check SQL repository not available")
                    raise HTTPException(status_code=500, detail="Health check repository not available")

                # Query for the latest health check data
                query = """
                SELECT h.Score, h.LastUpdated
                FROM App_HealthCheck h
                INNER JOIN App_ExecutionLog e ON h.ExecutionLogId = e.Id
                WHERE h.OrgId = ? AND e.Status = 'Completed'
                ORDER BY e.StartTime DESC
                """
                results = health_check_repo.execute_query(query, (integration_id,))

                if not results or len(results) == 0:
                    # No data found, return a response indicating data needs to be fetched
                    return {
                        "dataStatus": "empty",
                        "message": "No health check data available. Please sync to fetch data.",
                        "timestamp": datetime.now().isoformat(),
                        "tenantUrl": integration.get("tenantUrl", "")
                    }

                # Extract data from the query results
                score, last_updated = results[0]

                # Query for health check risks
                query = """
                SELECT r.RiskType, r.Description, r.Impact, r.Recommendation
                FROM App_HealthCheckRisk r
                INNER JOIN App_HealthCheck h ON r.HealthCheckId = h.Id
                INNER JOIN App_ExecutionLog e ON h.ExecutionLogId = e.Id
                WHERE h.OrgId = ? AND e.Status = 'Completed'
                ORDER BY e.StartTime DESC, r.Impact DESC
                """
                risk_results = health_check_repo.execute_query(query, (integration_id,))

                # Process risks
                risks = []
                for risk_row in risk_results:
                    risk_type, description, impact, recommendation = risk_row
                    risks.append({
                        "riskType": risk_type,
                        "description": description,
                        "impact": impact,
                        "recommendation": recommendation
                    })

                # Create the response data
                health_check_data = {
                    "dataStatus": "available",
                    "score": score,
                    "risks": risks,
                    "lastUpdated": last_updated.isoformat() if isinstance(last_updated, datetime) else last_updated
                }

            # Return the health check data
            return health_check_data

        # If force refresh or no data in database, trigger a background fetch
        logger.info(f"Force refresh requested for health check data. Integration ID: {integration.get('id')}")

        try:
            # Create a background processor
            processor = BackgroundProcessor()

            # Get the integration ID
            integration_id = integration.get("id")
            if not integration_id:
                logger.error("Integration ID is missing")
                raise HTTPException(status_code=500, detail="Integration ID is missing")

            # Enqueue a task
            task_id = processor.enqueue_task(
                task_type=TASK_TYPE_HEALTH_CHECK,
                org_id=integration_id,
                user_id=current_user.get("id")
            )

            if not task_id:
                raise HTTPException(status_code=500, detail="Failed to enqueue task")

            # Return pending status
            response_data = {
                "dataStatus": "pending",
                "message": "The health check data will be available shortly. Please refresh to check if data is available.",
                "timestamp": datetime.now().isoformat(),
                "tenantUrl": integration.get("tenantUrl", ""),
                "taskId": task_id
            }

            return response_data
        except Exception as e:
            logger.error(f"Error enqueueing health check task: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error enqueueing health check task: {str(e)}")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_integration_health_check: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{integration_id}/profiles")
async def get_integration_profiles(
    integration_id: str = Path(..., description="Integration ID"),
    force_refresh: bool = Query(False, description="Force refresh data"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get integration profiles and permission sets
    """
    logger.info("Getting integration profiles and permission sets...")
    logger.info(f"Integration ID: {integration_id}")
    logger.info(f"Force refresh: {force_refresh}")

    try:
        # Get integration by ID
        if is_local_dev():
            # For local development, try to get integration from Azure Table Storage
            try:
                integration_repo = get_table_storage_repository("Integrations")
                if not integration_repo:
                    logger.error("Integration table repository not available")
                    raise HTTPException(status_code=500, detail="Integration repository not available")

                # Get integration by ID
                integration = integration_repo.get_entity("integration", integration_id)
                if not integration:
                    logger.warning(f"Integration not found for ID: {integration_id}")
                    raise HTTPException(status_code=404, detail=f"Integration not found for ID: {integration_id}")

                # Convert to dictionary
                integration = {
                    "id": integration.get("RowKey"),
                    "name": integration.get("Name", ""),
                    "tenantUrl": integration.get("TenantUrl", ""),
                    "type": integration.get("Type", "Salesforce"),
                    "description": integration.get("Description", ""),
                    "environment": integration.get("Environment", "production"),
                    "isActive": integration.get("IsActive", True),
                    "lastScan": integration.get("LastScan", ""),
                    "createdAt": integration.get("CreatedAt", ""),
                    "userEmail": integration.get("UserEmail", "")
                }
                logger.info(f"Found integration: {integration}")
            except Exception as e:
                logger.error(f"Error getting integration from Azure Table Storage: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Error getting integration: {str(e)}")
        else:
            # Use SQL Database for production
            integration_repo = get_integration_sql_repo()
            if not integration_repo:
                logger.error("Integration SQL repository not available")
                raise HTTPException(status_code=500, detail="Integration repository not available")

            # Get integration by ID
            query = """
            SELECT Id, Name, TenantUrl, Type, Description, Environment, IsActive, LastScan, CreatedAt, UserEmail
            FROM App_Organization
            WHERE Id = ?
            """
            results = integration_repo.execute_query(query, (integration_id,))

            if not results or len(results) == 0:
                logger.warning(f"Integration not found for ID: {integration_id}")
                raise HTTPException(status_code=404, detail=f"Integration not found for ID: {integration_id}")

            # Convert to dictionary
            integration = {
                "id": results[0][0],
                "name": results[0][1],
                "tenantUrl": results[0][2],
                "type": results[0][3],
                "description": results[0][4],
                "environment": results[0][5],
                "isActive": results[0][6],
                "lastScan": results[0][7].isoformat() if results[0][7] else None,
                "createdAt": results[0][8].isoformat() if results[0][8] else None,
                "userEmail": results[0][9]
            }
            logger.info(f"Found integration: {integration}")

        # Check if integration is active
        if not integration.get("isActive", False):
            raise HTTPException(status_code=400, detail="Integration is not active")

        # Try to get data from database first
        if not force_refresh:
            # Get integration ID
            integration_id = integration.get("id")

            # Query the database for profiles data
            if is_local_dev():
                # For local development, try to get data from Azure Table Storage
                try:
                    # Query the database for profiles data using the profile system permissions module
                    logger.info("Fetching system permissions data for profiles and permission sets")

                    # Initialize the ProfilePermissions table repository
                    profile_permissions_repo = get_table_storage_repository("ProfilePermissions")
                    if not profile_permissions_repo:
                        logger.error("ProfilePermissions table repository not available")
                        return {
                            "dataStatus": "empty",
                            "message": "No profiles data available. Please sync to fetch data.",
                            "timestamp": datetime.now().isoformat(),
                            "tenantUrl": integration.get("tenantUrl", "")
                        }

                    # Query for profile permissions
                    filter_query = f"PartitionKey eq '{integration_id}' and EntityType eq 'Profile'"
                    logger.info(f"Querying ProfilePermissions table with filter: {filter_query}")
                    profile_entities = profile_permissions_repo.query_entities(filter_query)
                    logger.info(f"Found {len(profile_entities) if profile_entities else 0} profile permission entities")

                    # Query for permission set permissions
                    filter_query = f"PartitionKey eq '{integration_id}' and EntityType eq 'PermissionSet'"
                    logger.info(f"Querying ProfilePermissions table with filter: {filter_query}")
                    permission_set_entities = profile_permissions_repo.query_entities(filter_query)
                    logger.info(f"Found {len(permission_set_entities) if permission_set_entities else 0} permission set entities")

                    # If we don't have any records, return a response indicating data needs to be fetched
                    if (not profile_entities or len(profile_entities) == 0) and (not permission_set_entities or len(permission_set_entities) == 0):
                        logger.info(f"No profile or permission set records found for integration {integration_id}")
                        return {
                            "dataStatus": "empty",
                            "message": "No profiles data available. Please sync to fetch data.",
                            "timestamp": datetime.now().isoformat(),
                            "tenantUrl": integration.get("tenantUrl", "")
                        }

                    # Process profiles
                    profiles = []
                    for entity in profile_entities or []:
                        try:
                            # Determine security risk level based on permissions
                            risk_level = "LOW_RISK"
                            permissions = {}

                            # Extract permissions from entity
                            for key, value in entity.items():
                                if key.startswith("Permission_"):
                                    perm_name = key[11:]  # Remove "Permission_" prefix
                                    permissions[perm_name] = value == "true" or value is True

                            # Determine risk level based on permissions
                            if permissions.get("modifyAllData") or permissions.get("manageUsers") or permissions.get("manageProfilesPermissionsets") or permissions.get("manageRoles"):
                                risk_level = "HIGH_RISK"
                            elif permissions.get("viewAllData") or permissions.get("resetPasswords") or permissions.get("dataExport") or permissions.get("manageSharing"):
                                risk_level = "MEDIUM_RISK"

                            profile = {
                                "id": entity.get("EntityId", ""),
                                "name": entity.get("Name", ""),
                                "description": f"{entity.get('Name', '')} profile",
                                "userLicense": entity.get("UserLicense", "Salesforce"),
                                "createdDate": entity.get("CreatedDate", datetime.now().isoformat()),
                                "lastModifiedDate": entity.get("LastModifiedDate", datetime.now().isoformat()),
                                "riskLevel": risk_level,
                                "riskScore": 90 if risk_level == "HIGH_RISK" else (60 if risk_level == "MEDIUM_RISK" else 30),
                                "userCount": int(entity.get("UserCount", 0)),
                                "permissions": permissions
                            }
                            profiles.append(profile)
                        except Exception as profile_error:
                            logger.error(f"Error processing profile entity: {str(profile_error)}")

                    # Process permission sets
                    permission_sets = []
                    for entity in permission_set_entities or []:
                        try:
                            # Determine security risk level based on permissions
                            risk_level = "LOW_RISK"
                            permissions = {}

                            # Extract permissions from entity
                            for key, value in entity.items():
                                if key.startswith("Permission_"):
                                    perm_name = key[11:]  # Remove "Permission_" prefix
                                    permissions[perm_name] = value == "true" or value is True

                            # Determine risk level based on permissions
                            if permissions.get("modifyAllData") or permissions.get("manageUsers") or permissions.get("manageProfilesPermissionsets") or permissions.get("manageRoles"):
                                risk_level = "HIGH_RISK"
                            elif permissions.get("viewAllData") or permissions.get("resetPasswords") or permissions.get("dataExport") or permissions.get("manageSharing"):
                                risk_level = "MEDIUM_RISK"

                            permission_set = {
                                "id": entity.get("EntityId", ""),
                                "name": entity.get("Name", ""),
                                "description": f"{entity.get('Name', '')} permission set",
                                "createdDate": entity.get("CreatedDate", datetime.now().isoformat()),
                                "lastModifiedDate": entity.get("LastModifiedDate", datetime.now().isoformat()),
                                "riskLevel": risk_level,
                                "riskScore": 90 if risk_level == "HIGH_RISK" else (60 if risk_level == "MEDIUM_RISK" else 30),
                                "userCount": int(entity.get("UserCount", 0)),
                                "permissions": permissions
                            }
                            permission_sets.append(permission_set)
                        except Exception as ps_error:
                            logger.error(f"Error processing permission set entity: {str(ps_error)}")

                    # Create the response data
                    profiles_data = {
                        "dataStatus": "available",
                        "profiles": profiles,
                        "permissionSets": permission_sets,
                        "lastUpdated": datetime.now().isoformat()
                    }
                    logger.info(f"Created profiles data response with {len(profiles)} profiles and {len(permission_sets)} permission sets")
                except Exception as e:
                    logger.error(f"Error retrieving profiles data from Azure Table Storage: {str(e)}")
                    # Return empty data response
                    return {
                        "dataStatus": "empty",
                        "message": "No profiles data available. Please sync to fetch data.",
                        "timestamp": datetime.now().isoformat(),
                        "tenantUrl": integration.get("tenantUrl", "")
                    }
            else:
                # Use SQL Database for production
                profiles_repo = get_profiles_sql_repo()
                if not profiles_repo:
                    logger.error("Profiles SQL repository not available")
                    raise HTTPException(status_code=500, detail="Profiles repository not available")

                # Query for the latest profiles data
                query = """
                SELECT p.Id, p.Name, p.Description, p.UserLicense, p.CreatedDate, p.LastModifiedDate,
                       p.RiskLevel, p.RiskScore, p.UserCount, p.Permissions
                FROM App_Profile p
                INNER JOIN App_ExecutionLog e ON p.ExecutionLogId = e.Id
                WHERE p.OrgId = ? AND e.Status = 'Completed'
                ORDER BY e.StartTime DESC, p.RiskScore DESC
                """
                profile_results = profiles_repo.execute_query(query, (integration_id,))

                if not profile_results or len(profile_results) == 0:
                    # No data found, return a response indicating data needs to be fetched
                    return {
                        "dataStatus": "empty",
                        "message": "No profiles data available. Please sync to fetch data.",
                        "timestamp": datetime.now().isoformat(),
                        "tenantUrl": integration.get("tenantUrl", "")
                    }

                # Query for permission sets
                query = """
                SELECT ps.Id, ps.Name, ps.Description, ps.CreatedDate, ps.LastModifiedDate,
                       ps.RiskLevel, ps.RiskScore, ps.UserCount, ps.Permissions
                FROM App_PermissionSet ps
                INNER JOIN App_ExecutionLog e ON ps.ExecutionLogId = e.Id
                WHERE ps.OrgId = ? AND e.Status = 'Completed'
                ORDER BY e.StartTime DESC, ps.RiskScore DESC
                """
                permission_set_results = profiles_repo.execute_query(query, (integration_id,))

                # Process profiles
                processed_profiles = []
                for profile_row in profile_results:
                    profile_id, name, description, user_license, created_date, last_modified_date, risk_level, risk_score, user_count, permissions_json = profile_row

                    # Parse permissions JSON
                    try:
                        permissions = json.loads(permissions_json) if permissions_json else {}
                    except:
                        permissions = {}

                    processed_profiles.append({
                        "id": profile_id,
                        "name": name,
                        "description": description,
                        "userLicense": user_license,
                        "createdDate": created_date.isoformat() if isinstance(created_date, datetime) else created_date,
                        "lastModifiedDate": last_modified_date.isoformat() if isinstance(last_modified_date, datetime) else last_modified_date,
                        "riskLevel": risk_level,
                        "riskScore": risk_score,
                        "userCount": user_count,
                        "permissions": permissions
                    })

                # Process permission sets
                processed_permission_sets = []
                for ps_row in permission_set_results:
                    ps_id, name, description, created_date, last_modified_date, risk_level, risk_score, user_count, permissions_json = ps_row

                    # Parse permissions JSON
                    try:
                        permissions = json.loads(permissions_json) if permissions_json else {}
                    except:
                        permissions = {}

                    processed_permission_sets.append({
                        "id": ps_id,
                        "name": name,
                        "description": description,
                        "createdDate": created_date.isoformat() if isinstance(created_date, datetime) else created_date,
                        "lastModifiedDate": last_modified_date.isoformat() if isinstance(last_modified_date, datetime) else last_modified_date,
                        "riskLevel": risk_level,
                        "riskScore": risk_score,
                        "userCount": user_count,
                        "permissions": permissions
                    })

                # Get the last updated timestamp
                query = """
                SELECT MAX(e.EndTime)
                FROM App_ExecutionLog e
                WHERE e.OrgId = ? AND e.TaskType = ? AND e.Status = 'Completed'
                """
                last_updated_result = profiles_repo.execute_query(query, (integration_id, TASK_TYPE_PROFILES))
                last_updated = last_updated_result[0][0] if last_updated_result and last_updated_result[0][0] else datetime.now()

                # Create the response data
                profiles_data = {
                    "dataStatus": "available",
                    "profiles": processed_profiles,
                    "permissionSets": processed_permission_sets,
                    "lastUpdated": last_updated.isoformat() if isinstance(last_updated, datetime) else last_updated
                }

            logger.info(f"Returning profiles data with {len(profiles_data.get('profiles', [])) if isinstance(profiles_data, dict) else 0} profiles and {len(profiles_data.get('permissionSets', [])) if isinstance(profiles_data, dict) else 0} permission sets")

            # Return the profiles data
            return profiles_data

        # If force refresh or no data in database, trigger a background fetch
        # Create a background processor
        # processor = BackgroundProcessor()

        # Enqueue a task
        # task_id = processor.enqueue_task(
        #     task_type=TASK_TYPE_PROFILES,
        #     org_id=integration.get("id"),
        #     user_id=current_user.get("id")
        # )

        # if not task_id:
        #     raise HTTPException(status_code=500, detail="Failed to enqueue task")

        # Return pending status
        # response_data = {
        #     "dataStatus": "pending",
        #     "message": "The profiles data will be available shortly. Please refresh to check if data is available.",
        #     "timestamp": datetime.now().isoformat(),
        #     "tenantUrl": integration.get("tenantUrl", ""),
        #     "taskId": task_id
        # }
        response_data = {
            "dataStatus": "empty",
            "message": "Development in progress.", # User requested message
            "timestamp": datetime.now().isoformat(),
            "tenantUrl": integration.get("tenantUrl", ""),
            "taskId": None # Or an empty string
        }

        return response_data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_integration_profiles: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

# @router.get("/{integration_id}/guest-user-risks") // Removed Guest User Risks endpoint
# async def get_integration_guest_user_risks(
#     integration_id: str = Path(..., description="Integration ID"),
#     force_refresh: bool = Query(False, description="Force refresh data"),
#     current_user: Dict[str, Any] = Depends(get_current_user)
# ):
#     """
#     Get integration guest user profile risks
#     """
#     logger.info("Getting integration guest user profile risks...")
#     logger.info(f"Integration ID: {integration_id}")
#     logger.info(f"Force refresh: {force_refresh}")
# 
#     try:
#         # Get integration by ID
#         if is_local_dev():
#             # For local development, try to get integration from Azure Table Storage
#             try:
#                 integration_repo = get_table_storage_repository("Integrations")
#                 if not integration_repo:
#                     logger.error("Integration table repository not available")
#                     raise HTTPException(status_code=500, detail="Integration repository not available")
# 
#                 # Get integration by ID
#                 integration = integration_repo.get_entity("integration", integration_id)
#                 if not integration:
#                     logger.warning(f"Integration not found for ID: {integration_id}")
#                     raise HTTPException(status_code=404, detail=f"Integration not found for ID: {integration_id}")
# 
#                 # Convert to dictionary
#                 integration = {
#                     "id": integration.get("RowKey"),
#                     "name": integration.get("Name", ""),
#                     "tenantUrl": integration.get("TenantUrl", ""),
#                     "type": integration.get("Type", "Salesforce"),
#                     "description": integration.get("Description", ""),
#                     "environment": integration.get("Environment", "production"),
#                     "isActive": integration.get("IsActive", True),
#                     "lastScan": integration.get("LastScan", ""),
#                     "createdAt": integration.get("CreatedAt", ""),
#                     "userEmail": integration.get("UserEmail", "")
#                 }
#                 logger.info(f"Found integration: {integration}")
#             except Exception as e:
#                 logger.error(f"Error getting integration from Azure Table Storage: {str(e)}")
#                 raise HTTPException(status_code=500, detail=f"Error getting integration: {str(e)}")
#         else:
#             # Use SQL Database for production
#             integration_repo = get_integration_sql_repo()
#             if not integration_repo:
#                 logger.error("Integration SQL repository not available")
#                 raise HTTPException(status_code=500, detail="Integration repository not available")
# 
#             # Get integration by ID
#             query = """
#             SELECT Id, Name, TenantUrl, Type, Description, Environment, IsActive, LastScan, CreatedAt, UserEmail
#             FROM App_Organization
#             WHERE Id = ?
#             """
#             results = integration_repo.execute_query(query, (integration_id,))
# 
#             if not results or len(results) == 0:
#                 logger.warning(f"Integration not found for ID: {integration_id}")
#                 raise HTTPException(status_code=404, detail=f"Integration not found for ID: {integration_id}")
# 
#             # Convert to dictionary
#             integration = {
#                 "id": results[0][0],
#                 "name": results[0][1],
#                 "tenantUrl": results[0][2],
#                 "type": results[0][3],
#                 "description": results[0][4],
#                 "environment": results[0][5],
#                 "isActive": results[0][6],
#                 "lastScan": results[0][7].isoformat() if results[0][7] else None,
#                 "createdAt": results[0][8].isoformat() if results[0][8] else None,
#                 "userEmail": results[0][9]
#             }
#             logger.info(f"Found integration: {integration}")
# 
#         # Check if integration is active
#         if not integration.get("isActive", False):
#             raise HTTPException(status_code=400, detail="Integration is not active")
# 
#         # Try to get data from database first
#         if not force_refresh:
#             # Get integration ID
#             integration_id = integration.get("id")
# 
#             # Query the database for guest user risks data
#             if is_local_dev():
#                 # For local development, try to get data from Azure Table Storage
#                 try:
#                     # Initialize the GuestUserRisks table repository
#                     guest_user_risks_repo = get_table_storage_repository("GuestUserRisks")
#                     if not guest_user_risks_repo:
#                         logger.error("GuestUserRisks table repository not available")
#                         return {
#                             "dataStatus": "empty",
#                             "message": "No guest user profile risks data available. Please sync to fetch data.",
#                             "timestamp": datetime.now().isoformat(),
#                             "tenantUrl": integration.get("tenantUrl", "")
#                         }
# 
#                     # Query for the guest user risks data for this organization
#                     filter_query = f"PartitionKey eq '{integration_id}'"
#                     logger.info(f"Querying GuestUserRisks table with filter: {filter_query}")
#                     entities = guest_user_risks_repo.query_entities(filter_query)
#                     logger.info(f"Found {len(entities) if entities else 0} guest user risks entities")
# 
#                     if not entities or len(entities) == 0:
#                         # No data found, return a response indicating data needs to be fetched
#                         logger.info(f"No guest user risks data found for integration {integration_id}")
#                         return {
#                             "dataStatus": "empty",
#                             "message": "No guest user profile risks data available. Please sync to fetch data.",
#                             "timestamp": datetime.now().isoformat(),
#                             "tenantUrl": integration.get("tenantUrl", "")
#                         }
# 
#                     # Extract risks from the entities
#                     risks = []
#                     last_updated = None
# 
#                     for entity in entities:
#                         risk = {
#                             "riskType": entity.get("RiskType", "UNKNOWN_RISK"),
#                             "description": entity.get("Description", "Unknown Risk"),
#                             "impact": entity.get("Impact", "Unknown"),
#                             "recommendation": entity.get("Recommendation", "Unknown")
#                         }
#                         risks.append(risk)
# 
#                         # Track the latest update time
#                         entity_timestamp = entity.get("Timestamp")
#                         if entity_timestamp and (last_updated is None or entity_timestamp > last_updated):
#                             last_updated = entity_timestamp
# 
#                     # Create the response data
#                     guest_user_risks_data = {
#                         "dataStatus": "available",
#                         "risks": risks,
#                         "lastUpdated": last_updated.isoformat() if isinstance(last_updated, datetime) else (datetime.now().isoformat() if last_updated is None else last_updated)
#                     }
#                     logger.info(f"Created guest user risks data response with {len(risks)} risks")
#                 except Exception as e:
#                     logger.error(f"Error retrieving guest user risks data from Azure Table Storage: {str(e)}")
#                     # Return empty data response
#                     return {
#                         "dataStatus": "empty",
#                         "message": "No guest user profile risks data available. Please sync to fetch data.",
#                         "timestamp": datetime.now().isoformat(),
#                         "tenantUrl": integration.get("tenantUrl", "")
#                     }
#             else:
#                 # Use SQL Database for production
#                 guest_user_risks_repo = get_guest_user_risks_repo()
#                 if not guest_user_risks_repo:
#                     logger.error("Guest user risks SQL repository not available")
#                     raise HTTPException(status_code=500, detail="Guest user risks repository not available")
# 
#                 # Query for the latest guest user risks data
#                 query = """
#                 SELECT g.RiskType, g.Description, g.Impact, g.Recommendation, g.LastUpdated
#                 FROM App_GuestUserRisks g
#                 INNER JOIN App_ExecutionLog e ON g.ExecutionLogId = e.Id
#                 WHERE g.OrgId = ? AND e.Status = 'Completed'
#                 ORDER BY e.StartTime DESC
#                 """
#                 results = guest_user_risks_repo.execute_query(query, (integration_id,))
# 
#                 if not results or len(results) == 0:
#                     # No data found, return a response indicating data needs to be fetched
#                     return {
#                         "dataStatus": "empty",
#                         "message": "No guest user profile risks data available. Please sync to fetch data.",
#                         "timestamp": datetime.now().isoformat(),
#                         "tenantUrl": integration.get("tenantUrl", "")
#                     }
# 
#                 # Process risks
#                 risks = []
#                 last_updated = None
#                 for risk_row in results:
#                     risk_type, description, impact, recommendation, updated_at = risk_row
#                     risks.append({
#                         "riskType": risk_type,
#                         "description": description,
#                         "impact": impact,
#                         "recommendation": recommendation
#                     })
#                     if not last_updated:
#                         last_updated = updated_at
# 
#                 # Create the response data
#                 guest_user_risks_data = {
#                     "dataStatus": "available",
#                     "risks": risks,
#                     "lastUpdated": last_updated.isoformat() if isinstance(last_updated, datetime) else last_updated
#                 }
# 
#             # Return the guest user risks data
#             return guest_user_risks_data
# 
#         # If force refresh or no data in database, trigger a background fetch
#         # Create a background processor
#         # processor = BackgroundProcessor()
# 
#         # Enqueue a task
#         # task_id = processor.enqueue_task(
#         #     task_type=TASK_TYPE_GUEST_USER_RISKS,
#         #     org_id=integration.get("id"),
#         #     user_id=current_user.get("id")
#         # )
# 
#         # if not task_id:
#         #     raise HTTPException(status_code=500, detail="Failed to enqueue task")
# 
#         # Return pending status
#         # response_data = {
#         #     "dataStatus": "pending",
#         #     "message": "The guest user risks data will be available shortly. Please refresh to check if data is available.",
#         #     "timestamp": datetime.now().isoformat(),
#         #     "tenantUrl": integration.get("tenantUrl", ""),
#         #     "taskId": task_id
#         # }
#         response_data = {
#             "dataStatus": "empty",
#             "message": "Development in progress.", # User requested message
#             "timestamp": datetime.now().isoformat(),
#             "tenantUrl": integration.get("tenantUrl", ""),
#             "taskId": None # Or an empty string
#         }
# 
#         return response_data
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"Error in get_integration_guest_user_risks: {str(e)}")
#         import traceback
#         logger.error(f"Traceback: {traceback.format_exc()}")
#         raise HTTPException(status_code=500, detail=str(e))

# @router.get("/{integration_id}/pmd-issues") // Removed PMD Issues endpoint
# async def get_integration_pmd_issues(
#     integration_id: str = Path(..., description="Integration ID"),
#     force_refresh: bool = Query(False, description="Force refresh data"),
#     current_user: Dict[str, Any] = Depends(get_current_user)
# ):
#     """
#     Get integration PMD issues (development and security issues)
#     """
#     logger.info("Getting integration PMD issues...")
#     logger.info(f"Integration ID: {integration_id}")
#     logger.info(f"Force refresh: {force_refresh}")
# 
#     try:
#         # Get integration by ID
#         if is_local_dev():
#             # For local development, try to get integration from Azure Table Storage
#             try:
#                 integration_repo = get_table_storage_repository("Integrations")
#                 if not integration_repo:
#                     logger.error("Integration table repository not available")
#                     raise HTTPException(status_code=500, detail="Integration repository not available")
# 
#                 # Get integration by ID
#                 integration = integration_repo.get_entity("integration", integration_id)
#                 if not integration:
#                     logger.warning(f"Integration not found for ID: {integration_id}")
#                     raise HTTPException(status_code=404, detail=f"Integration not found for ID: {integration_id}")
# 
#                 # Convert to dictionary
#                 integration = {
#                     "id": integration.get("RowKey"),
#                     "name": integration.get("Name", ""),
#                     "tenantUrl": integration.get("TenantUrl", ""),
#                     "type": integration.get("Type", "Salesforce"),
#                     "description": integration.get("Description", ""),
#                     "environment": integration.get("Environment", "production"),
#                     "isActive": integration.get("IsActive", True),
#                     "lastScan": integration.get("LastScan", ""),
#                     "createdAt": integration.get("CreatedAt", ""),
#                     "userEmail": integration.get("UserEmail", "")
#                 }
#                 logger.info(f"Found integration: {integration}")
#             except Exception as e:
#                 logger.error(f"Error getting integration from Azure Table Storage: {str(e)}")
#                 raise HTTPException(status_code=500, detail=f"Error getting integration: {str(e)}")
#         else:
#             # Use SQL Database for production
#             integration_repo = get_integration_sql_repo()
#             if not integration_repo:
#                 logger.error("Integration SQL repository not available")
#                 raise HTTPException(status_code=500, detail="Integration repository not available")
# 
#             # Get integration by ID
#             query = """
#             SELECT Id, Name, TenantUrl, Type, Description, Environment, IsActive, LastScan, CreatedAt, UserEmail
#             FROM App_Organization
#             WHERE Id = ?
#             """
#             results = integration_repo.execute_query(query, (integration_id,))
# 
#             if not results or len(results) == 0:
#                 logger.warning(f"Integration not found for ID: {integration_id}")
#                 raise HTTPException(status_code=404, detail=f"Integration not found for ID: {integration_id}")
# 
#             # Convert to dictionary
#             integration = {
#                 "id": results[0][0],
#                 "name": results[0][1],
#                 "tenantUrl": results[0][2],
#                 "type": results[0][3],
#                 "description": results[0][4],
#                 "environment": results[0][5],
#                 "isActive": results[0][6],
#                 "lastScan": results[0][7].isoformat() if results[0][7] else None,
#                 "createdAt": results[0][8].isoformat() if results[0][8] else None,
#                 "userEmail": results[0][9]
#             }
#             logger.info(f"Found integration: {integration}")
# 
#         # Check if integration is active
#         if not integration.get("isActive", False):
#             raise HTTPException(status_code=400, detail="Integration is not active")
# 
#         # Check if we have PMD issues data in the database
#         pmd_issues_data = None
#         if not force_refresh and not is_local_dev():
#             pmd_issues_repo = get_pmd_issues_repo()
#             if pmd_issues_repo:
#                 # Query for PMD issues data
#                 query = """
#                 SELECT Id, OrgId, Data, CreatedAt, UpdatedAt
#                 FROM App_PMDIssues
#                 WHERE OrgId = ?
#                 ORDER BY UpdatedAt DESC
#                 """
#                 results = pmd_issues_repo.execute_query(query, (integration.get("id"),))
# 
#                 if results and len(results) > 0:
#                     # Parse the data
#                     try:
#                         pmd_issues_json = results[0][2]  # Data column
#                         pmd_issues_data = json.loads(pmd_issues_json)
# 
#                         # Add metadata
#                         pmd_issues_data["lastUpdated"] = results[0][4]  # UpdatedAt column
#                         pmd_issues_data["tenantUrl"] = integration.get("tenantUrl", "")
#                         pmd_issues_data["dataStatus"] = "available"
# 
#                         logger.info(f"Found PMD issues data for integration {integration.get('id')}")
#                     except Exception as parse_error:
#                         logger.error(f"Error parsing PMD issues data: {str(parse_error)}")
#                         pmd_issues_data = None
# 
#         # If we're in local development mode and don't have data, try to get it from Azure Table Storage
#         if is_local_dev() and not pmd_issues_data:
#             try:
#                 # Get integration ID
#                 org_id = integration.get("id")
# 
#                 # Initialize the PMDIssues table repository
#                 pmd_issues_repo = get_table_storage_repository("PMDIssues")
#                 if not pmd_issues_repo:
#                     logger.error("PMDIssues table repository not available")
#                     return {
#                         "dataStatus": "empty",
#                         "message": "No PMD issues data available. Please sync to fetch data.",
#                         "timestamp": datetime.now().isoformat(),
#                         "tenantUrl": integration.get("tenantUrl", "")
#                     }
# 
#                 # Query for the PMD issues data for this organization
#                 filter_query = f"PartitionKey eq '{org_id}'"
#                 logger.info(f"Querying PMDIssues table with filter: {filter_query}")
#                 entities = pmd_issues_repo.query_entities(filter_query)
#                 logger.info(f"Found {len(entities) if entities else 0} PMD issues entities")
# 
#                 if entities and len(entities) > 0:
#                     # Sort entities by timestamp (RowKey) to get the latest
#                     sorted_entities = sorted(entities, key=lambda x: x.get('RowKey', ''), reverse=True)
#                     latest_entity = sorted_entities[0]
#                     logger.info(f"Latest entity RowKey: {latest_entity.get('RowKey')}")
# 
#                     # Parse the data
#                     try:
#                         pmd_issues_json = latest_entity.get('Data')
#                         if pmd_issues_json:
#                             pmd_issues_data = json.loads(pmd_issues_json)
# 
#                             # Add metadata
#                             pmd_issues_data["lastUpdated"] = latest_entity.get('Timestamp', datetime.now().isoformat())
#                             pmd_issues_data["tenantUrl"] = integration.get("tenantUrl", "")
#                             pmd_issues_data["dataStatus"] = "available"
# 
#                             logger.info(f"Found PMD issues data for integration {org_id}")
#                         else:
#                             logger.warning("PMD issues entity found but Data field is empty")
#                     except Exception as parse_error:
#                         logger.error(f"Error parsing PMD issues data: {str(parse_error)}")
#                 else:
#                     # No data found, return a response indicating data needs to be fetched
#                     return {
#                         "dataStatus": "empty",
#                         "message": "No PMD issues data available. Please sync to fetch data.",
#                         "timestamp": datetime.now().isoformat(),
#                         "tenantUrl": integration.get("tenantUrl", "")
#                     }
#             except Exception as e:
#                 logger.error(f"Error retrieving PMD issues data from Azure Table Storage: {str(e)}")
#                 # Return empty data response
#                 return {
#                     "dataStatus": "empty",
#                     "message": "No PMD issues data available. Please sync to fetch data.",
#                     "timestamp": datetime.now().isoformat(),
#                     "tenantUrl": integration.get("tenantUrl", "")
#                 }
# 
#         # If we have data, return it
#         if pmd_issues_data:
#             logger.info("Returning PMD issues data")
#             return pmd_issues_data
# 
#         # If force refresh or no data in database, trigger a background fetch
#         # Create a background processor
#         # processor = BackgroundProcessor()
# 
#         # Enqueue a task
#         # task_id = processor.enqueue_task(
#         #     task_type=TASK_TYPE_PMD_ISSUES,
#         #     org_id=integration.get("id"),
#         #     user_id=current_user.get("id")
#         # )
# 
#         # if not task_id:
#         #     raise HTTPException(status_code=500, detail="Failed to enqueue task")
# 
#         # Return pending status
#         # response_data = {
#         #     "dataStatus": "pending",
#         #     "message": "The PMD issues data will be available shortly. Please refresh to check if data is available.",
#         #     "timestamp": datetime.now().isoformat(),
#         #     "tenantUrl": integration.get("tenantUrl", ""),
#         #     "taskId": task_id
#         # }
#         response_data = {
#             "dataStatus": "empty",
#             "message": "Development in progress.", # User requested message
#             "timestamp": datetime.now().isoformat(),
#             "tenantUrl": integration.get("tenantUrl", ""),
#             "taskId": None # Or an empty string
#         }
# 
#         return response_data
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"Error in get_integration_pmd_issues: {str(e)}")
#         import traceback
#         logger.error(f"Traceback: {traceback.format_exc()}")
#         raise HTTPException(status_code=500, detail=str(e))
