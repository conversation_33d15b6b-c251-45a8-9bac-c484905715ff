import requests
import os
import json

def make_request(resource, query_parameters=""):
    """
    Make a request to the deployed function app

    Args:
        resource: The endpoint to call (e.g., "api/health")
        query_parameters: Query parameters to include

    Returns:
        Response object
    """
    # Get base URL from environment variable or use default
    base_url = os.environ.get("base_url", "https://func-atomsec-sfdc-dev.azurewebsites.net/")

    # Ensure base_url ends with a slash
    if not base_url.endswith("/"):
        base_url += "/"

    # Make the request
    response = requests.get(f'{base_url}{resource}?{query_parameters}')
    return response

def make_request_local(resource, query_parameters=""):
    """
    Make a request to the local function app

    Args:
        resource: The endpoint to call (e.g., "api/health")
        query_parameters: Query parameters to include

    Returns:
        Response object
    """
    # Use local URL for testing
    base_url = "http://localhost:7071/"
    response = requests.get(f'{base_url}{resource}?{query_parameters}')
    return response

def make_post_request(resource, data, query_parameters=""):
    """
    Make a POST request to the deployed function app

    Args:
        resource: The endpoint to call (e.g., "api/add_profile_metadata")
        data: JSON data to send in the request body
        query_parameters: Query parameters to include

    Returns:
        Response object
    """
    # Get base URL from environment variable or use default
    base_url = os.environ.get("base_url", "https://func-atomsec-sfdc-dev.azurewebsites.net/")

    # Ensure base_url ends with a slash
    if not base_url.endswith("/"):
        base_url += "/"

    # Make the request
    headers = {"Content-Type": "application/json"}
    response = requests.post(
        f'{base_url}{resource}?{query_parameters}',
        data=json.dumps(data),
        headers=headers
    )
    return response