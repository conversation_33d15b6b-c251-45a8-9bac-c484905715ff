"""Tests for the WrapperFunction example (FastAPI integration)

NOTE: These tests are disabled by default since WrapperFunction has been moved to examples/
and is not actively deployed. To run these tests, you need to:

1. Uncomment the FastAPI integration in function_app.py
2. Deploy the application
3. Uncomment the test functions below
"""

from tests.make_requests import make_request
import pytest

@pytest.mark.skip(reason="WrapperFunction moved to examples/ and not actively deployed")
def test_fastapi_sample():
    response = make_request("sample")
    assert response.status_code == 200
    assert "info" in response.json()

@pytest.mark.skip(reason="WrapperFunction moved to examples/ and not actively deployed")
def test_fastapi_hello():
    response = make_request("hello/x")
    assert response.status_code == 200
    assert response.json() == {"name": "x"}

@pytest.mark.skip(reason="WrapperFunction moved to examples/ and not actively deployed")
def test_fastapi_hello_different_name():
    response = make_request("hello/xyz")
    assert response.status_code == 200
    assert response.json() == {"name": "xyz"}