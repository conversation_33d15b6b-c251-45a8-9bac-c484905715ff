"""
Tests for the FastAPI integration

These tests verify that the FastAPI integration is working correctly.
"""

import pytest
import requests
import json
from tests.make_requests import make_request

# Skip these tests if the FastAPI integration is not enabled
# Remove the skip decorator when the integration is enabled
@pytest.mark.skip(reason="FastAPI integration not yet enabled in production")
def test_fastapi_docs():
    """Test that the FastAPI documentation is accessible"""
    response = make_request("docs")
    assert response.status_code == 200
    assert "text/html" in response.headers.get("Content-Type", "")

@pytest.mark.skip(reason="FastAPI integration not yet enabled in production")
def test_fastapi_openapi():
    """Test that the OpenAPI schema is accessible"""
    response = make_request("openapi.json")
    assert response.status_code == 200
    assert "application/json" in response.headers.get("Content-Type", "")
    
    # Verify that the schema contains the expected endpoints
    schema = response.json()
    assert "paths" in schema
    assert "/api/accounts" in schema["paths"]
    assert "/api/roles" in schema["paths"]

@pytest.mark.skip(reason="FastAPI integration not yet enabled in production")
def test_fastapi_health():
    """Test the FastAPI health endpoint"""
    response = make_request("health")
    assert response.status_code == 200
    assert "application/json" in response.headers.get("Content-Type", "")
    
    data = response.json()
    assert "status" in data
    assert data["status"] == "healthy"

@pytest.mark.skip(reason="FastAPI integration not yet enabled in production")
def test_fastapi_accounts():
    """Test the FastAPI accounts endpoint"""
    response = make_request("accounts")
    assert response.status_code == 200
    assert "application/json" in response.headers.get("Content-Type", "")
    
    data = response.json()
    assert "success" in data
    assert data["success"] == True
    assert "data" in data
    assert isinstance(data["data"], list)

@pytest.mark.skip(reason="FastAPI integration not yet enabled in production")
def test_fastapi_create_account():
    """Test creating an account through FastAPI"""
    # Generate a unique account name
    import random
    account_name = f"Test Account {random.randint(1000, 9999)}"
    
    # Create the account
    response = make_request(
        "accounts", 
        method="POST",
        json={"name": account_name}
    )
    
    assert response.status_code == 201
    assert "application/json" in response.headers.get("Content-Type", "")
    
    data = response.json()
    assert "success" in data
    assert data["success"] == True
    assert "data" in data
    assert data["data"]["Name"] == account_name
    assert "ID" in data["data"]
    
    # Verify that the account was created
    account_id = data["data"]["ID"]
    response = make_request("accounts")
    
    data = response.json()
    account_ids = [account["ID"] for account in data["data"]]
    assert account_id in account_ids
