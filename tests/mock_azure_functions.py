"""
Mock Azure Functions module for testing

This module provides mock implementations of Azure Functions classes and functions
for use in testing without requiring the actual azure-functions package.
"""

class HttpRequest:
    """Mock HttpRequest class"""
    def __init__(self, method="GET", url="/", body=None, headers=None, route_params=None, params=None):
        self.method = method
        self.url = url
        self._body = body or b""
        self.headers = headers or {}
        self.route_params = route_params or {}
        self.params = params or {}

    def get_json(self):
        """Get JSON from request body"""
        import json
        return json.loads(self._body.decode())

    def get_body(self):
        """Get request body"""
        return self._body


class HttpResponse:
    """Mock HttpResponse class"""
    def __init__(self, body=None, status_code=200, headers=None, mimetype=None):
        self._body = body
        self.status_code = status_code
        self.headers = headers or {}
        self.mimetype = mimetype

    def get_body(self):
        """Get response body"""
        return self._body


class Blueprint:
    """Mock Blueprint class"""
    def __init__(self):
        self.routes = []

    def route(self, route, methods=None):
        """Route decorator"""
        methods = methods or ["GET"]

        def decorator(func):
            self.routes.append((route, methods, func))
            return func

        return decorator


# Define AuthLevel enum
class AuthLevel:
    """Mock AuthLevel enum"""
    ANONYMOUS = "anonymous"
    FUNCTION = "function"
    ADMIN = "admin"


# Define FunctionApp class
class FunctionApp:
    """Mock FunctionApp class"""
    def __init__(self, http_auth_level=None):
        self.http_auth_level = http_auth_level or AuthLevel.ANONYMOUS
        self.functions = []

    def register_functions(self, blueprint):
        """Register functions from a blueprint"""
        self.functions.extend(blueprint.routes)

    def route(self, route, methods=None, auth_level=None):
        """Route decorator"""
        methods = methods or ["GET"]
        auth_level = auth_level or self.http_auth_level

        def decorator(func):
            self.functions.append((route, methods, func))
            return func

        return decorator
