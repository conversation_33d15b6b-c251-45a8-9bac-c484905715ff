"""
Tests for the authentication blueprint

This module contains tests for the authentication functions.
"""

import unittest
import json
import secrets
from unittest.mock import patch, MagicMock

# Use mock azure.functions if the real one is not available
try:
    import azure.functions as func
except ImportError:
    # Use our mock implementation
    from tests.mock_azure_functions import HttpRequest, HttpResponse, Blueprint, FunctionApp

    # Create mock module
    class MockAzureFunctions:
        def __init__(self):
            self.HttpRequest = HttpRequest
            self.HttpResponse = HttpResponse
            self.Blueprint = Blueprint
            self.FunctionApp = FunctionApp
            self.AuthLevel = type('AuthLevel', (), {'ANONYMOUS': 'anonymous'})

    # Create mock module
    func = MockAzureFunctions()

# Import the blueprint
from blueprints.auth import bp, signup, login, refresh_token, get_user, hash_password
from shared.user_repository import hash_password as repo_hash_password

class TestAuth(unittest.TestCase):
    """Test cases for the authentication blueprint"""

    def test_hash_password(self):
        """Test password hashing"""
        # Test that the same password produces the same hash with the same salt
        password = "test_password"
        salt = secrets.token_hex(16)

        hash1 = repo_hash_password(password, salt)
        hash2 = repo_hash_password(password, salt)
        self.assertEqual(hash1, hash2)

        # Test that different passwords produce different hashes
        password2 = "different_password"
        hash3 = repo_hash_password(password2, salt)
        self.assertNotEqual(hash1, hash3)

    @patch('shared.user_repository.create_user_account')
    @patch('shared.user_repository.create_user_login')
    def test_create_user_from_repository(self, mock_create_user_login, mock_create_user_account):
        """Test user creation from repository"""
        from shared.user_repository import create_user
        import uuid

        # Generate a unique email for this test
        unique_email = f"test_{uuid.uuid4().hex[:8]}@example.com"

        # Mock create_user_account to return a user ID
        mock_create_user_account.return_value = 12345

        # Mock create_user_login to return True (success)
        mock_create_user_login.return_value = True

        # Test user creation
        user_id = create_user(
            email=unique_email,
            password="password123",
            first_name="Test",
            last_name="User"
        )

        # Assert that the user ID is returned
        self.assertEqual(user_id, 12345)

        # Verify that create_user_account was called with the correct parameters
        mock_create_user_account.assert_called_once_with(
            email=unique_email,
            first_name="Test",
            middle_name="",
            last_name="User",
            dob=None,
            contact=None,
            state="",
            country="",
            organization=""
        )

        # Verify that create_user_login was called with the correct parameters
        mock_create_user_login.assert_called_once_with(
            user_id=12345,
            username=unique_email,
            password="password123",
            algorithm_id=1
        )

    @patch('blueprints.auth.get_user_account_by_email')
    def test_get_user(self, mock_get_user_account_by_email):
        """Test getting a user"""
        # Mock the user account
        mock_user_account = MagicMock()
        mock_user_account.Email = "<EMAIL>"
        mock_user_account.FirstName = "Test"
        mock_user_account.LastName = "User"
        mock_user_account.DoB = None
        mock_user_account.Contact = "123-456-7890"
        mock_user_account.State = "CA"
        mock_user_account.Country = "USA"
        mock_user_account.UserId = 123

        mock_get_user_account_by_email.return_value = mock_user_account

        # Test getting a user
        user = get_user("<EMAIL>")
        self.assertIsNotNone(user)
        self.assertEqual(user["Email"], "<EMAIL>")
        self.assertEqual(user["Name"], "Test User")
        self.assertEqual(user["UserId"], 123)

        # Test getting a non-existent user
        mock_get_user_account_by_email.return_value = None
        user = get_user("<EMAIL>")
        self.assertIsNone(user)

    @patch('blueprints.auth.create_user')
    @patch('blueprints.auth.get_user')
    @patch('blueprints.auth.store_refresh_token')
    @patch('blueprints.auth.create_access_token')
    @patch('blueprints.auth.create_refresh_token')
    def test_signup(self, mock_create_refresh_token, mock_create_access_token,
                   mock_store_refresh_token, mock_get_user, mock_create_user):
        """Test signup endpoint"""
        # Mock dependencies
        mock_get_user.return_value = None  # User doesn't exist
        mock_create_user.return_value = 123  # User ID
        mock_store_refresh_token.return_value = True
        mock_create_access_token.return_value = "test_access_token"
        mock_create_refresh_token.return_value = "test_refresh_token"

        # Create a mock request
        req_body = {
            "email": "<EMAIL>",
            "password": "password123",
            "name": "Test User"
        }
        req = func.HttpRequest(
            method="POST",
            url="/api/auth/signup",
            body=json.dumps(req_body).encode(),
            headers={"Content-Type": "application/json"}
        )

        # Call the function
        response = signup(req)

        # Verify the response
        self.assertEqual(response.status_code, 200)

        # Parse the response body
        response_body = json.loads(response.get_body())

        # Check if the response has the expected structure
        if "success" in response_body:
            # Old response format
            self.assertTrue(response_body["success"])
            self.assertEqual(response_body["statusCode"], 200)
            self.assertTrue("access_token" in response_body["data"])
            self.assertTrue("refresh_token" in response_body["data"])
            self.assertEqual(response_body["data"]["user"]["email"], "<EMAIL>")
        else:
            # New response format
            self.assertTrue("access_token" in response_body)
            self.assertTrue("refresh_token" in response_body)
            self.assertEqual(response_body["user"]["email"], "<EMAIL>")

        # Test signup with existing user
        mock_get_user.return_value = {"Email": "<EMAIL>"}
        response = signup(req)
        self.assertEqual(response.status_code, 400)

if __name__ == '__main__':
    unittest.main()
