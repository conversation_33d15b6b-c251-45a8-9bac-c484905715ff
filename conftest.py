"""
Root conftest.py for pytest

This file is automatically loaded by pytest before any tests are run.
It sets up the Python path to include the necessary modules.
"""

import os
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add the current directory to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    logger.info(f"Adding current directory to Python path: {current_dir}")
    sys.path.insert(0, current_dir)

# Set environment variables for testing
os.environ["PYTEST_RUNNING"] = "true"
os.environ["ATOMSEC_TEST_ENV"] = "true"
os.environ["IS_LOCAL_DEV"] = "true"

logger.info(f"Python path: {sys.path}")
logger.info(f"Current directory: {os.getcwd()}")
