#!/usr/bin/env python3
"""
Simple test script for PMD components to verify they work correctly.
"""

import sys
import os
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_pmd_components():
    """Test that PMD components can be imported and initialized"""
    try:
        # Test imports
        from pmd_components.pmd_rules_config import PMDRulesConfig
        from pmd_components.pmd_scanner import PMDScanner
        from pmd_components.pmd_blob_handler import PMDBlobHandler
        from pmd_components.pmd_results_processor import PMDResultsProcessor
        
        logger.info("✓ All PMD components imported successfully")
        
        # Test initialization
        config = PMDRulesConfig()
        scanner = PMDScanner()
        blob_handler = PMDBlobHandler()
        processor = PMDResultsProcessor()
        
        logger.info("✓ All PMD components initialized successfully")
        
        # Test basic functionality
        rules_info = config.get_ruleset_info()
        logger.info(f"✓ Rules config: {rules_info['total_count']} rulesets available")
        
        scanner_info = scanner.get_scanner_info()
        logger.info(f"✓ Scanner environment: {scanner_info['environment']}")
        
        logger.info("✓ All PMD components are working correctly")
        return True
        
    except Exception as e:
        logger.error(f"✗ PMD components test failed: {e}")
        return False

def test_pmd_task_import():
    """Test that the PMD task can import the components"""
    try:
        # Test importing the task
        from task_processor.tasks.pmd_task import process_pmd_task
        
        logger.info("✓ PMD task imported successfully")
        logger.info("✓ PMD task can access all components")
        return True
        
    except Exception as e:
        logger.error(f"✗ PMD task import test failed: {e}")
        return False

def main():
    """Run all tests"""
    logger.info("=" * 50)
    logger.info("PMD COMPONENTS TEST")
    logger.info("=" * 50)
    
    tests = [
        ("PMD Components", test_pmd_components),
        ("PMD Task Import", test_pmd_task_import)
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        logger.info(f"\n--- {name} Test ---")
        if test_func():
            passed += 1
            logger.info(f"✓ {name} test passed")
        else:
            logger.error(f"✗ {name} test failed")
    
    logger.info("\n" + "=" * 50)
    logger.info(f"TEST RESULTS: {passed}/{total} tests passed")
    logger.info("=" * 50)
    
    if passed == total:
        logger.info("🎉 All tests passed! PMD components are ready for use.")
        logger.info("✅ Components can be imported and initialized")
        logger.info("✅ PMD task can access all components")
        logger.info("✅ Ready for task execution")
        return 0
    else:
        logger.error("❌ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 