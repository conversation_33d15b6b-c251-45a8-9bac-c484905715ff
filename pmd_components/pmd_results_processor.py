"""
PMD Results Processor Module

This module handles processing and storing PMD scan results in the dedicated PMDScans table.
It includes comprehensive PMD-specific columns for detailed analysis.
"""

import logging
import json
from typing import Dict, Any, List, Optional
from datetime import datetime

# Import from the parent directory's shared module
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from shared.data_access import get_pmd_scans_table_repo

logger = logging.getLogger(__name__)

class PMDResultsProcessor:
    """Processor for PMD scan results"""
    
    def __init__(self):
        """Initialize PMD results processor"""
        self.pmd_scans_repo = get_pmd_scans_table_repo()
    
    def _get_weakness_type(self, rule_name: str, category: str) -> tuple[str, str]:
        """
        Map PMD rule name and category to a standard weakness type and description
        
        Args:
            rule_name: PMD rule name
            category: PMD category
            
        Returns:
            Tuple of (weakness_type, description)
        """
        # Security-related rules
        security_rules = {
            'ApexBadCrypto': ('Security Best Practice', 'Cryptographic vulnerabilities and insecure encryption practices'),
            'ApexCRUDViolation': ('Security Best Practice', 'CRUD permission violations and unauthorized data access'),
            'ApexDangerousMethods': ('Security Best Practice', 'Use of dangerous methods that can lead to security vulnerabilities'),
            'ApexInsecureEndpoint': ('Security Best Practice', 'Insecure endpoint configurations and API security issues'),
            'ApexOpenRedirect': ('Security Best Practice', 'Open redirect vulnerabilities that can be exploited for phishing'),
            'ApexSharingViolations': ('Security Best Practice', 'Sharing and security violations in data access patterns'),
            'ApexSOQLInjection': ('Security Best Practice', 'SOQL injection vulnerabilities from unvalidated user input'),
            'ApexSuggestUsingNamedCred': ('Security Best Practice', 'Recommendations for using named credentials for secure authentication'),
            'ApexXSSFromEscapeFalse': ('Security Best Practice', 'Cross-site scripting vulnerabilities from unescaped output'),
            'ApexXSSFromURLParam': ('Security Best Practice', 'Cross-site scripting vulnerabilities from URL parameters')
        }
        
        # Performance-related rules
        performance_rules = {
            'ApexSOQLInLoop': ('Performance Best Practice', 'SOQL queries inside loops causing performance degradation'),
            'ApexDMLInLoop': ('Performance Best Practice', 'DML operations inside loops causing governor limit issues'),
            'ApexCPUtimeLimit': ('Performance Best Practice', 'CPU time limit violations and inefficient processing'),
            'ApexHeapSizeLimit': ('Performance Best Practice', 'Heap size limit violations and memory management issues'),
            'ApexExcessiveParameterList': ('Performance Best Practice', 'Methods with too many parameters affecting maintainability'),
            'ApexExcessiveClassLength': ('Performance Best Practice', 'Classes that are too long and violate single responsibility principle'),
            'ApexExcessiveMethodLength': ('Performance Best Practice', 'Methods that are too long and difficult to maintain'),
            'ApexExcessivePublicCount': ('Performance Best Practice', 'Classes with too many public methods violating encapsulation')
        }
        
        # Code style rules
        code_style_rules = {
            'ApexDoc': ('Development Best Practice', 'Missing or inadequate documentation for classes and methods'),
            'ApexUnitTestClassShouldHaveAsserts': ('Development Best Practice', 'Unit test classes missing assertions for proper validation'),
            'ApexUnitTestMethodShouldHaveIsTestAnnotation': ('Development Best Practice', 'Test methods missing proper test annotations'),
            'ApexUnitTestShouldNotUseSeeAllDataTrue': ('Development Best Practice', 'Test methods using SeeAllData=true which can cause test isolation issues'),
            'ApexVariableNamingConventions': ('Development Best Practice', 'Variables not following proper naming conventions'),
            'ApexMethodNamingConventions': ('Development Best Practice', 'Methods not following proper naming conventions'),
            'ApexClassNamingConventions': ('Development Best Practice', 'Classes not following proper naming conventions')
        }
        
        # Design rules
        design_rules = {
            'ApexExcessiveClassLength': ('Design Best Practice', 'Classes that are too long and violate single responsibility principle'),
            'ApexExcessiveMethodLength': ('Design Best Practice', 'Methods that are too long and difficult to understand'),
            'ApexExcessivePublicCount': ('Design Best Practice', 'Classes with too many public methods violating encapsulation'),
            'ApexExcessiveParameterList': ('Design Best Practice', 'Methods with too many parameters affecting maintainability'),
            'ApexCyclomaticComplexity': ('Design Best Practice', 'Methods with high cyclomatic complexity making them difficult to test and maintain'),
            'ApexCognitiveComplexity': ('Design Best Practice', 'Methods with high cognitive complexity making them difficult to understand')
        }
        
        # Error prone rules
        error_prone_rules = {
            'ApexUnusedLocalVariable': ('Code Quality', 'Local variables that are declared but never used'),
            'ApexUnusedPrivateMethod': ('Code Quality', 'Private methods that are defined but never called'),
            'ApexUnusedPrivateField': ('Code Quality', 'Private fields that are declared but never accessed'),
            'ApexUnusedParameter': ('Code Quality', 'Method parameters that are declared but never used'),
            'ApexUnusedGlobalVariable': ('Code Quality', 'Global variables that are declared but never used')
        }
        
        # Check specific rule names first
        if rule_name in security_rules:
            return security_rules[rule_name]
        elif rule_name in performance_rules:
            return performance_rules[rule_name]
        elif rule_name in code_style_rules:
            return code_style_rules[rule_name]
        elif rule_name in design_rules:
            return design_rules[rule_name]
        elif rule_name in error_prone_rules:
            return error_prone_rules[rule_name]
        
        # Fallback to category-based mapping
        category_lower = category.lower()
        if 'security' in category_lower:
            return ('Security Best Practice', 'Security-related issues that could lead to vulnerabilities or unauthorized access')
        elif 'performance' in category_lower:
            return ('Performance Best Practice', 'Performance-related issues that could affect system efficiency and governor limits')
        elif 'bestpractices' in category_lower or 'best_practices' in category_lower:
            return ('Development Best Practice', 'General development best practices and coding standards')
        elif 'codestyle' in category_lower or 'code_style' in category_lower:
            return ('Development Best Practice', 'Code style and formatting issues affecting readability and maintainability')
        elif 'design' in category_lower:
            return ('Design Best Practice', 'Software design issues affecting code structure and maintainability')
        elif 'errorprone' in category_lower or 'error_prone' in category_lower:
            return ('Code Quality', 'Code quality issues that could lead to errors or maintenance problems')
        elif 'documentation' in category_lower:
            return ('Development Best Practice', 'Documentation issues affecting code understanding and maintainability')
        else:
            return ('Code Quality', 'General code quality issues that should be addressed for better maintainability')  # Default fallback
    
    def process_findings(self, 
                        findings: List[Dict[str, Any]], 
                        org_id: str, 
                        task_id: str,
                        execution_log_id: Optional[str] = None,
                        blob_prefix: Optional[str] = None) -> Dict[str, Any]:
        """
        Process PMD findings and store them in the PMDScans table
        
        Args:
            findings: List of PMD findings from scanner
            org_id: Organization ID
            task_id: Task ID
            execution_log_id: Optional execution log ID
            blob_prefix: Optional blob prefix for context
            
        Returns:
            Dictionary containing processing results
        """
        logger.info(f"Processing {len(findings)} PMD findings for org_id: {org_id}")
        
        processed_count = 0
        error_count = 0
        processed_findings = []
        
        for finding in findings:
            try:
                processed_finding = self._create_pmd_scan_entity(
                    finding, org_id, task_id, execution_log_id, blob_prefix
                )
                
                # Insert into PMDScans table
                self.pmd_scans_repo.insert_entity(processed_finding)
                processed_count += 1
                processed_findings.append(processed_finding)
                
            except Exception as e:
                logger.error(f"Error processing finding: {e}")
                error_count += 1
                continue
        
        logger.info(f"Successfully processed {processed_count} findings, {error_count} errors")
        
        return {
            "processed_count": processed_count,
            "error_count": error_count,
            "total_findings": len(findings),
            "processed_findings": processed_findings
        }
    
    def _create_pmd_scan_entity(self, 
                               finding: Dict[str, Any], 
                               org_id: str, 
                               task_id: str,
                               execution_log_id: Optional[str] = None,
                               blob_prefix: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a PMD scan entity from PMD finding
        
        Args:
            finding: PMD finding dictionary
            org_id: Organization ID
            task_id: Task ID
            execution_log_id: Optional execution log ID
            blob_prefix: Optional blob prefix for context
            
        Returns:
            PMD scan entity dictionary
        """
        timestamp = datetime.now()
        timestamp_str = timestamp.strftime('%Y%m%d%H%M%S%f')
        
        # Create unique RowKey
        row_key = f"pmd-{timestamp_str}-{finding.get('file_name', 'unknown').replace('.cls', '')}"
        
        # Build policy name with category
        category = finding.get('category', 'Code Quality')
        policy_name = f'Static Code Analysis (PMD) - {category}'
        
        # Get weakness type
        rule_name = finding.get('rule_name', 'Unknown Rule')
        weakness_type, description = self._get_weakness_type(rule_name, category)
        
        # Create entity for PMDScans table
        entity = {
            # Standard table fields
            'PartitionKey': org_id,
            'RowKey': row_key,
            'TaskStatusId': execution_log_id or task_id,
            'IntegrationId': org_id,
            'TaskId': task_id,
            'ExecutionLogId': execution_log_id or task_id,
            'BlobPrefix': blob_prefix or '',
            
            # Scan metadata
            'ScanType': 'PMD',
            'Language': 'Apex',
            'Tool': 'PMD',
            
            # Policy information
            'PolicyName': policy_name,
            'RuleName': rule_name,
            'Status': finding.get('status', 'failed'),
            'Severity': finding.get('severity', 'Medium'),
            
            # File and location information
            'FileName': finding.get('file_name', 'Unknown'),
            'FilePath': finding.get('file_path', ''),
            'LineNumber': finding.get('line_number', ''),
            'Package': finding.get('package', ''),
            
            # Issue details
            'IssueType': rule_name,
            'IssueCategory': category,
            'IssueDescription': finding.get('description', ''),
            'IssuePriority': finding.get('priority', ''),
            
            # PMD-specific information
            'PMDRuleSet': finding.get('rule_set', ''),
            'PMDProblem': finding.get('problem', ''),
            'WeaknessType': weakness_type,
            'WeaknessTypeDescription': description,
            
            # Metadata
            'CreatedAt': timestamp.isoformat()
        }
        
        return entity
    
    def get_findings_summary(self, findings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate a summary of PMD findings
        
        Args:
            findings: List of PMD findings
            
        Returns:
            Dictionary containing findings summary
        """
        if not findings:
            return {
                "total_findings": 0,
                "severity_breakdown": {},
                "category_breakdown": {},
                "files_affected": 0,
                "unique_rules": 0
            }
        
        # Count by severity
        severity_counts = {}
        category_counts = {}
        files_affected = set()
        unique_rules = set()
        
        for finding in findings:
            # Severity breakdown
            severity = finding.get('severity', 'Unknown')
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
            # Category breakdown
            category = finding.get('category', 'Unknown')
            category_counts[category] = category_counts.get(category, 0) + 1
            
            # Files affected
            file_name = finding.get('file_name', 'Unknown')
            files_affected.add(file_name)
            
            # Unique rules
            rule_name = finding.get('rule_name', 'Unknown')
            unique_rules.add(rule_name)
        
        return {
            "total_findings": len(findings),
            "severity_breakdown": severity_counts,
            "category_breakdown": category_counts,
            "files_affected": len(files_affected),
            "unique_rules": len(unique_rules),
            "files_list": list(files_affected),
            "rules_list": list(unique_rules)
        } 