import os
import asyncio
import json
import logging
from datetime import datetime
from shared.data_access import get_policies_result_table_repo, BlobStorageRepository
from shared.salesforce_utils import execute_salesforce_query
from .utils import (
    load_best_practices_xml,
    normalize_profile_name,
    normalize_permission_name,
    normalize_value,
    parse_profile_permissions,
    parse_permissionset_permissions,
    extract_user_license_from_profile
)

def process_mfa_enforcement_task(processor, task_id, org_id, user_id, params, execution_log_id):
    """
    Handler for MFA Enforcement task. Checks MFA-related settings in profiles and permission sets against best practices.
    Only inserts mismatches into PoliciesResult. For profiles, also insert if the setting is missing.
    Args:
        processor: BackgroundProcessor instance
        task_id: Task ID
        org_id: Integration/Org ID
        user_id: User ID
        params: Parameters dict (should include access_token, instance_url, blob_prefix, org_name, environment, etc.)
        execution_log_id: Execution log ID
    """
    logger = logging.getLogger(__name__)
    access_token = params.get("access_token")
    instance_url = params.get("instance_url")
    org_name = params.get("org_name")
    environment = params.get("environment", "production")
    blob_prefix = params.get("blob_prefix")
    if not (access_token and instance_url and blob_prefix):
        logger.error(f"[MFAEnforcementTask {task_id}] Missing required params (access_token, instance_url, blob_prefix)")
        processor.update_task_status(
            task_id=task_id,
            status="failed",
            progress=0,
            message="Missing required params for MFA enforcement task."
        )
        return

    # Load MFA best practices XML
    mfa_bp_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..', 'best_practices', 'MFA_PermissionSetRisks-BestPractice.xml'))
    mfa_best_practices = load_best_practices_xml(mfa_bp_path)
    # Build a map: userType -> [practice dicts]
    mfa_bp_by_usertype = {}
    for bp in mfa_best_practices:
        usertype = bp.get('UserType', 'BLANK')
        mfa_bp_by_usertype.setdefault(usertype, []).append(bp)
    blob_repo = BlobStorageRepository(container_name="salesforce-metadata")
    repo = get_policies_result_table_repo()

    try:
        # 1. Query Salesforce for active profiles (with active user assignments)
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        profile_query = "SELECT Id, Name FROM Profile"
        profile_query_result = loop.run_until_complete(
            execute_salesforce_query(
                profile_query,
                access_token=access_token,
                instance_url=instance_url,
                environment=environment
            )
        )
        active_profile_ids = set()
        active_profile_names = set()
        # Query active user counts per profile
        user_count_query = (
            "SELECT ProfileId, Profile.Name, COUNT(Id) cnt "
            "FROM User WHERE IsActive = TRUE AND Id NOT IN "
            "(SELECT UserId FROM UserLogin WHERE IsFrozen = true) "
            "GROUP BY ProfileId, Profile.Name"
        )
        user_count_result = loop.run_until_complete(
            execute_salesforce_query(
                user_count_query,
                access_token=access_token,
                instance_url=instance_url,
                environment=environment
            )
        )
        if user_count_result and 'records' in user_count_result:
            for rec in user_count_result['records']:
                profile_id = rec.get('ProfileId')
                profile_name = rec.get('Name')
                count = rec.get('cnt')
                if profile_id and profile_name and count and int(count) > 0:
                    active_profile_ids.add(profile_id)
                    active_profile_names.add(normalize_profile_name(profile_name))
        # Map profile name to ID for matching
        profile_id_map = {}
        if profile_query_result and "records" in profile_query_result:
            for rec in profile_query_result["records"]:
                norm_name = normalize_profile_name(rec["Name"])
                profile_id_map[norm_name] = rec["Id"]
        # 2. Traverse profile XML files in blob storage
        profile_folder = f"{blob_prefix}/profiles/"
        blobs = blob_repo.list_blobs(name_starts_with=profile_folder)
        inserted_profiles = set()
        for blob_name in blobs:
            if not blob_name.endswith('.profile'):
                continue
            profile_name = os.path.basename(blob_name).replace('.profile', '')
            norm_blob_name = normalize_profile_name(profile_name)
            if norm_blob_name in inserted_profiles:
                continue  # Skip duplicate
            is_admin = norm_blob_name == 'admin'
            is_standard = norm_blob_name == 'standard'
            # Only process if this profile is active
            if not (
                norm_blob_name in active_profile_names or
                (is_admin and 'system administrator' in active_profile_names) or
                (is_standard and 'standard platform user' in active_profile_names)
            ):
                continue
            try:
                xml_bytes = blob_repo.get_blob_bytes(blob_name)
                if xml_bytes is None:
                    logger.error(f"[MFAEnforcementTask {task_id}] Failed to download blob bytes for {blob_name}")
                    continue
            except Exception as e:
                logger.error(f"[MFAEnforcementTask {task_id}] Error downloading blob {blob_name}: {e}")
                continue
            user_license = extract_user_license_from_profile(xml_bytes) or 'BLANK'
            user_permissions = parse_profile_permissions(xml_bytes)
            # 3. Compare userPermissions with MFA best practices for this user license
            bp_practices = mfa_bp_by_usertype.get(user_license, mfa_bp_by_usertype.get('BLANK', []))
            results_arr = []
            for bp in bp_practices:
                bp_setting = (bp.get('SalesforceSetting') or '').strip()
                bp_standard_value = (bp.get('StandardValue') or '').strip()
                normalized_bp_setting = normalize_permission_name(bp_setting)
                profile_perm = next((p for p in user_permissions if normalize_permission_name(p['name']) == normalized_bp_setting), None)
                if profile_perm is not None:
                    profile_value = profile_perm['enabled']
                    match = normalize_value(profile_value) == normalize_value(bp_standard_value)
                    if not match:
                        results_arr.append({
                            'SalesforceSetting': bp_setting,
                            'StandardValue': bp_standard_value,
                            'OrgValue': profile_value,
                            'Description': bp.get('Description'),
                            'OWASP': bp.get('OWASP'),
                            'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity'),
                            'Issue': 'Value does not match best practice'
                        })
                else:
                    # If the setting is missing, that's also a best-practice violation
                    results_arr.append({
                        'SalesforceSetting': bp_setting,
                        'StandardValue': bp_standard_value,
                        'OrgValue': None,
                        'Description': bp.get('Description'),
                        'OWASP': bp.get('OWASP'),
                        'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity'),
                        'Issue': 'Setting missing from profile'
                    })
            # 4. Insert mismatches or missing settings into PoliciesResult
            row_key = f"{profile_name}-{execution_log_id}"
            entity = {
                'PartitionKey': str(org_id),
                'RowKey': row_key,
                'OrgValue': json.dumps(results_arr),
                'IntegrationId': str(org_id),
                'TaskStatusId': execution_log_id,
                'CreatedAt': datetime.now().isoformat(),
                'ProfileName': profile_name,
                'Type': 'MFAProfilePermissions',
            }
            existing = repo.get_entity(partition_key=str(org_id), row_key=row_key)
            if not existing:
                try:
                    repo.insert_entity(entity)
                    inserted_profiles.add(norm_blob_name)
                except Exception as e:
                    logger.error(f"[MFAEnforcementTask {task_id}] Failed to insert PoliciesResult for profile {profile_name}: {e}")
        # 5. Permission Set: Find active assignments, then check permission set XMLs (unchanged logic)
        psa_query = (
            "SELECT AssigneeId, PermissionSetId, PermissionSet.Name, Assignee.ProfileId FROM PermissionSetAssignment "
            "WHERE AssigneeId IN (SELECT Id FROM User WHERE IsActive = TRUE AND Id NOT IN (SELECT UserId FROM UserLogin WHERE IsFrozen = true))"
        )
        psa_result = loop.run_until_complete(
            execute_salesforce_query(
                psa_query,
                access_token=access_token,
                instance_url=instance_url,
                environment=environment
            )
        )
        # Build mapping: PermissionSetName -> set of ProfileIds
        ps_to_profileids = {}
        if psa_result and 'records' in psa_result:
            for rec in psa_result['records']:
                ps_name = rec.get('PermissionSet', {}).get('Name')
                profile_id = rec.get('Assignee', {}).get('ProfileId')
                if ps_name and profile_id:
                    ps_to_profileids.setdefault(ps_name, set()).add(profile_id)
        # Map ProfileId to userLicense (from earlier profile_query_result)
        profileid_to_userlicense = {}
        if profile_query_result and "records" in profile_query_result:
            for rec in profile_query_result["records"]:
                norm_name = normalize_profile_name(rec["Name"])
                profile_id = rec["Id"]
                # Find the corresponding blob and extract userLicense
                profile_blob_name = None
                for blob_name in blobs:
                    if blob_name.endswith('.profile'):
                        base_name = os.path.basename(blob_name).replace('.profile', '')
                        if normalize_profile_name(base_name) == norm_name:
                            profile_blob_name = blob_name
                            break
                if profile_blob_name:
                    try:
                        xml_bytes = blob_repo.get_blob_bytes(profile_blob_name)
                        user_license = extract_user_license_from_profile(xml_bytes)
                        if user_license:
                            profileid_to_userlicense[profile_id] = user_license
                    except Exception as e:
                        logger.error(f"[MFAEnforcementTask {task_id}] Error extracting userLicense for profile {profile_id}: {e}")
        # Traverse permission set XMLs
        permissionset_folder = f"{blob_prefix}/permissionsets/"
        permission_set_blobs = blob_repo.list_blobs(name_starts_with=permissionset_folder)
        inserted_ps_userlicense = set()
        for blob_name in permission_set_blobs:
            if not blob_name.endswith('.permissionset'):
                continue
            permission_set_name = os.path.basename(blob_name).replace('.permissionset', '')
            # Find all userLicenses for profiles assigned to this permission set
            assigned_profileids = ps_to_profileids.get(permission_set_name, set())
            assigned_userlicenses = set()
            for pid in assigned_profileids:
                if pid in profileid_to_userlicense:
                    assigned_userlicenses.add(profileid_to_userlicense[pid])
            if not assigned_userlicenses:
                assigned_userlicenses = {'BLANK'}
            try:
                xml_bytes = blob_repo.get_blob_bytes(blob_name)
                if xml_bytes is None:
                    logger.error(f"[MFAEnforcementTask {task_id}] Failed to download blob bytes for {blob_name}")
                    continue
            except Exception as e:
                logger.error(f"[MFAEnforcementTask {task_id}] Error downloading blob {blob_name}: {e}")
                continue
            user_permissions = parse_permissionset_permissions(xml_bytes)
            # For each userLicense, compare with the relevant best practices
            for user_license in assigned_userlicenses:
                ps_ul_key = (permission_set_name, user_license)
                if ps_ul_key in inserted_ps_userlicense:
                    continue  # Skip duplicate
                bp_practices = mfa_bp_by_usertype.get(user_license, mfa_bp_by_usertype.get('BLANK', []))
                results_arr = []
                for bp in bp_practices:
                    bp_setting = (bp.get('SalesforceSetting') or '').strip()
                    bp_standard_value = (bp.get('StandardValue') or '').strip()
                    normalized_bp_setting = normalize_permission_name(bp_setting)
                    ps_perm = next((p for p in user_permissions if normalize_permission_name(p['name']) == normalized_bp_setting), None)
                    if ps_perm is not None:
                        ps_value = ps_perm['enabled']
                        match = normalize_value(ps_value) == normalize_value(bp_standard_value)
                        if not match:
                            results_arr.append({
                                'SalesforceSetting': bp_setting,
                                'StandardValue': bp_standard_value,
                                'OrgValue': ps_value,
                                'Description': bp.get('Description'),
                                'OWASP': bp.get('OWASP'),
                                'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity'),
                                'UserLicense': user_license,
                                'Issue': 'Value does not match best practice'
                            })
                if results_arr:
                    row_key = f"{permission_set_name}-{user_license}-{execution_log_id}"
                    entity = {
                        'PartitionKey': str(org_id),
                        'RowKey': row_key,
                        'OrgValue': json.dumps(results_arr),
                        'IntegrationId': str(org_id),
                        'TaskStatusId': execution_log_id,
                        'CreatedAt': datetime.now().isoformat(),
                        'PermissionSetName': permission_set_name,
                        'UserLicense': user_license,
                        'Type': 'MFAPermissionSetPermissions',
                    }
                    existing = repo.get_entity(partition_key=str(org_id), row_key=row_key)
                    if not existing:
                        try:
                            repo.insert_entity(entity)
                            inserted_ps_userlicense.add(ps_ul_key)
                        except Exception as e:
                            logger.error(f"[MFAEnforcementTask {task_id}] Failed to insert PoliciesResult for permission set {permission_set_name} (userLicense {user_license}): {e}")
        loop.close()
        processor.update_task_status(
            task_id=task_id,
            status="completed",
            progress=100,
            message="MFA Enforcement check completed and results stored."
        )
    except Exception as e:
        logger.error(f"[MFAEnforcementTask {task_id}] Error: {e}")
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(
            task_id=task_id,
            status="failed",
            progress=0,
            message=f"Error processing MFA enforcement task: {e}"
        ) 