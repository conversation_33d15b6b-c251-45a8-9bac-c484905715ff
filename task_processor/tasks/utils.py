import xml.etree.ElementTree as ET
import urllib.parse
import re
import logging
from shared.salesforce_utils import execute_salesforce_query

def normalize_value(val):
    if val is None:
        return None
    val = val.strip().lower()
    if val in ['❌ false', 'x false', 'false', 'no', 'disabled', '0']:
        return 'false'
    if val in ['✅ true', 'tickmark/true', 'true', 'yes', 'enabled', '1']:
        return 'true'
    return val

def normalize_key(key):
    if key is None:
        return None
    return key.strip().lower().replace(' ', '')

def load_best_practices_xml(xml_path):
    tree = ET.parse(xml_path)
    root = tree.getroot()
    best_practices = []
    for usertype_elem in root.findall('UserType'):
        usertype = usertype_elem.attrib.get('name', '')
        for practice in usertype_elem.findall('Practice'):
            bp = {child.tag: child.text for child in practice}
            bp['UserType'] = usertype  # Add UserType from attribute
            best_practices.append(bp)
    return best_practices

def normalize_profile_name(name):
    if not name:
        return ''
    # Decode URL-encoded characters, strip whitespace, and lowercase
    return urllib.parse.unquote(name).strip().lower()

def normalize_permission_name(name):
    if not name:
        return ''
    return name.strip().lower().replace(' ', '').replace('_', '')

def parse_profile_permissions(xml_bytes):
    perms = []
    try:
        root = ET.fromstring(xml_bytes)
        # Find all userPermissions, regardless of namespace
        for perm in root.iter():
            if perm.tag.endswith('userPermissions'):
                name_elem = None
                enabled_elem = None
                for child in perm:
                    if child.tag.endswith('name'):
                        name_elem = child
                    elif child.tag.endswith('enabled'):
                        enabled_elem = child
                if name_elem is not None and enabled_elem is not None:
                    perms.append({
                        'name': name_elem.text.strip() if name_elem.text else '',
                        'enabled': enabled_elem.text.strip() if enabled_elem.text else ''
                    })
    except ET.ParseError as e:
        logging.error(f"[ERROR] XML parsing error in profile: {e}")
        logging.info(f"[DEBUG] XML parsing error. XML preview: {xml_bytes[:200] if isinstance(xml_bytes, bytes) else str(xml_bytes)[:200]}")
    except Exception as e:
        logging.error(f"[ERROR] Error parsing userPermissions: {e}")
        logging.info(f"[DEBUG] Error parsing userPermissions. XML preview: {xml_bytes[:200] if isinstance(xml_bytes, bytes) else str(xml_bytes)[:200]}")
    return perms

def parse_permissionset_permissions(xml_bytes):
    perms = []
    try:
        root = ET.fromstring(xml_bytes)
        for perm in root.iter():
            if perm.tag.endswith('userPermissions'):
                name_elem = None
                enabled_elem = None
                for child in perm:
                    if child.tag.endswith('name'):
                        name_elem = child
                    elif child.tag.endswith('enabled'):
                        enabled_elem = child
                if name_elem is not None and enabled_elem is not None:
                    perms.append({
                        'name': name_elem.text.strip() if name_elem.text else '',
                        'enabled': enabled_elem.text.strip() if enabled_elem.text else ''
                    })
    except ET.ParseError as e:
        logging.error(f"[ERROR] XML parsing error in permissionset: {e}")
        logging.info(f"[DEBUG] XML parsing error. XML preview: {xml_bytes[:200] if isinstance(xml_bytes, bytes) else str(xml_bytes)[:200]}")
    except Exception as e:
        logging.error(f"[ERROR] Error parsing userPermissions: {e}")
        logging.info(f"[DEBUG] Error parsing userPermissions. XML preview: {xml_bytes[:200] if isinstance(xml_bytes, bytes) else str(xml_bytes)[:200]}")
    return perms

def extract_user_license_from_profile(xml_bytes):
    try:
        root = ET.fromstring(xml_bytes)
        for elem in root.iter():
            if elem.tag.endswith('userLicense') and elem.text:
                return elem.text.strip()
    except Exception as e:
        logging.error(f"[ERROR] Error extracting userLicense from profile XML: {e}")
    return None

def get_active_profiles_and_permissionsets(access_token, instance_url, environment):
    """
    Returns (active_profile_names, active_permission_set_names)
    Both are sets of normalized names.
    """
    import asyncio
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    # Get active profiles
    user_count_query = (
        "SELECT ProfileId, Profile.Name, COUNT(Id) cnt "
        "FROM User WHERE IsActive = TRUE AND Id NOT IN "
        "(SELECT UserId FROM UserLogin WHERE IsFrozen = true) "
        "GROUP BY ProfileId, Profile.Name"
    )
    user_count_result = loop.run_until_complete(
        execute_salesforce_query(
            user_count_query,
            access_token=access_token,
            instance_url=instance_url,
            environment=environment
        )
    )
    active_profile_names = set()
    active_profile_ids = set()
    if user_count_result and 'records' in user_count_result:
        for rec in user_count_result['records']:
            profile_id = rec.get('ProfileId')
            profile_name = rec.get('Name')
            count = rec.get('cnt')
            if profile_id and profile_name and count and int(count) > 0:
                active_profile_names.add(normalize_profile_name(profile_name))
                active_profile_ids.add(profile_id)
    # Get active permission sets
    psa_query = (
        "SELECT AssigneeId, PermissionSetId, PermissionSet.Name FROM PermissionSetAssignment "
        "WHERE AssigneeId IN (SELECT Id FROM User WHERE IsActive = TRUE AND Id NOT IN (SELECT UserId FROM UserLogin WHERE IsFrozen = true))"
    )
    psa_result = loop.run_until_complete(
        execute_salesforce_query(
            psa_query,
            access_token=access_token,
            instance_url=instance_url,
            environment=environment
        )
    )
    active_permission_set_names = set()
    if psa_result and 'records' in psa_result:
        for rec in psa_result['records']:
            ps_name = rec.get('PermissionSet', {}).get('Name')
            if ps_name:
                active_permission_set_names.add(ps_name)
    loop.close()
    return active_profile_names, active_permission_set_names 