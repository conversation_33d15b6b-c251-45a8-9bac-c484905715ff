import os
import asyncio
import json
import logging
from datetime import datetime
from shared.data_access import get_policies_result_table_repo, BlobStorageRepository
from shared.salesforce_utils import execute_salesforce_query
from .utils import (
    load_best_practices_xml,
    normalize_profile_name,
    normalize_permission_name,
    normalize_value,
    parse_profile_permissions,
    get_active_profiles_and_permissionsets
)

def process_device_activation_task(processor, task_id, org_id, user_id, params, execution_log_id):
    logger = logging.getLogger(__name__)
    access_token = params.get("access_token")
    instance_url = params.get("instance_url")
    org_name = params.get("org_name")
    environment = params.get("environment", "production")
    blob_prefix = params.get("blob_prefix")
    if not (access_token and instance_url and blob_prefix):
        logger.error(f"[DeviceActivationTask {task_id}] Missing required params (access_token, instance_url, blob_prefix)")
        processor.update_task_status(
            task_id=task_id,
            status="failed",
            progress=0,
            message="Missing required params for Device Activation task."
        )
        return

    # Load Device Activation best practices XML
    bp_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..', 'best_practices', 'DeviceActivation_Profile-BestPractices.xml'))
    best_practices = load_best_practices_xml(bp_path)
    blob_repo = BlobStorageRepository(container_name="salesforce-metadata")
    repo = get_policies_result_table_repo()

    try:
        # Get active profiles using shared utility
        active_profile_names, _ = get_active_profiles_and_permissionsets(access_token, instance_url, environment)
        # 2. Traverse profile XML files in blob storage
        profile_folder = f"{blob_prefix}/profiles/"
        blobs = blob_repo.list_blobs(name_starts_with=profile_folder)
        inserted_profiles = set()
        for norm_blob_name in set(active_profile_names):  # Deduplicate upfront
            # Find the corresponding blob for this profile
            matched_blob = None
            for blob_name in blobs:
                if not blob_name.endswith('.profile'):
                    continue
                profile_name = os.path.basename(blob_name).replace('.profile', '')
                if normalize_profile_name(profile_name) == norm_blob_name:
                    matched_blob = blob_name
                    break
            if norm_blob_name in inserted_profiles:
                continue  # Skip duplicate
            is_admin = norm_blob_name == 'admin'
            is_standard = norm_blob_name == 'standard'
            if not (
                norm_blob_name in active_profile_names or
                (is_admin and 'system administrator' in active_profile_names) or
                (is_standard and 'standard platform user' in active_profile_names)
            ):
                continue
            results_arr = []
            if not matched_blob:
                # No device activation setting file found for this profile
                for bp in best_practices:
                    results_arr.append({
                        'SalesforceSetting': bp.get('SalesforceSetting'),
                        'StandardValue': bp.get('StandardValue'),
                        'OrgValue': None,
                        'Description': bp.get('Description'),
                        'OWASP': bp.get('OWASP'),
                        'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity'),
                        'Issue': 'Device activation setting missing for profile'
                    })
            else:
                try:
                    xml_bytes = blob_repo.get_blob_bytes(matched_blob)
                    if xml_bytes is None:
                        logger.error(f"[DeviceActivationTask {task_id}] Failed to download blob bytes for {matched_blob}")
                        for bp in best_practices:
                            results_arr.append({
                                'SalesforceSetting': bp.get('SalesforceSetting'),
                                'StandardValue': bp.get('StandardValue'),
                                'OrgValue': None,
                                'Description': bp.get('Description'),
                                'OWASP': bp.get('OWASP'),
                                'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity'),
                                'Issue': 'Device activation setting missing for profile'
                            })
                    else:
                        user_permissions = parse_profile_permissions(xml_bytes)
                        for bp in best_practices:
                            bp_setting = (bp.get('SalesforceSetting') or '').strip()
                            bp_standard_value = (bp.get('StandardValue') or '').strip()
                            normalized_bp_setting = normalize_permission_name(bp_setting)
                            profile_perm = next((p for p in user_permissions if normalize_permission_name(p['name']) == normalized_bp_setting), None)
                            if profile_perm is not None:
                                profile_value = profile_perm['enabled']
                                match = normalize_value(profile_value) == normalize_value(bp_standard_value)
                                if not match:
                                    results_arr.append({
                                        'SalesforceSetting': bp_setting,
                                        'StandardValue': bp_standard_value,
                                        'OrgValue': profile_value,
                                        'Description': bp.get('Description'),
                                        'OWASP': bp.get('OWASP'),
                                        'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity'),
                                        'Issue': 'Value does not match best practice'
                                    })
                            else:
                                results_arr.append({
                                    'SalesforceSetting': bp_setting,
                                    'StandardValue': bp_standard_value,
                                    'OrgValue': None,
                                    'Description': bp.get('Description'),
                                    'OWASP': bp.get('OWASP'),
                                    'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity'),
                                    'Issue': 'Device activation setting missing for profile'
                                })
                except Exception as e:
                    logger.error(f"[DeviceActivationTask {task_id}] Error downloading blob {matched_blob}: {e}")
                    for bp in best_practices:
                        results_arr.append({
                            'SalesforceSetting': bp.get('SalesforceSetting'),
                            'StandardValue': bp.get('StandardValue'),
                            'OrgValue': None,
                            'Description': bp.get('Description'),
                            'OWASP': bp.get('OWASP'),
                            'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity'),
                            'Issue': 'Device activation setting missing for profile'
                        })
            unique_results = []
            seen = set()
            for entry in results_arr:
                entry_json = json.dumps(entry, sort_keys=True)
                if entry_json not in seen:
                    unique_results.append(entry)
                    seen.add(entry_json)
            row_key = f"{norm_blob_name}-{execution_log_id}"
            entity = {
                'PartitionKey': str(org_id),
                'RowKey': row_key,
                'OrgValue': json.dumps(unique_results),
                'IntegrationId': str(org_id),
                'TaskStatusId': execution_log_id,
                'CreatedAt': datetime.now().isoformat(),
                'ProfileName': norm_blob_name,
                'Type': 'DeviceActivationProfilePermissions',
            }
            existing = repo.get_entity(partition_key=str(org_id), row_key=row_key)
            if not existing:
                try:
                    repo.insert_entity(entity)
                    inserted_profiles.add(norm_blob_name)
                except Exception as e:
                    logger.error(f"[DeviceActivationTask {task_id}] Failed to insert PoliciesResult for profile {norm_blob_name}: {e}")
        processor.update_task_status(
            task_id=task_id,
            status="completed",
            progress=100,
            message="Device Activation check completed and results stored."
        )
    except Exception as e:
        logger.error(f"[DeviceActivationTask {task_id}] Error: {e}")
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(
            task_id=task_id,
            status="failed",
            progress=0,
            message=f"Error processing Device Activation task: {e}"
        ) 