"""
Task Processor Function

This function processes tasks from the task queue and updates their status.
"""

import json
import logging
import azure.functions as func
import requests
import urllib.parse
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import asyncio
import os
import xml.etree.ElementTree as ET
import re
try:
    from fuzzywuzzy import process as fuzzy_process
    FUZZY_AVAILABLE = True
except ImportError:
    FUZZY_AVAILABLE = False

# Import shared modules
from shared.background_processor import (
    BackgroundProcessor,
    TASK_STATUS_PENDING,
    TASK_STATUS_RUNNING,
    TASK_STATUS_COMPLETED,
    TASK_STATUS_FAILED,
    TASK_STATUS_RETRY,
    TASK_STATUS_CANCELLED,
    TASK_TYPE_HEALTH_CHECK,
    TASK_TYPE_DATA_EXPORT,
    TASK_TYPE_REPORT_GENERATION,
    TASK_TYPE_SCHEDULED_SCAN,
    TASK_TYPE_NOTIFICATION,
    TASK_TYPE_METADATA_EXTRACTION,
    TASK_TYPE_SFDC_AUTHENTICATE,
    TASK_PRIORITY_HIGH,
    TASK_PRIORITY_MEDIUM,
    TASK_PRIORITY_LOW
)
from shared.salesforce_utils import (
    execute_salesforce_query,
    execute_salesforce_tooling_query,
    get_salesforce_access_token,
    test_salesforce_connection,
    get_salesforce_client
)
from shared.auth_service import authenticate_salesforce_integration
from shared.data_access import TableStorageRepository, SqlDatabaseRepository, BlobStorageRepository, get_table_storage_repository, get_policies_result_table_repo, get_profile_assignment_count_table_repo, enqueue_policy_tasks
from shared.common import is_local_dev
from shared.azure_services import get_secret
from shared.metadata_extraction import extract_salesforce_metadata
from blueprints.security_analysis import fetch_security_health_check_risks, calculate_health_score, process_and_store_policies_results # For health check task
from blueprints.integration import get_integration_table_repo, get_integration_sql_repo # Added import
from fastapi import APIRouter
from .tasks.pmd_task import process_pmd_task
router = APIRouter()

@router.get("/api/profiles-permissions")
def get_profiles_permissions(orgId: str):
    task_repo = TableStorageRepository(table_name="TaskStatus")
    # 1. Get latest profiles_permission_sets task
    filter_query_profiles = f"PartitionKey eq '{orgId}' and TaskType eq 'profiles_permission_sets'"
    tasks_profiles = task_repo.query_entities(filter_query_profiles)
    completed_profiles_tasks = [t for t in tasks_profiles if t.get("Status") == "completed"]
    if not completed_profiles_tasks:
        return {"status": "processing", "message": "Profiles and permissions scan is still running."}
    latest_profiles_task = sorted(completed_profiles_tasks, key=lambda t: t.get("CreatedAt", ""), reverse=True)[0]
    execution_log_id_profiles = latest_profiles_task.get("ExecutionLogId")

    # 2. Get latest permission_sets task
    filter_query_psets = f"PartitionKey eq '{orgId}' and TaskType eq 'permission_sets'"
    tasks_psets = task_repo.query_entities(filter_query_psets)
    completed_psets_tasks = [t for t in tasks_psets if t.get("Status") == "completed"]
    execution_log_id_psets = None
    if completed_psets_tasks:
        latest_psets_task = sorted(completed_psets_tasks, key=lambda t: t.get("CreatedAt", ""), reverse=True)[0]
        execution_log_id_psets = latest_psets_task.get("ExecutionLogId")

    policies_repo = TableStorageRepository(table_name="PoliciesResult")
    assignment_repo = TableStorageRepository(table_name="ProfileAssignmentCount")
    # 3. Query for profile policies (all types for profiles_permission_sets)
    policies = policies_repo.query_entities(f"PartitionKey eq '{orgId}' and TaskStatusId eq '{execution_log_id_profiles}'")
    all_policies = list(policies)

    # 4. Query for permission set policies (Type = 'PermissionSetPermissions' for permission_sets)
    if execution_log_id_psets:
        permission_set_policies = policies_repo.query_entities(
            f"PartitionKey eq '{orgId}' and TaskStatusId eq '{execution_log_id_psets}' and Type eq 'PermissionSetPermissions'"
        )
        for p in permission_set_policies:
            if p not in all_policies:
                all_policies.append(p)

    # 5. Also fetch PermissionSetPermissions records for profiles_permission_sets task (legacy support)
    permission_set_policies_profiles = policies_repo.query_entities(
        f"PartitionKey eq '{orgId}' and TaskStatusId eq '{execution_log_id_profiles}' and Type eq 'PermissionSetPermissions'"
    )
    for p in permission_set_policies_profiles:
        if p not in all_policies:
            all_policies.append(p)

    # 6. Fetch assignments (for latest profiles_permission_sets task)
    assignments = assignment_repo.query_entities(f"PartitionKey eq '{orgId}' and TaskStatusId eq '{execution_log_id_profiles}'")
    profile_assignments = [a for a in assignments if a.get('Type') == 'ProfilePermissions']
    permissionset_assignments = [a for a in assignments if a.get('Type') == 'ProfilePermissionSetAssignment']

    # 7. Collect all unique permission setting names for dynamic columns
    import json as _json
    all_settings = set()
    for p in all_policies:
        try:
            for setting in _json.loads(p.get('OrgValue', '[]')):
                if setting.get('SalesforceSetting'):
                    all_settings.add(setting.get('SalesforceSetting'))
        except Exception:
            pass

    return {
        "status": "completed",
        "settings": sorted(all_settings),
        "policies": [p for p in all_policies],
        "profileAssignments": profile_assignments,
        "permissionSetAssignments": permissionset_assignments
    }

# Configure module-level logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

# Add to the list of task type constants at the top
TASK_TYPE_PERMISSION_SETS = "permission_sets"
TASK_TYPE_PROFILES_PERMISSION_SETS = "profiles_permission_sets"
TASK_TYPE_MFA_ENFORCEMENT = "mfa_enforcement"
# Add new task type constants
TASK_TYPE_DEVICE_ACTIVATION = "device_activation"
TASK_TYPE_LOGIN_IP_RANGES = "login_ip_ranges"
TASK_TYPE_LOGIN_HOURS = "login_hours"
TASK_TYPE_SESSION_TIMEOUT = "session_timeout"
TASK_TYPE_API_WHITELISTING = "api_whitelisting"
TASK_TYPE_PASSWORD_POLICY = "password_policy"
TASK_TYPE_PMD_APEX_SECURITY = "pmd_apex_security"

def main(msg: func.QueueMessage) -> None:
    """
    Process a task from the queue

    Args:
        msg: Queue message
    """
    import traceback
    logger.info("[MAIN] Entered main function for task processing.")
    try:
        # Handle message content
        logger.info("[MAIN] Processing queue message...")

        # With messageEncoding="base64" in function.json, Azure Functions automatically decodes the message
        # We just need to decode the bytes to a string and parse it as JSON
        try:
            # Get the message body and decode it to a string
            message_body = msg.get_body().decode('utf-8')
            logger.debug(f"[MAIN] Message body: {message_body}")

            # Parse the message as JSON
            task_data = json.loads(message_body)
            logger.debug(f"[MAIN] Successfully parsed message as JSON: {task_data}")
        except Exception as e:
            logger.error(f"[MAIN] Error processing message: {str(e)}")
            logger.error(f"[MAIN] Message type: {type(msg)}")
            if hasattr(msg, 'get_body'):
                try:
                    body = msg.get_body()
                    logger.error(f"[MAIN] Message body: {body}")
                    logger.error(f"[MAIN] Message body type: {type(body)}")
                except Exception as body_error:
                    logger.error(f"[MAIN] Error getting message body: {str(body_error)}")
            logger.error(f"[MAIN] Traceback:\n{traceback.format_exc()}")
            raise ValueError(f"Could not process message: {str(e)}")

        # Log the parsed task data for debugging
        logger.debug(f"[MAIN] Parsed task data: {task_data}")

        # Extract task information
        task_id = task_data.get("task_id")
        task_type = task_data.get("task_type")
        org_id = task_data.get("org_id")
        user_id = task_data.get("user_id")
        params = task_data.get("params", {})
        execution_log_id = task_data.get("execution_log_id")

        # Normalize task_type to avoid whitespace/case issues
        task_type = (task_type or "").strip().lower()
        logger.info(f"[MAIN] Received task_type: '{task_type}'")

        logger.info(f"[MAIN] Processing task {task_id} of type {task_type} for organization {org_id}")
        logger.debug(f"[MAIN] Task parameters: {params}")

        # Initialize background processor
        processor = BackgroundProcessor()

        # Update task status to running
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=10,
            message="Starting overview data collection"
        )

        # Extract priority and retry count
        priority = task_data.get("priority", TASK_PRIORITY_MEDIUM)
        retry_count = task_data.get("retry_count", 0)

        # Log task details
        logger.info(f"[MAIN] Processing task {task_id} of type {task_type} for organization {org_id} with priority {priority} (retry {retry_count})")

        # Check if this is a recurring task that needs to be rescheduled
        is_recurring = params.get("is_recurring", False)
        if is_recurring and task_type == TASK_TYPE_SCHEDULED_SCAN:
            logger.info(f"[MAIN] Rescheduling recurring task {task_id}.")
            schedule_type = params.get("schedule_type", "daily")
            processor.schedule_recurring_task(
                task_type=TASK_TYPE_SCHEDULED_SCAN,
                org_id=org_id,
                user_id=user_id,
                schedule_type=schedule_type,
                params=params,
                priority=priority
            )
            logger.info(f"[MAIN] Rescheduled recurring task {task_id} with schedule type {schedule_type}")

        # Process task based on type
        logger.info(f"[MAIN] Branching to task type handler for {task_type}")
        if task_type == TASK_TYPE_OVERVIEW:
            logger.info(f"[MAIN] Calling process_overview_task for {task_id}")
            process_overview_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_HEALTH_CHECK:
            logger.info(f"[MAIN] Calling process_health_check_task for {task_id}")
            process_health_check_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_PROFILES:
            logger.info(f"[MAIN] Calling process_profiles_task for {task_id}")
            process_profiles_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_DATA_EXPORT:
            logger.info(f"[MAIN] Calling process_data_export_task for {task_id}")
            process_data_export_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_REPORT_GENERATION:
            logger.info(f"[MAIN] Calling process_report_generation_task for {task_id}")
            process_report_generation_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_SCHEDULED_SCAN:
            logger.info(f"[MAIN] Calling process_scheduled_scan_task for {task_id}")
            process_scheduled_scan_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_NOTIFICATION:
            logger.info(f"[MAIN] Calling process_notification_task for {task_id}")
            process_notification_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_METADATA_EXTRACTION:
            logger.info(f"[MAIN] Calling process_metadata_extraction_task for {task_id}")
            process_metadata_extraction_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_SFDC_AUTHENTICATE:
            logger.info(f"[MAIN] Calling process_sfdc_authenticate_task for {task_id}")
            process_sfdc_authenticate_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_PROFILES_PERMISSION_SETS:
            logger.info(f"[MAIN] Calling process_profiles_permission_sets_task for {task_id}")
            process_profiles_permission_sets_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_PERMISSION_SETS:
            logger.info(f"[MAIN] Calling process_permission_sets_task for {task_id}")
            process_permission_sets_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_MFA_ENFORCEMENT:
            logger.info(f"[MAIN] Calling process_mfa_enforcement_task for {task_id}")
            process_mfa_enforcement_task(processor, task_id, org_id, user_id, params, execution_log_id)
        # Add new task type dispatchers
        elif task_type == TASK_TYPE_DEVICE_ACTIVATION:
            logger.info(f"[MAIN] Calling process_device_activation_task for {task_id}")
            process_device_activation_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_LOGIN_IP_RANGES:
            logger.info(f"[MAIN] Calling process_login_ip_ranges_task for {task_id}")
            process_login_ip_ranges_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_LOGIN_HOURS:
            logger.info(f"[MAIN] Calling process_login_hours_task for {task_id}")
            process_login_hours_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_SESSION_TIMEOUT:
            logger.info(f"[MAIN] Calling process_session_timeout_task for {task_id}")
            process_session_timeout_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_API_WHITELISTING:
            logger.info(f"[MAIN] Calling process_api_whitelisting_task for {task_id}")
            process_api_whitelisting_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_PASSWORD_POLICY:
            logger.info(f"[MAIN] Calling process_password_policy_task for {task_id}")
            process_password_policy_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_PMD_APEX_SECURITY:
            logger.info(f"[MAIN] Calling process_pmd_task for {task_id}")
            process_pmd_task(processor, task_id, org_id, user_id, params, execution_log_id)
        else:
            logger.error(f"[MAIN] Unknown task type: {task_type}")
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=0,
                message=f"Unknown task type: {task_type}"
            )
        logger.info(f"[MAIN] Exiting main function for task {task_id}")
    except Exception as e:
        logger.error(f"[MAIN] Error processing task: {str(e)}")
        logger.error(f"[MAIN] Traceback:\n{traceback.format_exc()}")
        # Try to update task status if possible
        try:
            processor = BackgroundProcessor()
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=0,
                message=f"Error processing task: {str(e)}"
            )
        except Exception as update_error:
            logger.error(f"[MAIN] Error updating task status: {str(update_error)}")
            logger.error(f"[MAIN] Traceback (update error):\n{traceback.format_exc()}")

def get_integration_by_id(org_id: str) -> Dict[str, Any]:
    """
    Get integration by ID

    Args:
        org_id: Organization ID

    Returns:
        Dict[str, Any]: Integration if found, None otherwise
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            integration_repo = TableStorageRepository(table_name="Integrations")

            # Query for integration with matching ID
            filter_query = f"PartitionKey eq 'integration' and RowKey eq '{org_id}'"
            entities = integration_repo.query_entities(filter_query)

            if not entities:
                logger.warning(f"No integration found with ID: {org_id}")
                return None

            # Return the first matching integration
            integration = {
                "id": entities[0].get("RowKey"),
                "name": entities[0].get("Name", ""),
                "tenantUrl": entities[0].get("TenantUrl", ""),
                "type": entities[0].get("Type", "Salesforce"),
                "description": entities[0].get("Description", ""),
                "environment": entities[0].get("Environment", "production"),
                "isActive": entities[0].get("IsActive", True),
                "lastScan": entities[0].get("LastScan", ""),
                "createdAt": entities[0].get("CreatedAt", ""),
                "userEmail": entities[0].get("UserEmail", "")
            }
            return integration
        else:
            # Use SQL Database for production
            integration_repo = SqlDatabaseRepository(table_name="App_Organization")

            # Query for integration with matching ID
            query = """
            SELECT Id, Name, TenantUrl, Type, Description, Environment, IsActive, LastScan, CreatedAt, CreatedBy
            FROM App_Organization
            WHERE Id = ?
            """
            results = integration_repo.execute_query(query, (org_id,))

            if not results or len(results) == 0:
                logger.warning(f"No integration found with ID: {org_id}")
                return None

            # Return the first matching integration
            integration = {
                "id": results[0][0],
                "name": results[0][1],
                "tenantUrl": results[0][2],
                "type": results[0][3],
                "description": results[0][4],
                "environment": results[0][5],
                "isActive": results[0][6],
                "lastScan": results[0][7],
                "createdAt": results[0][8],
                "createdBy": results[0][9]
            }
            return integration
    except Exception as e:
        logger.error(f"Error getting integration by ID: {str(e)}")
        return None

# Salesforce API functions are now imported from shared.salesforce_utils

def process_overview_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str) -> None:
    """
    Process an overview task

    Args:
        processor: Background processor
        task_id: Task ID
        org_id: Organization ID
        user_id: User ID
        params: Task parameters
        execution_log_id: Execution log ID
    """
    try:
        logger.info(f"Processing overview task {task_id} for organization {org_id}")
        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 10, "Starting overview data collection")

        access_token = params.get("access_token")
        instance_url = params.get("instance_url")
        integration_id_from_params = params.get("integration_id") # Should be same as org_id

        if not all([access_token, instance_url, integration_id_from_params]):
            err_msg = "Missing access_token, instance_url, or integration_id in task parameters for overview."
            logger.error(f"[OverviewTask {task_id}] {err_msg}")
            processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, err_msg)
            return

        if org_id != integration_id_from_params:
            logger.warning(f"[OverviewTask {task_id}] org_id ({org_id}) and integration_id_from_params ({integration_id_from_params}) mismatch. Using org_id from task.")

        try:
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_RUNNING,
                progress=40,
                message="Fetching overview data from Salesforce using provided token."
            )

            # Fetch data from Salesforce
            # 1. Get total profiles
            profiles_query = "SELECT COUNT() FROM Profile"
            profiles_result = execute_salesforce_query(profiles_query, access_token, instance_url)
            total_profiles = profiles_result.get("totalSize", 0) if profiles_result else 0
            logger.info(f"Found {total_profiles} profiles")

            # 2. Get total permission sets
            permission_sets_query = "SELECT COUNT() FROM PermissionSet WHERE IsOwnedByProfile = false"
            permission_sets_result = execute_salesforce_query(permission_sets_query, access_token, instance_url)
            total_permission_sets = permission_sets_result.get("totalSize", 0) if permission_sets_result else 0
            logger.info(f"Found {total_permission_sets} permission sets")

            # Initialize risk counts
            total_risks = 0
            high_risks = 0
            medium_risks = 0
            low_risks = 0

            # Get health score directly from SecurityHealthCheck
            score_query = "SELECT Score FROM SecurityHealthCheck"
            score_result = execute_salesforce_tooling_query(score_query, access_token, instance_url, api_version="v58.0")

            if score_result and "records" in score_result and len(score_result["records"]) > 0:
                # Use the score directly from Salesforce
                health_score = score_result["records"][0].get("Score", 0)
                logger.info(f"Retrieved health score directly from Salesforce: {health_score}")

                # Get total risks count for reference
                risks_count_query = "SELECT COUNT() FROM SecurityHealthCheckRisks"
                risks_count_result = execute_salesforce_tooling_query(risks_count_query, access_token, instance_url)
                total_risks = risks_count_result.get("totalSize", 0) if risks_count_result else 0
                logger.info(f"Found {total_risks} security health check risks")
            else:
                logger.warning("Could not retrieve health score directly from SecurityHealthCheck, using fallback method")

                # 3. Get security health check data
                health_check_query = "SELECT COUNT() FROM SecurityHealthCheckRisks"
                health_check_result = execute_salesforce_tooling_query(health_check_query, access_token, instance_url)
                total_risks = health_check_result.get("totalSize", 0) if health_check_result else 0
                logger.info(f"Found {total_risks} security health check risks")

                # 4. Get risk counts by severity
                high_risks_query = "SELECT COUNT() FROM SecurityHealthCheckRisks WHERE RiskType = 'HIGH_RISK'"
                high_risks_result = execute_salesforce_tooling_query(high_risks_query, access_token, instance_url)
                high_risks = high_risks_result.get("totalSize", 0) if high_risks_result else 0
                logger.info(f"Found {high_risks} high risks")

                medium_risks_query = "SELECT COUNT() FROM SecurityHealthCheckRisks WHERE RiskType = 'MEDIUM_RISK'"
                medium_risks_result = execute_salesforce_tooling_query(medium_risks_query, access_token, instance_url)
                medium_risks = medium_risks_result.get("totalSize", 0) if medium_risks_result else 0
                logger.info(f"Found {medium_risks} medium risks")

                low_risks_query = "SELECT COUNT() FROM SecurityHealthCheckRisks WHERE RiskType = 'LOW_RISK'"
                low_risks_result = execute_salesforce_tooling_query(low_risks_query, access_token, instance_url)
                low_risks = low_risks_result.get("totalSize", 0) if low_risks_result else 0
                logger.info(f"Found {low_risks} low risks")

                # Calculate health score (simple algorithm: 100 - (high_risks * 5 + medium_risks * 2 + low_risks))
                health_score = 100 - (high_risks * 5 + medium_risks * 2 + low_risks)
                health_score = max(0, min(100, health_score))  # Ensure score is between 0 and 100
                logger.info(f"Calculated health score using fallback method: {health_score}")

            # Update task status
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_RUNNING,
                progress=70,
                message="Saving overview data"
            )

            # Save overview data to database
            if is_local_dev():
                # Use Azure Table Storage for local development
                overview_repo = TableStorageRepository(table_name="Overview")

                # Create entity
                entity = {
                    "PartitionKey": org_id,
                    "RowKey": execution_log_id or datetime.now().strftime("%Y%m%d%H%M%S"),
                    "HealthScore": health_score,
                    "TotalProfiles": total_profiles,
                    "TotalPermissionSets": total_permission_sets,
                    "TotalRisks": total_risks,
                    "HighRisks": high_risks,
                    "MediumRisks": medium_risks,
                    "LowRisks": low_risks,
                    "LastUpdated": datetime.now().isoformat()
                }

                # Insert or replace entity
                overview_repo.insert_entity(entity)
                logger.info(f"Saved overview data to Azure Table Storage for organization {org_id}")
            else:
                # Use SQL Database for production
                overview_repo = SqlDatabaseRepository(table_name="App_Overview")

                # Insert overview data
                query = """
                INSERT INTO App_Overview (OrgId, ExecutionLogId, HealthScore, TotalProfiles, TotalPermissionSets, TotalRisks, HighRisks, MediumRisks, LowRisks, LastUpdated)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                sql_params = (
                    org_id,
                    execution_log_id,
                    health_score,
                    total_profiles,
                    total_permission_sets,
                    total_risks,
                    high_risks,
                    medium_risks,
                    low_risks,
                    datetime.now().isoformat()
                )

                overview_repo.execute_non_query(query, sql_params)
                logger.info(f"Saved overview data to SQL Database for organization {org_id}")

            # Update task status
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_COMPLETED,
                progress=100,
                message="Overview data fetched and saved successfully"
            )
        except Exception as e:
            logger.error(f"Error processing overview task: {str(e)}")
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=0,
                message=f"Error processing overview task: {str(e)}"
            )
    except Exception as e:
        logger.error(f"Error processing overview task: {str(e)}")
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_FAILED,
            progress=0,
            message=f"Error processing overview task: {str(e)}"
        )

def process_health_check_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str) -> None:
    """
    Process a health check task
    """
    logger.info(f"Processing health check task {task_id} for organization {org_id}")
    processor.update_task_status(task_id, TASK_STATUS_RUNNING, 10, "Starting health check")

    access_token = params.get("access_token")
    instance_url = params.get("instance_url")
    integration_id_from_params = params.get("integration_id") # Should be same as org_id

    if not all([access_token, instance_url, integration_id_from_params]):
        err_msg = "Missing access_token, instance_url, or integration_id in task parameters for health check."
        logger.error(f"[HealthCheckTask {task_id}] {err_msg}")
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, err_msg)
        return
    
    if org_id != integration_id_from_params:
        logger.warning(f"[HealthCheckTask {task_id}] org_id ({org_id}) and integration_id_from_params ({integration_id_from_params}) mismatch. Using org_id.")

    try:
        logger.info(f"[HealthCheckTask {task_id}] Fetching security health check risks for {org_id} using provided token.")
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # fetch_security_health_check_risks is async, so await it.
        risks_coroutine = fetch_security_health_check_risks(access_token, instance_url)
        risks = loop.run_until_complete(risks_coroutine) 
        logger.debug(f"[HealthCheckTask {task_id}] Risks fetched: {json.dumps(risks) if risks else 'None'}")
        
        # calculate_health_score is also async, await it.
        health_score_coroutine = calculate_health_score(risks) 
        health_score = loop.run_until_complete(health_score_coroutine)
        logger.debug(f"[HealthCheckTask {task_id}] Health score calculated: {health_score}")
        
        loop.close()
        
        logger.info(f"[HealthCheckTask {task_id}] Calculated health score for {org_id}: {health_score}")
        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 70, f"Health score calculated: {health_score}")

        # Save risks to appropriate storage 
        logger.info(f"[HealthCheckTask {task_id}] Health check data (risks count: {len(risks)}) for {org_id} would be saved here.")

        # --- NEW: Process and store policies results (Best Practice Comparison) ---
        try:
            from blueprints.security_analysis import process_and_store_policies_results
            logger.info(f"[HealthCheckTask {task_id}] Processing and storing policies results for {org_id}...")
            # Fetch the TaskStatus entity for this task to get the correct ExecutionLogId
            task_status = processor.get_task_status(task_id)
            task_status_id = None
            if task_status:
                task_status_id = task_status.get("execution_log_id") or task_status.get("ExecutionLogId")
            logger.debug(f"[HealthCheckTask {task_id}] Using TaskStatusId for PoliciesResult: {task_status_id}")
            process_and_store_policies_results(risks, org_id, task_status_id=task_status_id)
            logger.info(f"[HealthCheckTask {task_id}] Policies results stored for {org_id}.")
        except Exception as e:
            logger.error(f"[HealthCheckTask {task_id}] Error processing/storing policies results for {org_id}: {str(e)}")
        # --- END NEW ---

        # Update Integration record 
        integration_repo_table = get_integration_table_repo()
        integration_repo_sql = get_integration_sql_repo()
        now_iso = datetime.now().isoformat()

        if is_local_dev() and integration_repo_table:
            integration_repo_table.update_entity({
                "PartitionKey": "integration", 
                "RowKey": org_id, 
                "HealthScore": str(health_score),
                "LastHealthCheckScan": now_iso
            })
        elif not is_local_dev() and integration_repo_sql:
            update_query = "UPDATE App_Integration SET HealthScore = ?, LastHealthCheckScan = ? WHERE Id = ?"
            integration_repo_sql.execute_non_query(update_query, (str(health_score), now_iso, org_id))
        
        logger.info(f"[HealthCheckTask {task_id}] Updated integration {org_id} with health score {health_score}.")

        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_COMPLETED,
            progress=100,
            message=f"Health check completed for {org_id}. Score: {health_score}",
            result=json.dumps({"health_score": health_score, "risks_count": len(risks) if isinstance(risks, list) else 0})
        )
    except TypeError as te:
        logger.error(f"[HealthCheckTask {task_id}] TypeError processing health check task for {org_id}: {str(te)}")
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"TypeError during health check: {str(te)}")    
    except Exception as e:
        logger.error(f"[HealthCheckTask {task_id}] Error processing health check task for {org_id}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"Error during health check: {str(e)}")

def process_profiles_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str) -> None:
    """
    Process a profiles task

    Args:
        processor: Background processor
        task_id: Task ID
        org_id: Organization ID
        user_id: User ID
        params: Task parameters
        execution_log_id: Execution log ID
    """
    logger.info(f"Processing profiles task {task_id} for organization {org_id}")
    processor.update_task_status(task_id, TASK_STATUS_RUNNING, 10, "Starting profiles data collection")

    access_token = params.get("access_token")
    instance_url = params.get("instance_url")
    integration_id_from_params = params.get("integration_id")

    if not all([access_token, instance_url, integration_id_from_params]):
        err_msg = "Missing access_token, instance_url, or integration_id in task parameters for profiles task."
        logger.error(f"[ProfilesTask {task_id}] {err_msg}")
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, err_msg)
        return

    if org_id != integration_id_from_params:
        logger.warning(f"[ProfilesTask {task_id}] org_id ({org_id}) and integration_id_from_params ({integration_id_from_params}) mismatch. Using org_id from task.")

    try:
        from blueprints.profile_system_permissions import (
            fetch_profile_system_permissions,
            fetch_permission_set_system_permissions,
            save_profile_system_permissions,
            save_permission_set_system_permissions
        )

        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=40,
            message="Fetching profile system permissions from Salesforce using provided token."
        )

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        profiles_coroutine = fetch_profile_system_permissions(access_token, instance_url)
        profiles = loop.run_until_complete(profiles_coroutine)
        logger.info(f"Fetched {len(profiles)} profiles with system permissions from Salesforce")

        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=60,
            message="Fetching permission set system permissions from Salesforce using provided token."
        )

        # fetch_permission_set_system_permissions is sync, call directly
        permission_sets = fetch_permission_set_system_permissions(access_token, instance_url)
        logger.info(f"Fetched {len(permission_sets)} permission sets with system permissions from Salesforce")

        loop.close()

        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=70,
            message="Saving profile system permissions"
        )
        profile_stats = save_profile_system_permissions(profiles, org_id)
        logger.info(f"Saved {profile_stats['inserted']} profiles with system permissions")

        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=85,
            message="Saving permission set system permissions"
        )
        permission_set_stats = save_permission_set_system_permissions(permission_sets, org_id)
        logger.info(f"Saved {permission_set_stats['inserted']} permission sets with system permissions")

        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_COMPLETED,
            progress=100,
            message="Profile and permission set system permissions fetched and saved successfully"
        )
    except TypeError as te: # Specific catch for TypeError
        logger.error(f"[ProfilesTask {task_id}] TypeError: {str(te)}")
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"TypeError: {str(te)}")
    except NameError as ne: # Specific catch for NameError
        logger.error(f"[ProfilesTask {task_id}] NameError: {str(ne)}")
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"NameError: {str(ne)}")
    except Exception as e:
        logger.error(f"[ProfilesTask {task_id}] Error processing profiles task: {str(e)}") # Corrected log task_id
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"Error processing profiles task: {str(e)}")

def process_data_export_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str) -> None:
    """
    Process a data export task

    Args:
        processor: Background processor
        task_id: Task ID
        org_id: Organization ID
        user_id: User ID
        params: Task parameters
        execution_log_id: Execution log ID
    """
    try:
        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=20,
            message="Preparing data for export"
        )

        # Get integration details
        integration = get_integration_by_id(org_id)
        if not integration:
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=0,
                message=f"Integration with ID {org_id} not found"
            )
            return

        # Extract export parameters
        export_format = params.get("format", "csv")
        data_type = params.get("data_type", "health_check")

        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=40,
            message=f"Generating {export_format.upper()} export for {data_type}"
        )

        # In a real implementation, you would fetch data and generate the export file
        # For now, we'll just simulate the process

        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=70,
            message="Saving export file"
        )

        # Generate a file name for the export
        file_name = f"{integration.get('name', 'export')}-{data_type}-{datetime.now().strftime('%Y%m%d%H%M%S')}.{export_format}"

        # In a real implementation, you would save the file to blob storage or a database
        # For now, we'll just simulate the process

        # Update task status with the result (file URL or download token)
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_COMPLETED,
            progress=100,
            message="Export completed successfully",
            result=json.dumps({
                "file_name": file_name,
                "format": export_format,
                "data_type": data_type,
                "download_url": f"/api/download/{file_name}",
                "expiry": (datetime.now() + timedelta(days=7)).isoformat()
            })
        )
    except Exception as e:
        logger.error(f"Error processing data export task: {str(e)}")
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_FAILED,
            progress=0,
            message=f"Error processing data export task: {str(e)}"
        )

def process_report_generation_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str) -> None:
    """
    Process a report generation task

    Args:
        processor: Background processor
        task_id: Task ID
        org_id: Organization ID
        user_id: User ID
        params: Task parameters
        execution_log_id: Execution log ID
    """
    try:
        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=20,
            message="Preparing report data"
        )

        # Get integration details
        integration = get_integration_by_id(org_id)
        if not integration:
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=0,
                message=f"Integration with ID {org_id} not found"
            )
            return

        # Extract report parameters
        report_type = params.get("report_type", "security_summary")
        report_format = params.get("format", "pdf")

        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=40,
            message=f"Generating {report_type} report in {report_format.upper()} format"
        )

        # In a real implementation, you would fetch data and generate the report
        # For now, we'll just simulate the process

        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=70,
            message="Saving report file"
        )

        # Generate a file name for the report
        file_name = f"{integration.get('name', 'report')}-{report_type}-{datetime.now().strftime('%Y%m%d%H%M%S')}.{report_format}"

        # In a real implementation, you would save the file to blob storage or a database
        # For now, we'll just simulate the process

        # Update task status with the result (file URL or download token)
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_COMPLETED,
            progress=100,
            message="Report generated successfully",
            result=json.dumps({
                "file_name": file_name,
                "report_type": report_type,
                "format": report_format,
                "download_url": f"/api/download/{file_name}",
                "expiry": (datetime.now() + timedelta(days=7)).isoformat()
            })
        )
    except Exception as e:
        logger.error(f"Error processing report generation task: {str(e)}")
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_FAILED,
            progress=0,
            message=f"Error processing report generation task: {str(e)}"
        )

def process_scheduled_scan_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str) -> None:
    """
    Process a scheduled scan task

    Args:
        processor: Background processor
        task_id: Task ID
        org_id: Organization ID
        user_id: User ID
        params: Task parameters
        execution_log_id: Execution log ID
    """
    try:
        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=10,
            message="Starting scheduled scan"
        )

        # Get integration details
        integration = get_integration_by_id(org_id)
        if not integration:
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=0,
                message=f"Integration with ID {org_id} not found"
            )
            return

        # Extract scan parameters
        scan_types = params.get("scan_types", ["overview", "health_check", "profiles", "guest_user_risks"])

        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=20,
            message=f"Scheduling {len(scan_types)} scan tasks"
        )

        # Enqueue individual scan tasks
        task_ids = []
        for scan_type in scan_types:
            # Map scan type to task type
            task_type_map = {
                "overview": TASK_TYPE_OVERVIEW,
                "health_check": TASK_TYPE_HEALTH_CHECK,
                "profiles": TASK_TYPE_PROFILES,
                "guest_user_risks": TASK_TYPE_GUEST_USER_RISKS
            }

            task_type = task_type_map.get(scan_type)
            if task_type:
                # Enqueue the task
                new_task_id = processor.enqueue_task(
                    task_type=task_type,
                    org_id=org_id,
                    user_id=user_id,
                    params=params.get(scan_type, {}),
                    priority=TASK_PRIORITY_MEDIUM
                )

                if new_task_id:
                    task_ids.append(new_task_id)
                    logger.info(f"Enqueued {scan_type} task {new_task_id} for scheduled scan {task_id}")

        # Update task status with the result (list of task IDs)
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_COMPLETED,
            progress=100,
            message=f"Scheduled scan completed successfully. Enqueued {len(task_ids)} tasks.",
            result=json.dumps({
                "task_ids": task_ids,
                "scan_types": scan_types
            })
        )
    except Exception as e:
        logger.error(f"Error processing scheduled scan task: {str(e)}")
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_FAILED,
            progress=0,
            message=f"Error processing scheduled scan task: {str(e)}"
        )

def process_metadata_extraction_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: Optional[str] = None) -> None:
    """
    Process metadata extraction task

    Args:
        processor: Background processor instance
        task_id: Task ID
        org_id: Organization ID (integration ID)
        user_id: User ID
        params: Task parameters
        execution_log_id: Execution log ID
    """
    # Idempotency check: skip if already completed/failed/cancelled
    task_status = processor.get_task_status(task_id)
    if task_status and task_status["status"] in (TASK_STATUS_COMPLETED, TASK_STATUS_FAILED, TASK_STATUS_CANCELLED):
        logger.info(f"[Idempotency] Task {task_id} already {task_status['status']}, skipping processing.")
        return

    logger.info(f"Processing metadata extraction task {task_id} for integration {org_id}")
    processor.update_task_status(task_id, TASK_STATUS_RUNNING, 10, "Initializing metadata extraction")

    # Get access_token and instance_url from params, passed by sfdc_authenticate_task
    access_token = params.get("access_token")
    instance_url = params.get("instance_url")
    integration_id_from_params = params.get("integration_id") # Should be same as org_id (which is integration_id for this task)

    if not all([access_token, instance_url, integration_id_from_params]):
        err_msg = "Missing access_token, instance_url, or integration_id in task parameters for metadata extraction."
        logger.error(f"[MetadataTask {task_id}] {err_msg}")
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, err_msg)
        return

    if org_id != integration_id_from_params:
        logger.warning(f"[MetadataTask {task_id}] org_id ({org_id}) and integration_id_from_params ({integration_id_from_params}) mismatch. Using org_id from task.")

    integration_id = org_id # Use org_id as the integration_id for clarity in this task

    try:
        # Get integration details for org name
        integration_repo = get_integration_table_repo() # Use the imported function
        integration_entity = None
        if is_local_dev() and integration_repo:
            integration_entity = integration_repo.get_entity("integration", integration_id)
        elif not is_local_dev():
            sql_repo = get_integration_sql_repo() # Use the imported function
            if sql_repo:
                query = "SELECT Name, Type, Environment FROM App_Integration WHERE Id = ?"
                results = sql_repo.execute_query(query, (integration_id,))
                if results and len(results) > 0:
                    integration_entity = {"Name": results[0][0], "Type": results[0][1], "Environment": results[0][2]}

        if not integration_entity:
            error_message = f"Integration with ID {integration_id} not found for metadata task."
            logger.error(f"[MetadataTask {task_id}] {error_message}")
            processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, error_message)
            return

        integration_type = integration_entity.get("Type", "Salesforce")
        org_name = integration_entity.get("Name", "unknown-org")
        environment = integration_entity.get("Environment", "production")

        logger.info(f"[MetadataTask {task_id}] Extracting metadata for {integration_type} integration {integration_id} ({org_name}) using provided token.")

        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=30,
            message="Extracting metadata from Salesforce using provided token."
        )

        # Extract metadata
        logger.info(f"[ORCHESTRATION] extract_salesforce_metadata called from process_metadata_extraction_task. Task ID: {task_id}")
        blob_repo = BlobStorageRepository(container_name="salesforce-metadata")
        success, error_message, returned_blob_path = extract_salesforce_metadata(
            access_token=access_token,
            instance_url=instance_url,
            blob_repo=blob_repo,
            integration_id=integration_id,
            org_name=org_name,
            orchestration_context=f"process_metadata_extraction_task:{task_id}",
            task_id=task_id  # Pass the task_id for consistent output folder
        )

        if not success:
            logger.error(f"[MetadataTask {task_id}] Failed to extract metadata from Salesforce: {error_message}")
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=100,
                message=f"Failed to extract metadata: {error_message}"
            )
            return

        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=70,
            message="Metadata successfully retrieved/extracted."
        )

        # Update integration with last scan timestamp
        integration_repo_table = get_integration_table_repo()
        if is_local_dev() and integration_repo_table:
            integration_repo_table.update_entity({"PartitionKey": "integration", "RowKey": integration_id, "LastMetadataScanTime": datetime.now().isoformat()})
        elif not is_local_dev():
            sql_repo = get_integration_sql_repo()
            if sql_repo:
                sql_repo.execute_non_query("UPDATE App_Integration SET LastMetadataScanTime = ? WHERE Id = ?", (datetime.now().isoformat(), integration_id))

        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_COMPLETED,
            progress=100,
            message="Metadata extraction completed successfully.",
            result=json.dumps({
                "blob_path": returned_blob_path,
                "timestamp": datetime.now().isoformat()
            })
        )

        # Fetch the latest TaskStatus for this task_id to get blob_path
        task_status = processor.get_task_status(task_id)
        blob_prefix = None
        if task_status and task_status.get("result"):
            try:
                result_json = json.loads(task_status["result"])
                blob_prefix = result_json.get("blob_path")
            except Exception as e:
                logger.error(f"Could not parse blob_path from result: {e}")

        if blob_prefix:
            from shared.data_access import enqueue_policy_tasks
            enqueue_policy_tasks(
                processor=processor,
                org_id=org_id,
                user_id=user_id,
                policy_name="Profiles and Permissions",
                params={
                    "org_name": org_name,
                    "blob_prefix": blob_prefix,
                    "integration_id": org_id,
                    "access_token": access_token,
                    "instance_url": instance_url,
                    "environment": environment
                },
                priority=TASK_PRIORITY_MEDIUM
            )
            # Enqueue Static Code Analysis task
            enqueue_policy_tasks(
                processor=processor,
                org_id=org_id,
                user_id=user_id,
                policy_name="Static Code Analysis (PMD)",
                params={
                    "blob_prefix": blob_prefix,
                    "integration_id": org_id
                },
                priority=TASK_PRIORITY_MEDIUM
            )
        else:
            logger.error("Could not enqueue policy tasks: blob_prefix is missing.")

    except Exception as e:
        error_message = f"Error processing metadata extraction task: {str(e)}"
        logger.error(error_message)
        import traceback
        logger.error(traceback.format_exc())

        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_FAILED,
            progress=100,
            message=error_message
        )

def process_notification_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str) -> None:
    """
    Process a notification task

    Args:
        processor: Background processor
        task_id: Task ID
        org_id: Organization ID
        user_id: User ID
        params: Task parameters
        execution_log_id: Execution log ID
    """
    try:
        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=20,
            message="Preparing notification"
        )

        # Extract notification parameters
        notification_type = params.get("notification_type", "task_completed")
        recipients = params.get("recipients", [])
        subject = params.get("subject", "AtomSec Notification")
        message = params.get("message", "")
        related_task_id = params.get("related_task_id", "")

        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=40,
            message=f"Sending {notification_type} notification to {len(recipients)} recipients"
        )

        # In a real implementation, you would send the notification via email, SMS, or in-app
        # For now, we'll just simulate the process

        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=70,
            message="Recording notification delivery"
        )

        # In a real implementation, you would record the notification delivery in a database
        # For now, we'll just simulate the process

        # Update task status with the result
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_COMPLETED,
            progress=100,
            message="Notification sent successfully",
            result=json.dumps({
                "notification_type": notification_type,
                "recipients": recipients,
                "subject": subject,
                "sent_at": datetime.now().isoformat(),
                "related_task_id": related_task_id
            })
        )
    except Exception as e:
        logger.error(f"Error processing notification task: {str(e)}")
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_FAILED,
            progress=0,
            message=f"Error processing notification task: {str(e)}"
        )

def process_sfdc_authenticate_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str) -> None:
    """
    Processes the Salesforce authentication task.
    Retrieves credentials, authenticates with Salesforce, and if successful,
    enqueues subsequent data collection tasks.
    """
    logger.info(f"Processing Salesforce authentication task {task_id} for integration {org_id}")
    processor.update_task_status(task_id, TASK_STATUS_RUNNING, 10, f"Starting Salesforce authentication for {org_id}")

    integration_id = params.get("integration_id") # This should be the same as org_id for this task
    if not integration_id:
        logger.error(f"[AuthTask {task_id}] Missing integration_id in task parameters.")
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, "Missing integration_id in task parameters.")
        return

    # Get Integration record to retrieve TenantUrl and Environment
    integration_repo_table = get_integration_table_repo()
    integration_repo_sql = get_integration_sql_repo()
    integration_entity = None

    if is_local_dev():
        if integration_repo_table:
            integration_entity = integration_repo_table.get_entity("integration", integration_id)
    else:
        if integration_repo_sql:
            query = "SELECT Id, Name, TenantUrl, Type, Environment, IsActive FROM App_Integration WHERE Id = ?"
            results = integration_repo_sql.execute_query(query, (integration_id,))
            if results and len(results) > 0:
                row = results[0]
                integration_entity = {
                    "RowKey": row[0], "Name": row[1], "TenantUrl": row[2],
                    "Type": row[3], "Environment": row[4], "IsActive": row[5]
                }

    if not integration_entity:
        logger.error(f"[AuthTask {task_id}] Integration {integration_id} not found.")
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"Integration {integration_id} not found.")
        return

    tenant_url = integration_entity.get("TenantUrl")
    environment = integration_entity.get("Environment", "production")
    is_sandbox = environment.lower() == "sandbox"
    service_name = f"salesforce-{integration_id}"

    logger.info(f"[AuthTask {task_id}] Attempting to retrieve credentials for {service_name}")

    # Retrieve credentials
    client_id = None
    client_secret = None
    jwt_username = None
    private_key = None
    auth_flow_type = None # To store whether 'jwt' or 'client_credentials'

    try:
        if is_local_dev():
            logger.info(f"[AuthTask {task_id}] Local dev: Retrieving creds from Credentials table for {service_name}")
            cred_table_repo = get_table_storage_repository("Credentials")
            if cred_table_repo:
                client_id_entity = cred_table_repo.get_entity(service_name, "client-id")
                if client_id_entity:
                    client_id = client_id_entity.get("Value")
                    auth_flow_type = client_id_entity.get("AuthFlow") # AuthFlow hint
                
                if auth_flow_type == "jwt":
                    username_entity = cred_table_repo.get_entity(service_name, "username")
                    if username_entity: jwt_username = username_entity.get("Value")
                    pkey_entity = cred_table_repo.get_entity(service_name, "private-key")
                    if pkey_entity: private_key = pkey_entity.get("Value")
                else: # Default to client_credentials or if AuthFlow is not jwt
                    secret_entity = cred_table_repo.get_entity(service_name, "client-secret")
                    if secret_entity: client_secret = secret_entity.get("Value")
                    if client_id and client_secret : auth_flow_type = "client_credentials" # Confirm if not set
                if client_id and not auth_flow_type:
                    # if auth flow not specified, try to infer based on available creds
                    if jwt_username and private_key: auth_flow_type = "jwt"
                    elif client_secret: auth_flow_type = "client_credentials"
            else:
                raise Exception("Credentials table repository not available in local dev.")
        else:
            logger.info(f"[AuthTask {task_id}] Production: Retrieving creds from Key Vault for {service_name}")
            client_id = get_secret(f"{service_name}-client-id")
            # Check for AuthFlow hint if stored with client-id or as a separate secret
            auth_flow_hint = get_secret(f"{service_name}-auth-flow") 
            if auth_flow_hint and auth_flow_hint.lower() in ["jwt", "client_credentials"]:
                auth_flow_type = auth_flow_hint.lower()
            
            if auth_flow_type == "jwt":
                jwt_username = get_secret(f"{service_name}-username")
                private_key = get_secret(f"{service_name}-private-key")
            else: # Default to client_credentials or if AuthFlow is not jwt
                client_secret = get_secret(f"{service_name}-client-secret")
                if client_id and client_secret : auth_flow_type = "client_credentials"
            if client_id and not auth_flow_type:
                # Try to infer if not specified
                jwt_username_check = get_secret(f"{service_name}-username")
                if jwt_username_check: # If username exists, assume JWT
                    jwt_username = jwt_username_check
                    private_key = get_secret(f"{service_name}-private-key")
                    auth_flow_type = "jwt"
                else: # else assume client_credentials
                    client_secret = get_secret(f"{service_name}-client-secret")
                    auth_flow_type = "client_credentials"

        if not client_id:
            raise Exception("Client ID could not be retrieved.")
        if auth_flow_type == "jwt" and (not jwt_username or not private_key):
            raise Exception("JWT username or private key could not be retrieved for JWT flow.")
        if auth_flow_type == "client_credentials" and not client_secret:
            raise Exception("Client Secret could not be retrieved for Client Credentials flow.")
        if not auth_flow_type:
            raise Exception("Could not determine authentication flow type (JWT or Client Credentials).")

        logger.info(f"[AuthTask {task_id}] Credentials retrieved. Determined auth flow: {auth_flow_type}")
        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 30, f"Credentials retrieved. Attempting {auth_flow_type} authentication.")

        logger.info(f"[AuthTask {task_id}] Calling central authentication service. Flow type: {auth_flow_type}")
        auth_step_log = [f"Central Auth Init. Flow: {auth_flow_type}, User: {jwt_username if auth_flow_type == 'jwt' else 'N/A (CC)'}, ClientID: {client_id[:5]}..."]

        auth_success, auth_error_message, sf_instance, connection_details = authenticate_salesforce_integration(
            auth_flow_type=auth_flow_type,
            tenant_url=tenant_url, # This is the my-domain or login URL base
            environment=environment,
            client_id=client_id,
            client_secret=client_secret,
            jwt_username=jwt_username,
            private_key=private_key
        )

        # Log all steps from the central auth service if it returns them, or rely on its internal logging.
        # For simplicity, we assume central service logs sufficiently.

        if auth_success and sf_instance and connection_details:
            access_token = connection_details.get("access_token")
            instance_url_fqdn = connection_details.get("instance_url") # This is the full FQDN like https://mydomain.my.salesforce.com

            if not access_token or not instance_url_fqdn:
                logger.error(f"[AuthTask {task_id}] Central auth succeeded but access_token or instance_url missing in returned details.")
                processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, "Auth token/instance URL missing post central auth.")
                return
            
            logger.info(f"[AuthTask {task_id}] Salesforce central authentication successful for {org_id}. Instance URL: {instance_url_fqdn}")
            processor.update_task_status(task_id, TASK_STATUS_RUNNING, 70, "Authentication successful. Enqueuing data collection tasks.")

            common_task_params = {
                "access_token": access_token,
                "instance_url": instance_url_fqdn, # Use the FQDN from auth service
                "integration_id": integration_id,
                "tenant_url": tenant_url, 
                "environment": environment
            }
            
            # Overview data collection (medium priority)
            # processor.enqueue_task(TASK_TYPE_OVERVIEW, org_id, user_id, params=common_task_params.copy(), priority=TASK_PRIORITY_MEDIUM)
            # Health Check security analysis (high priority)
            processor.enqueue_task(TASK_TYPE_HEALTH_CHECK, org_id, user_id, params=common_task_params.copy(), priority=TASK_PRIORITY_HIGH)
            # Profiles and Permissions retrieval (medium priority)
            # processor.enqueue_task(TASK_TYPE_PROFILES, org_id, user_id, params=common_task_params.copy(), priority=TASK_PRIORITY_MEDIUM)
            # Metadata extraction (high priority)
            # Idempotency check: only enqueue if no pending/running extraction task exists
            latest_metadata_task = processor.get_latest_task_for_org(org_id, TASK_TYPE_METADATA_EXTRACTION)
            if latest_metadata_task and latest_metadata_task["status"] in (TASK_STATUS_PENDING, TASK_STATUS_RUNNING):
                logger.info(f"[AuthTask {task_id}] Skipping metadata extraction enqueue for {org_id}: existing task {latest_metadata_task['task_id']} is {latest_metadata_task['status']}")
            else:
                processor.enqueue_task(TASK_TYPE_METADATA_EXTRACTION, org_id, user_id, params=common_task_params.copy(), priority=TASK_PRIORITY_HIGH)
                logger.info(f"[AuthTask {task_id}] Enqueued metadata extraction task for {org_id}.")

            logger.info(f"[AuthTask {task_id}] Successfully enqueued data collection tasks for {org_id}.")
            processor.update_task_status(task_id, TASK_STATUS_COMPLETED, 100, f"Authentication successful. Data collection tasks for {org_id} enqueued.")
            
            # Update LastScan timestamp on the integration record upon successful auth and task enqueuing
            now_iso = datetime.now().isoformat()
            if is_local_dev() and integration_repo_table:
                integration_repo_table.update_entity({
                    "PartitionKey": "integration", 
                    "RowKey": integration_id, 
                    "LastScan": now_iso # Update actual LastScan here
                })
            elif not is_local_dev() and integration_repo_sql:
                update_query = "UPDATE App_Integration SET LastScan = ? WHERE Id = ?"
                integration_repo_sql.execute_non_query(update_query, (now_iso, integration_id))

        else:
            logger.error(f"[AuthTask {task_id}] Salesforce central authentication failed for {org_id}: {auth_error_message}")
            processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"Salesforce authentication failed: {auth_error_message}")
            # Update Integration IsActive to False
            if is_local_dev() and integration_repo_table:
                integration_repo_table.update_entity({"PartitionKey": "integration", "RowKey": integration_id, "IsActive": False})
            elif not is_local_dev() and integration_repo_sql:
                update_query = "UPDATE App_Integration SET IsActive = ? WHERE Id = ?"
                integration_repo_sql.execute_non_query(update_query, (False, integration_id))
            logger.info(f"[AuthTask {task_id}] Marked integration {integration_id} as inactive due to auth failure.")

    except Exception as e:
        logger.error(f"[AuthTask {task_id}] Error during Salesforce authentication task for {org_id}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"Error during authentication: {str(e)}")
        # Optionally mark integration as inactive here too if a general error occurs during credential fetch etc.
        if integration_id: # Check if integration_id was retrieved before error
            try:
                if is_local_dev() and integration_repo_table:
                    integration_repo_table.update_entity({"PartitionKey": "integration", "RowKey": integration_id, "IsActive": False})
                elif not is_local_dev() and integration_repo_sql:
                    update_query = "UPDATE App_Integration SET IsActive = ? WHERE Id = ?"
                    integration_repo_sql.execute_non_query(update_query, (False, integration_id))
                logger.info(f"[AuthTask {task_id}] Marked integration {integration_id} as inactive due to an exception in the auth task.")
            except Exception as update_err:
                logger.error(f"[AuthTask {task_id}] Failed to mark integration {integration_id} as inactive after exception: {str(update_err)}")

def process_profiles_permission_sets_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: dict, execution_log_id: str = None) -> None:
    """
    Compare profile metadata in blob storage with best practices, store results in PoliciesResult, and mark task as completed.
    For each profile, fetch the number of active (not frozen) users assigned to that profile and store in ProfileAssignmentCount table.
    Also process permission set best-practice analysis and store those results with the same execution_log_id.
    """
    import asyncio
    org_name = params.get('org_name')
    blob_prefix = params.get('blob_prefix')
    access_token = params.get('access_token')
    instance_url = params.get('instance_url')
    environment = params.get('environment', 'production')
    if not org_name or not blob_prefix:
        logging.error("Missing org_name or blob_prefix in params for profiles_permission_sets task.")
        return

    import os
    from shared.salesforce_utils import execute_salesforce_query
    best_practices_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'best_practices', 'Profiles_PermissionSetRisks-BestPractice.xml'))
    best_practices = load_best_practices_xml(best_practices_path)
    blob_repo = BlobStorageRepository(container_name="salesforce-metadata")

    try:
        # Call the core logic and get results
        profile_results = process_profiles_in_blob(blob_repo, blob_prefix, best_practices)

        # --- Build normalized Salesforce profile name map ---
        normalized_profile_id_map = {}
        profile_active_user_counts = {}
        if access_token and instance_url:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            # Query all profiles
            profile_query = "SELECT Id, Name FROM Profile"
            profile_query_result = loop.run_until_complete(
                execute_salesforce_query(
                    profile_query,
                    access_token=access_token,
                    instance_url=instance_url,
                    environment=environment
                )
            )
            if profile_query_result and "records" in profile_query_result:
                for rec in profile_query_result["records"]:
                    norm_name = normalize_profile_name(rec["Name"])
                    normalized_profile_id_map[norm_name] = rec["Id"]
            # Query active user counts per profile (PII-free)
            try:
                user_count_query = ("SELECT ProfileId, Profile.Name, COUNT(Id) cnt "
                                   "FROM User WHERE IsActive = TRUE AND Id NOT IN "
                                   "(SELECT UserId FROM UserLogin WHERE IsFrozen = true) "
                                   "GROUP BY ProfileId, Profile.Name")
                user_count_result = loop.run_until_complete(
                    execute_salesforce_query(
                        user_count_query,
                        access_token=access_token,
                        instance_url=instance_url,
                        environment=environment
                    )
                )
                if user_count_result and 'records' in user_count_result:
                    for rec in user_count_result['records']:
                        profile_id = rec.get('ProfileId')
                        profile_name = rec.get('Name')
                        count = rec.get('cnt')
                        if profile_id and profile_name and count is not None:
                            profile_active_user_counts[profile_id] = {
                                'ProfileId': profile_id,
                                'ProfileName': profile_name,
                                'AssignmentCount': int(count)
                            }
                else:
                    logger.error("Active user count query returned no records or unexpected format. Skipping assignment count insertion for this run.")
            except Exception as e:
                logger.error(f"Error querying active user counts per profile: {e}")
                logger.error("Skipping assignment count insertion for this run due to query error.")
            finally:
                loop.close()

        unmatched_profiles = []
        repo = get_policies_result_table_repo()
        assignment_repo = get_table_storage_repository("ProfileAssignmentCount")
        inserted_count = 0
        inserted_profiles = set()  # Track inserted profile names to prevent duplicates
        for profile_name, results in profile_results.items():
            norm_blob_name = normalize_profile_name(profile_name)
            profile_id = normalized_profile_id_map.get(norm_blob_name)
            # Special-case: Admin.profile should match System Administrator
            if not profile_id and norm_blob_name == 'admin':
                profile_id = normalized_profile_id_map.get('system administrator')
            if not profile_id and norm_blob_name == 'standard':
                profile_id = normalized_profile_id_map.get('standard platform user')
            if not profile_id:
                for sf_name, sf_id in normalized_profile_id_map.items():
                    if sf_name.replace(' ', '') == norm_blob_name:
                        profile_id = sf_id
                        break
            if not profile_id:
                norm_blob_no_space = norm_blob_name.replace(' ', '')
                for sf_name, sf_id in normalized_profile_id_map.items():
                    if sf_name.replace(' ', '') == norm_blob_no_space:
                        profile_id = sf_id
                        break
            if not profile_id and FUZZY_AVAILABLE and normalized_profile_id_map:
                match, score = fuzzy_process.extractOne(norm_blob_name, list(normalized_profile_id_map.keys()))
                if score >= 90:
                    profile_id = normalized_profile_id_map[match]
            if not profile_id:
                unmatched_profiles.append(profile_name)

            # Only insert one PoliciesResult per profile per run
            if profile_name not in inserted_profiles and results and len(results) > 0:
                entity = {
                    'PartitionKey': str(org_id),
                    'RowKey': f"{profile_name}-{datetime.now().strftime('%Y%m%d%H%M%S%f')}",
                    'OrgValue': json.dumps(results),  # Store the full array as JSON
                    'IntegrationId': str(org_id),
                    'TaskStatusId': execution_log_id,
                    'CreatedAt': datetime.now().isoformat(),
                    'ProfileName': profile_name,
                    'Type': 'ProfilePermissions',
                }
                try:
                    repo.insert_entity(entity)
                    inserted_count += 1
                    inserted_profiles.add(profile_name)
                except Exception as e:
                    logger.error(f"Failed to insert PoliciesResult for profile {profile_name}: {e}")

            # Always insert assignment count for every profile (restore previous behavior)
            assignment_count = 0
            try:
                if profile_id and profile_id in profile_active_user_counts:
                    assignment_count = profile_active_user_counts[profile_id]['AssignmentCount']
                if assignment_repo:
                    assignment_entity = {
                        'PartitionKey': str(org_id),
                        'RowKey': f"{profile_name}-{datetime.now().strftime('%Y%m%d%H%M%S%f')}",
                        'ProfileName': profile_name,
                        'ProfileId': profile_id or '',
                        'AssignmentCount': assignment_count,
                        'IntegrationId': str(org_id),
                        'TaskStatusId': execution_log_id,
                        # PoliciesResultProfileRowKey is a logical foreign key to PoliciesResult.RowKey (may be missing if no PoliciesResult row)
                        'PoliciesResultProfileRowKey': None,
                        'CreatedAt': datetime.now().isoformat(),
                        'Type': 'ProfilePermissions',
                    }
                    assignment_repo.insert_entity(assignment_entity)

                    # --- Restore permission set assignment count logic per profile ---
                    if profile_id and access_token and instance_url:
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        # 1. Get all active user IDs for this profile
                        user_ids_query = f"SELECT Id FROM User WHERE ProfileId = '{profile_id}' AND IsActive = true AND Id NOT IN (SELECT UserId FROM UserLogin WHERE IsFrozen = true)"
                        user_ids_result = loop.run_until_complete(
                            execute_salesforce_query(
                                user_ids_query,
                                access_token=access_token,
                                instance_url=instance_url,
                                environment=environment
                            )
                        )
                        user_ids = [rec['Id'] for rec in user_ids_result.get('records', [])] if user_ids_result and 'records' in user_ids_result else []
                        if user_ids:
                            # 2. Query PermissionSetAssignment for these user IDs, group by PermissionSet.Name
                            # Salesforce SOQL has a 2000 item IN clause limit, so chunk if needed
                            chunk_size = 200
                            for i in range(0, len(user_ids), chunk_size):
                                chunk = user_ids[i:i+chunk_size]
                                ids_str = ",".join([f"'{uid}'" for uid in chunk])
                                psa_query = f"SELECT PermissionSet.Name FROM PermissionSetAssignment WHERE AssigneeId IN ({ids_str})"
                                psa_result = loop.run_until_complete(
                                    execute_salesforce_query(
                                        psa_query,
                                        access_token=access_token,
                                        instance_url=instance_url,
                                        environment=environment
                                    )
                                )
                                psa_counts = {}
                                if psa_result and 'records' in psa_result:
                                    for rec in psa_result['records']:
                                        ps_name = rec.get('PermissionSet', {}).get('Name')
                                        if ps_name:
                                            psa_counts[ps_name] = psa_counts.get(ps_name, 0) + 1
                                # Insert each PermissionSet assignment count for this profile chunk
                                for ps_name, count in psa_counts.items():
                                    if count > 0:
                                        ps_assignment_entity = {
                                            'PartitionKey': str(org_id),
                                            'RowKey': f"{profile_name}-{ps_name}-{datetime.now().strftime('%Y%m%d%H%M%S%f')}",
                                            'ProfileName': profile_name,
                                            'ProfileId': profile_id or '',
                                            'PermissionSetName': ps_name,
                                            'AssignmentCount': count,
                                            'IntegrationId': str(org_id),
                                            'TaskStatusId': execution_log_id,
                                            'PoliciesResultProfileRowKey': None,
                                            'CreatedAt': datetime.now().isoformat(),
                                            'Type': 'ProfilePermissionSetAssignment',
                                        }
                                        assignment_repo.insert_entity(ps_assignment_entity)
                        loop.close()
            except Exception as e:
                logger.error(f"Error inserting assignment count for profile {profile_name}: {e}")

        # Log unmatched profiles for review
        if unmatched_profiles:
            logger.warning(f"Unmatched profiles (could not find ProfileId in Salesforce): {unmatched_profiles}")

        # After processing profiles, process permission sets with the same execution_log_id
        process_permissionsets_in_blob(
            blob_repo,
            blob_prefix,
            best_practices,
            org_id,
            execution_log_id,
            access_token=access_token,
            instance_url=instance_url,
            environment=environment
        )
        # Mark task as completed
        processor.update_task_status(
            task_id=task_id,
            status="completed",
            progress=100,
            message="Profiles and Permission Sets scan completed and results stored."
        )
    except Exception as e:
        logger.error(f"Error processing profiles_permission_sets_task: {e}")
        processor.update_task_status(
            task_id=task_id,
            status="failed",
            progress=0,
            message=f"Error processing profiles_permission_sets_task: {e}"
        )

def process_permissionsets_in_blob(blob_repo, blob_prefix, best_practices, org_id, execution_log_id, access_token=None, instance_url=None, environment='production'):
    import xml.etree.ElementTree as ET
    import urllib.parse
    logger = logging.getLogger(__name__)
    permissionset_folder = f"{blob_prefix}/permissionsets/"
    permission_set_blobs = blob_repo.list_blobs(name_starts_with=permissionset_folder)
    for blob_name in permission_set_blobs:
        if not blob_name.endswith('.permissionset'):
            continue
        permission_set_name = blob_name.split('/')[-1].replace('.permissionset', '')
        logger.debug(f"[DEBUG] Processing Permission Set: {permission_set_name}")
        try:
            xml_bytes = blob_repo.get_blob_bytes(blob_name)
            # Extract <License> (case-insensitive)
            try:
                xml_str = xml_bytes.decode('utf-8', errors='ignore')
            except Exception as e:
                logger.error(f"Failed to decode XML for {permission_set_name}: {e}")
                continue
            match = re.search(r'<license>(.*?)</license>', xml_str, re.DOTALL | re.IGNORECASE)
            license_value = match.group(1).strip() if match else ''
            normalized_license = (license_value or '').strip().lower().replace(' ', '') or 'blank'
            logger.debug(f"[DEBUG] Permission Set '{permission_set_name}': License='{license_value}' (normalized: '{normalized_license}')")
            # Parse userPermissions
            try:
                root = ET.fromstring(xml_str)
                user_permissions = []
                for perm in root.iter():
                    if perm.tag.endswith('userPermissions'):
                        name_elem = None
                        enabled_elem = None
                        for child in perm:
                            if child.tag.endswith('name'):
                                name_elem = child
                            elif child.tag.endswith('enabled'):
                                enabled_elem = child
                        if name_elem is not None and enabled_elem is not None:
                            user_permissions.append({
                                'name': name_elem.text.strip() if name_elem.text else '',
                                'enabled': enabled_elem.text.strip() if enabled_elem.text else ''
                            })
            except Exception as e:
                logger.error(f"Failed to parse userPermissions for {permission_set_name}: {e}")
                continue
            norm_names = [normalize_permission_name(p['name']) for p in user_permissions]
            # Best-practices matching
            relevant_bps = [bp for bp in best_practices if normalize_user_type(bp.get('UserType')) == normalized_license]
            matched_user_type = normalized_license if relevant_bps else 'blank'
            if not relevant_bps:
                relevant_bps = [bp for bp in best_practices if normalize_user_type(bp.get('UserType')) == 'blank']
            results_arr = []
            for bp in relevant_bps:
                bp_setting = (bp.get('SalesforceSetting') or '').strip()
                bp_standard_value = (bp.get('StandardValue') or '').strip()
                normalized_bp_setting = normalize_permission_name(bp_setting)
                ps_perm = next((p for p in user_permissions if normalize_permission_name(p['name']) == normalized_bp_setting), None)
                if ps_perm is not None:
                    ps_value = ps_perm['enabled']
                    match = normalize_value(ps_value) == normalize_value(bp_standard_value)
                    if not match:
                        results_arr.append({
                            'SalesforceSetting': bp_setting,
                            'StandardValue': bp_standard_value,
                            'PermissionSetValue-UserPermissions': ps_value,
                            'Match': False,
                            'UserType': matched_user_type,
                            'Description': bp.get('Description'),
                            'OWASP': bp.get('OWASP'),
                            'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity')
                        })
            # Only insert if there are discrepancies
            if results_arr:
                entity = {
                    'PartitionKey': str(org_id),
                    'RowKey': f"{permission_set_name}-{datetime.now().strftime('%Y%m%d%H%M%S%f')}",
                    'OrgValue': json.dumps(results_arr),
                    'IntegrationId': str(org_id),
                    'TaskStatusId': execution_log_id,
                    'CreatedAt': datetime.now().isoformat(),
                    'PermissionSetName': permission_set_name,
                    'ProfileName': '',
                    'Type': 'PermissionSetPermissions',
                }
                from shared.data_access import get_policies_result_table_repo, get_profile_assignment_count_table_repo
                get_policies_result_table_repo().insert_entity(entity)
            # Assignment logic (updated to use SOQL aggregation)
            if access_token and instance_url:
                import requests
                soql = "SELECT Assignee.Profile.Name, Assignee.ProfileId, PermissionSet.Name FROM PermissionSetAssignment"
                url = f"{instance_url}/services/data/v56.0/query/"
                headers = {"Authorization": f"Bearer {access_token}"}
                params = {"q": soql}
                resp = requests.get(url, headers=headers, params=params)
                assignment_counts = {}
                if resp.status_code == 200:
                    records = resp.json().get('records', [])
                    for rec in records:
                        assignee = rec.get('Assignee')
                        permission_set = rec.get('PermissionSet')
                        profile_id = assignee.get('ProfileId') if assignee else None
                        profile = assignee.get('Profile') if assignee else None
                        profile_name = profile.get('Name') if profile else None
                        ps_name = permission_set.get('Name') if permission_set else None
                        if profile_id and profile_name and ps_name:
                            key = (profile_id, profile_name, ps_name)
                            assignment_counts[key] = assignment_counts.get(key, 0) + 1
                    # Insert one record per (ProfileId, ProfileName, PermissionSetName) with count > 0
                    from shared.data_access import get_profile_assignment_count_table_repo
                    for (profile_id, profile_name, ps_name), count in assignment_counts.items():
                        if count > 0:
                            assignment_entity = {
                                'ExecutionLogId': execution_log_id,
                                'ProfileId': profile_id,
                                'ProfileName': profile_name,
                                'PermissionSetName': ps_name,
                                'AssignmentCount': count,
                                'Type': 'ProfilePermissionSetAssignment',
                                'CreatedAt': datetime.now().isoformat()
                            }
                            get_profile_assignment_count_table_repo().insert_entity(assignment_entity)
                else:
                    logger.warning(f"[DEBUG] Permission Set Assignment query failed, status {resp.status_code}")
        except Exception as e:
            logger.error(f"[DEBUG] Permission Set '{permission_set_name}': Error processing: {e}")

# --- Begin copied logic from process_profiles_best_practices.py ---
def normalize_value(val):
    if val is None:
        return None
    val = val.strip().lower()
    if val in ['❌ false', 'x false', 'false', 'no', 'disabled', '0']:
        return 'false'
    if val in ['✅ true', 'tickmark/true', 'true', 'yes', 'enabled', '1']:
        return 'true'
    return val

def normalize_key(key):
    if key is None:
        return None
    return key.strip().lower().replace(' ', '')

def load_best_practices_xml(xml_path):
    tree = ET.parse(xml_path)
    root = tree.getroot()
    best_practices = []
    for usertype_elem in root.findall('UserType'):
        usertype = usertype_elem.attrib.get('name', '')
        for practice in usertype_elem.findall('Practice'):
            bp = {child.tag: child.text for child in practice}
            bp['UserType'] = usertype  # Add UserType from attribute
            best_practices.append(bp)
    return best_practices

def extract_user_license_from_xml(xml_bytes):
    try:
        if isinstance(xml_bytes, bytes):
            xml_str = xml_bytes.decode('utf-8', errors='ignore')
        else:
            xml_str = xml_bytes
        match = re.search(r'<userLicense>(.*?)</userLicense>', xml_str, re.DOTALL)
        if match:
            return match.group(1).strip()
        else:
            logger.debug("No <userLicense> tag found in XML.")
    except Exception as e:
        logger.error(f"Error extracting userLicense with regex: {e}")
        logger.info(f"[DEBUG] Error extracting userLicense. XML preview: {xml_bytes[:200] if isinstance(xml_bytes, bytes) else str(xml_bytes)[:200]}")
    return ''

def normalize_permission_name(name):
    if not name:
        return ''
    return name.strip().lower().replace(' ', '').replace('_', '')

def normalize_user_type(val):
    return (val or '').strip().lower().replace(' ', '')

def parse_profile_permissions(xml_bytes):
    perms = []
    try:
        root = ET.fromstring(xml_bytes)
        # Find all userPermissions, regardless of namespace
        for perm in root.iter():
            if perm.tag.endswith('userPermissions'):
                name_elem = None
                enabled_elem = None
                for child in perm:
                    if child.tag.endswith('name'):
                        name_elem = child
                    elif child.tag.endswith('enabled'):
                        enabled_elem = child
                if name_elem is not None and enabled_elem is not None:
                    perms.append({
                        'name': name_elem.text.strip() if name_elem.text else '',
                        'enabled': enabled_elem.text.strip() if enabled_elem.text else ''
                    })
    except ET.ParseError as e:
        logger.error(f"[ERROR] XML parsing error in profile: {e}")
        logger.info(f"[DEBUG] XML parsing error. XML preview: {xml_bytes[:200] if isinstance(xml_bytes, bytes) else str(xml_bytes)[:200]}")
    except Exception as e:
        logger.error(f"[ERROR] Error parsing userPermissions: {e}")
        logger.info(f"[DEBUG] Error parsing userPermissions. XML preview: {xml_bytes[:200] if isinstance(xml_bytes, bytes) else str(xml_bytes)[:200]}")
    return perms

def process_profiles_in_blob(blob_repo, blob_prefix, best_practices, summary_output_path=None):
    import xml.etree.ElementTree as ET
    import os
    profile_folder = f"{blob_prefix}/profiles/"
    logger.info(f"[DEBUG] Looking for profiles in folder: {profile_folder}")
    blobs = blob_repo.list_blobs(name_starts_with=profile_folder)
    logger.info(f"[DEBUG] Blobs found: {blobs}")
    profile_results = {}
    summary_lines = []
    for blob_name in blobs:
        if not blob_name.endswith('.profile'):
            continue
        profile_name = os.path.basename(blob_name).replace('.profile', '')
        try:
            xml_bytes = blob_repo.get_blob_bytes(blob_name)
            if xml_bytes is None:
                logger.error(f"Failed to download blob bytes for {blob_name}")
                continue
        except Exception as e:
            logger.error(f"Error downloading blob {blob_name}: {e}")
            continue
        # Extract userLicense and userPermissions
        user_license = extract_user_license_from_xml(xml_bytes)
        user_permissions = parse_profile_permissions(xml_bytes)
        # Robust normalization and matching logic
        results_arr = []
        profile_user_type = normalize_user_type(user_license)
        relevant_bps = [bp for bp in best_practices if normalize_user_type(bp.get('UserType')) == profile_user_type]
        if not relevant_bps:
            relevant_bps = [bp for bp in best_practices if normalize_user_type(bp.get('UserType')) == 'blank']
        for bp in relevant_bps:
            bp_setting = (bp.get('SalesforceSetting') or '').strip()
            bp_standard_value = (bp.get('StandardValue') or '').strip()
            normalized_bp_setting = normalize_permission_name(bp_setting)
            profile_perm = next((p for p in user_permissions if normalize_permission_name(p['name']) == normalized_bp_setting), None)
            if profile_perm is not None:
                profile_value = profile_perm['enabled']
                match = normalize_value(profile_value) == normalize_value(bp_standard_value)
                results_arr.append({
                    'SalesforceSetting': bp_setting,
                    'StandardValue': bp_standard_value,
                    'ProfileValue': profile_value,
                    'Match': match,
                    'Description': bp.get('Description'),
                    'OWASP': bp.get('OWASP'),
                    'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity')
                })
        profile_results[profile_name] = results_arr
    if summary_output_path:
        try:
            with open(summary_output_path, 'w', encoding='utf-8') as f:
                f.write("Profile,TotalChecks,Mismatches\n")
                for line in summary_lines:
                    f.write(line + "\n")
        except Exception as e:
            logger.error(f"Error writing summary output file: {e}")
    return profile_results

def normalize_profile_name(name):
    if not name:
        return ''
    # Decode URL-encoded characters, strip whitespace, and lowercase
    return urllib.parse.unquote(name).strip().lower()
# --- End copied logic ---

def extract_license_from_permissionset_xml(xml_bytes):
    try:
        if isinstance(xml_bytes, bytes):
            xml_str = xml_bytes.decode('utf-8', errors='ignore')
        else:
            xml_str = xml_bytes
        match = re.search(r'<License>(.*?)</License>', xml_str, re.DOTALL)
        if match:
            return match.group(1).strip()
        else:
            logger.debug("No <License> tag found in XML.")
    except Exception as e:
        logger.error(f"Error extracting License with regex: {e}")
        logger.info(f"[DEBUG] Error extracting License. XML preview: {xml_bytes[:200] if isinstance(xml_bytes, bytes) else str(xml_bytes)[:200]}")
    return ''

def parse_permissionset_permissions(xml_bytes):
    perms = []
    try:
        root = ET.fromstring(xml_bytes)
        for perm in root.iter():
            if perm.tag.endswith('userPermissions'):
                name_elem = None
                enabled_elem = None
                for child in perm:
                    if child.tag.endswith('name'):
                        name_elem = child
                    elif child.tag.endswith('enabled'):
                        enabled_elem = child
                if name_elem is not None and enabled_elem is not None:
                    perms.append({
                        'name': name_elem.text.strip() if name_elem.text else '',
                        'enabled': enabled_elem.text.strip() if enabled_elem.text else ''
                    })
    except ET.ParseError as e:
        logger.error(f"[ERROR] XML parsing error in permissionset: {e}")
        logger.info(f"[DEBUG] XML parsing error. XML preview: {xml_bytes[:200] if isinstance(xml_bytes, bytes) else str(xml_bytes)[:200]}")
    except Exception as e:
        logger.error(f"[ERROR] Error parsing userPermissions: {e}")
        logger.info(f"[DEBUG] Error parsing userPermissions. XML preview: {xml_bytes[:200] if isinstance(xml_bytes, bytes) else str(xml_bytes)[:200]}")
    return perms

# Import the handler from the new tasks module
from .tasks.mfa_enforcement import process_mfa_enforcement_task
from .tasks.device_activation import process_device_activation_task
from .tasks.login_ip_ranges import process_login_ip_ranges_task
from .tasks.login_hours import process_login_hours_task
from .tasks.session_timeout import process_session_timeout_task
from .tasks.api_whitelisting import process_api_whitelisting_task
from .tasks.password_policy import process_password_policy_task