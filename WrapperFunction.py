"""
FastAPI Integration for Azure Functions

This module provides a FastAPI application that can be used with Azure Functions
via the ASGI integration. It provides enhanced features like:
- Automatic OpenAPI documentation
- Request validation
- Dependency injection
- Better error handling

The FastAPI app can be used alongside the existing Function App or as a replacement.
"""

import logging
import json
from datetime import datetime
from typing import Dict, Any, List, Optional

import fastapi
from fastapi import Depends, HTTPException, status, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
from pydantic import BaseModel, Field

# Import shared modules
from shared.config import is_local_dev
from shared.utils import create_json_response, handle_exception
from shared.auth_utils import require_auth, get_current_user
from shared.data_access import get_table_storage_repository

# Configure logging
logger = logging.getLogger('fastapi_wrapper')
logger.setLevel(logging.INFO)

# Create FastAPI app
app = fastapi.FastAPI(
    title="AtomSec API",
    description="AtomSec API for Salesforce security analysis",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    openapi_url="/api/openapi.json"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Update with your frontend URL in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Define Pydantic models for request/response validation
class AccountCreate(BaseModel):
    name: str = Field(..., description="Account name")

class AccountResponse(BaseModel):
    ID: str = Field(..., description="Account ID")
    Name: str = Field(..., description="Account name")
    CreatedAt: str = Field(..., description="Creation timestamp")
    IsActive: bool = Field(..., description="Account active status")

class ApiResponse(BaseModel):
    success: bool = Field(..., description="Success status")
    statusCode: int = Field(..., description="HTTP status code")
    timestamp: str = Field(..., description="Response timestamp")
    data: Any = Field(None, description="Response data")
    message: Optional[str] = Field(None, description="Response message")

# Define API routes
@app.get("/api/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat()
    }

@app.get("/api/accounts", response_model=ApiResponse)
async def get_accounts(include_inactive: bool = False):
    """
    Get all accounts endpoint
    
    This endpoint retrieves all accounts from the database.
    """
    logger.info('Processing get accounts request...')

    try:
        # Get accounts
        accounts = []

        if is_local_dev():
            # Use Azure Table Storage for local development
            account_repo = get_table_storage_repository('accounts')
            if not account_repo:
                logger.error("Account table repository not available")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to get accounts"
                )

            # Query accounts
            filter_query = "IsActive eq true" if not include_inactive else None
            entities = account_repo.query_entities(filter_query)

            # Convert entities to accounts
            for entity in entities:
                account = {
                    "ID": entity.get("RowKey"),
                    "Name": entity.get("Name", ""),
                    "CreatedAt": entity.get("CreatedAt", ""),
                    "IsActive": entity.get("IsActive", True)
                }
                accounts.append(account)
        else:
            # Use SQL Database for production
            # This would be implemented based on your SQL repository
            pass

        # Return accounts
        return {
            "success": True,
            "statusCode": 200,
            "timestamp": datetime.utcnow().isoformat(),
            "data": accounts
        }
    except Exception as e:
        logger.error(f"Error getting accounts: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting accounts: {str(e)}"
        )

@app.post("/api/accounts", response_model=ApiResponse, status_code=status.HTTP_201_CREATED)
async def create_account(account: AccountCreate):
    """
    Create account endpoint
    
    This endpoint creates a new account in the database.
    """
    logger.info('Processing create account request...')

    try:
        # Extract account data
        name = account.name

        if not name:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Name is required"
            )

        # Create account
        if is_local_dev():
            # Use Azure Table Storage for local development
            account_repo = get_table_storage_repository('accounts')
            if not account_repo:
                logger.error("Account table repository not available")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to create account"
                )

            # Generate account ID
            import random
            account_id = str(random.randint(1000, 9999))

            # Create entity
            created_at = datetime.utcnow()
            entity = {
                "PartitionKey": "account",
                "RowKey": account_id,
                "Name": name,
                "CreatedAt": created_at.isoformat(),
                "IsActive": True
            }

            # Insert entity
            success = account_repo.insert_entity(entity)
            if not success:
                logger.error("Failed to insert account entity")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to create account"
                )

            # Return created account
            created_account = {
                "ID": account_id,
                "Name": name,
                "CreatedAt": created_at.isoformat(),
                "IsActive": True
            }

            return {
                "success": True,
                "statusCode": 201,
                "timestamp": datetime.utcnow().isoformat(),
                "data": created_account
            }
        else:
            # Use SQL Database for production
            # This would be implemented based on your SQL repository
            raise HTTPException(
                status_code=status.HTTP_501_NOT_IMPLEMENTED,
                detail="Not implemented for production"
            )
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error creating account: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating account: {str(e)}"
        )

@app.get("/api/roles", response_model=ApiResponse)
async def get_roles():
    """
    Get all roles endpoint
    
    This endpoint retrieves all roles from the database.
    """
    logger.info('Processing get roles request...')

    try:
        # Get roles
        roles = []

        if is_local_dev():
            # Use Azure Table Storage for local development
            role_repo = get_table_storage_repository('roles')
            if not role_repo:
                logger.error("Role table repository not available")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to get roles"
                )

            # Query roles
            entities = role_repo.query_entities()

            # Convert entities to roles
            for entity in entities:
                role = {
                    "RoleId": entity.get("RowKey"),
                    "Rolename": entity.get("Rolename", ""),
                    "Description": entity.get("Description", "")
                }
                roles.append(role)
        else:
            # Use SQL Database for production
            # This would be implemented based on your SQL repository
            pass

        # Return roles
        return {
            "success": True,
            "statusCode": 200,
            "timestamp": datetime.utcnow().isoformat(),
            "data": roles
        }
    except Exception as e:
        logger.error(f"Error getting roles: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting roles: {str(e)}"
        )

# Add more routes as needed for your application

# Exception handler for all unhandled exceptions
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"Unhandled exception: {str(exc)}")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "success": False,
            "statusCode": 500,
            "timestamp": datetime.utcnow().isoformat(),
            "message": "An unexpected error occurred"
        }
    )

# Root route that redirects to the docs
@app.get("/")
async def root():
    """Redirect to API documentation"""
    return fastapi.responses.RedirectResponse(url="/api/docs")
