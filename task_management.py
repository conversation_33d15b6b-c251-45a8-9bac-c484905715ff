"""
Task Management Module

This module provides functions for managing background tasks.
"""

import logging
import json
import azure.functions as func
from datetime import datetime, timedelta
from shared.background_processor import BackgroundProcessor
from shared.utils import create_json_response
from shared.cors_middleware import cors_middleware

# Configure logging
logger = logging.getLogger('task_management')

# Create a blueprint for task management functions
bp = func.Blueprint()

# Define consolidated task management function
@bp.route(route="tasks/{action}", methods=["GET", "POST"])
def task_management_api(req: func.HttpRequest) -> func.HttpResponse:
    """
    Consolidated task management API endpoint

    This function handles all task-related operations:
    - list: List tasks for an organization
    - status: Get status of a specific task
    - schedule: Schedule a new task
    - cancel: Cancel a running task

    Args:
        req: HTTP request

    Returns:
        func.HttpResponse: HTTP response
    """
    # Get the action from route parameters
    action = req.route_params.get("action")
    method = req.method

    logger.info(f'Processing task management request: {action}, method: {method}')

    # Get current user from auth utils
    from shared.auth import get_current_user
    current_user = get_current_user(req)

    # Check if user is authorized
    if not current_user:
        return func.HttpResponse(
            json.dumps(create_json_response("Unauthorized", 401)),
            mimetype="application/json",
            status_code=401
        )

    try:
        # Initialize BackgroundProcessor
        processor = BackgroundProcessor()

        # Handle different actions based on the route parameter
        if action == "list" and method == "GET":
            return handle_list_tasks(req, processor, current_user)
        elif action == "status" and method == "GET":
            return handle_get_task_status(req, processor, current_user)
        elif action == "cancel" and method == "POST":
            return handle_cancel_task(req, processor, current_user)
        elif action == "schedule" and method == "POST":
            return handle_schedule_task(req, processor, current_user)
        else:
            # Unsupported action or method
            error_message = f"Unsupported task action or method: {action} {method}"
            logger.warning(error_message)

            response = func.HttpResponse(
                json.dumps(create_json_response(error_message, 404)),
                mimetype="application/json",
                status_code=404
            )

            return cors_middleware(req, response)
    except Exception as e:
        error_message = f"Error in task management API: {str(e)}"
        logger.error(error_message)

        response = func.HttpResponse(
            json.dumps(create_json_response(error_message, 500)),
            mimetype="application/json",
            status_code=500
        )

        return cors_middleware(req, response)

def handle_list_tasks(req: func.HttpRequest, processor: BackgroundProcessor, _current_user: dict) -> func.HttpResponse:
    """
    Handle list tasks action

    Args:
        req: HTTP request
        processor: BackgroundProcessor instance
        _current_user: Current authenticated user (unused)

    Returns:
        func.HttpResponse: HTTP response with tasks
    """
    logger.info('Processing list tasks request...')

    try:
        # Get query parameters
        org_id = req.params.get("org_id")
        include_completed = req.params.get("include_completed", "false").lower() == "true"

        if not org_id:
            return func.HttpResponse(
                json.dumps(create_json_response("Missing required parameter: org_id", 400)),
                mimetype="application/json",
                status_code=400
            )

        # Get tasks
        tasks = processor.get_tasks_by_org(org_id, include_completed)

        # Return tasks
        response_data = {
            "success": True,
            "data": {
                "tasks": tasks,
                "timestamp": datetime.now().isoformat()
            }
        }

        response = func.HttpResponse(
            json.dumps(response_data),
            mimetype="application/json",
            status_code=200
        )

        # Apply CORS middleware
        return cors_middleware(req, response)
    except Exception as e:
        error_message = f"Error getting tasks: {str(e)}"
        logger.error(error_message)

        response = func.HttpResponse(
            json.dumps(create_json_response(error_message, 500)),
            mimetype="application/json",
            status_code=500
        )

        # Apply CORS middleware
        return cors_middleware(req, response)

def handle_get_task_status(req: func.HttpRequest, processor: BackgroundProcessor, _current_user: dict) -> func.HttpResponse:
    """
    Handle get task status action

    Args:
        req: HTTP request
        processor: BackgroundProcessor instance
        _current_user: Current authenticated user (unused)

    Returns:
        func.HttpResponse: HTTP response with task status
    """
    logger.info('Processing get task status request...')

    try:
        # Get query parameters
        task_id = req.params.get("task_id")

        if not task_id:
            return func.HttpResponse(
                json.dumps(create_json_response("Missing required parameter: task_id", 400)),
                mimetype="application/json",
                status_code=400
            )

        # Get task status
        task_status = processor.get_task_status(task_id)

        if not task_status:
            return func.HttpResponse(
                json.dumps(create_json_response(f"Task not found: {task_id}", 404)),
                mimetype="application/json",
                status_code=404
            )

        # Return task status
        response_data = {
            "success": True,
            "data": task_status
        }

        response = func.HttpResponse(
            json.dumps(response_data),
            mimetype="application/json",
            status_code=200
        )

        # Apply CORS middleware
        return cors_middleware(req, response)
    except Exception as e:
        error_message = f"Error getting task status: {str(e)}"
        logger.error(error_message)

        response = func.HttpResponse(
            json.dumps(create_json_response(error_message, 500)),
            mimetype="application/json",
            status_code=500
        )

        # Apply CORS middleware
        return cors_middleware(req, response)

def handle_cancel_task(req: func.HttpRequest, processor: BackgroundProcessor, _current_user: dict) -> func.HttpResponse:
    """
    Handle cancel task action

    Args:
        req: HTTP request
        processor: BackgroundProcessor instance
        _current_user: Current authenticated user (unused)

    Returns:
        func.HttpResponse: HTTP response
    """
    logger.info('Processing cancel task request...')

    try:
        # Get request body
        try:
            req_body = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps(create_json_response("Invalid request body", 400)),
                mimetype="application/json",
                status_code=400
            )

        # Get task ID
        task_id = req_body.get("task_id")
        if not task_id:
            return func.HttpResponse(
                json.dumps(create_json_response("Missing required parameter: task_id", 400)),
                mimetype="application/json",
                status_code=400
            )

        # Cancel the task
        success = processor.cancel_task(task_id)

        if not success:
            return func.HttpResponse(
                json.dumps(create_json_response(f"Failed to cancel task: {task_id}", 500)),
                mimetype="application/json",
                status_code=500
            )

        # Return success response
        response_data = {
            "success": True,
            "message": f"Task {task_id} cancelled successfully"
        }

        response = func.HttpResponse(
            json.dumps(response_data),
            mimetype="application/json",
            status_code=200
        )

        # Apply CORS middleware
        return cors_middleware(req, response)
    except Exception as e:
        error_message = f"Error cancelling task: {str(e)}"
        logger.error(error_message)

        response = func.HttpResponse(
            json.dumps(create_json_response(error_message, 500)),
            mimetype="application/json",
            status_code=500
        )

        # Apply CORS middleware
        return cors_middleware(req, response)

def handle_schedule_task(req: func.HttpRequest, processor: BackgroundProcessor, current_user: dict) -> func.HttpResponse:
    """
    Handle schedule task action

    Args:
        req: HTTP request
        processor: BackgroundProcessor instance
        current_user: Current authenticated user

    Returns:
        func.HttpResponse: HTTP response
    """
    logger.info('Processing schedule task request...')

    try:
        # Get request body
        try:
            req_body = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps(create_json_response("Invalid request body", 400)),
                mimetype="application/json",
                status_code=400
            )

        # Get required parameters
        task_type = req_body.get("taskType")
        org_id = req_body.get("orgId")
        scheduled_time = req_body.get("scheduledTime")
        priority = req_body.get("priority", "medium")
        params = req_body.get("params", {})
        is_recurring = req_body.get("isRecurring", False)
        schedule_type = req_body.get("scheduleType", "daily")

        # Validate required parameters
        if not task_type or not org_id:
            return func.HttpResponse(
                json.dumps(create_json_response("Missing required parameters: taskType, orgId", 400)),
                mimetype="application/json",
                status_code=400
            )

        # Parse scheduled time if provided
        if scheduled_time:
            try:
                scheduled_time_dt = datetime.fromisoformat(scheduled_time)
            except ValueError:
                return func.HttpResponse(
                    json.dumps(create_json_response("Invalid scheduledTime format. Use ISO format (YYYY-MM-DDTHH:MM:SS)", 400)),
                    mimetype="application/json",
                    status_code=400
                )
        else:
            # Default to 1 hour from now
            scheduled_time_dt = datetime.now() + timedelta(hours=1)

        # Get user ID from current user
        user_id = current_user.get("id", "unknown")

        # Schedule the task
        if is_recurring:
            task_id = processor.schedule_recurring_task(
                task_type=task_type,
                org_id=org_id,
                user_id=user_id,
                schedule_type=schedule_type,
                params=params,
                priority=priority
            )
        else:
            task_id = processor.schedule_task(
                task_type=task_type,
                org_id=org_id,
                user_id=user_id,
                scheduled_time=scheduled_time_dt,
                params=params,
                priority=priority
            )

        if not task_id:
            return func.HttpResponse(
                json.dumps(create_json_response("Failed to schedule task", 500)),
                mimetype="application/json",
                status_code=500
            )

        # Return success response
        response_data = {
            "success": True,
            "message": f"Task scheduled successfully",
            "data": {
                "taskId": task_id,
                "scheduledTime": scheduled_time_dt.isoformat() if scheduled_time_dt else None,
                "isRecurring": is_recurring,
                "scheduleType": schedule_type if is_recurring else None
            }
        }

        response = func.HttpResponse(
            json.dumps(response_data),
            mimetype="application/json",
            status_code=200
        )

        # Apply CORS middleware
        return cors_middleware(req, response)
    except Exception as e:
        error_message = f"Error scheduling task: {str(e)}"
        logger.error(error_message)

        response = func.HttpResponse(
            json.dumps(create_json_response(error_message, 500)),
            mimetype="application/json",
            status_code=500
        )

        # Apply CORS middleware
        return cors_middleware(req, response)

# Define a new endpoint for task-status
@bp.route(route="task-status", methods=["GET"])
def task_status_api(req: func.HttpRequest) -> func.HttpResponse:
    """
    Task status API endpoint
    
    This function handles getting task status for an integration:
    - Retrieves tasks for a specific integration
    - Filters by status
    - Limits the number of results
    
    Args:
        req: HTTP request
        
    Returns:
        func.HttpResponse: HTTP response with task status
    """
    logger.info('Processing task status request...')
    
    # Get query parameters
    # Get all query parameters and log them
    all_params = dict(req.params)
    logger.info(f"All request parameters: {all_params}")
    
    integration_id = req.params.get("integration_id")
    
    # Handle case sensitivity for status parameter
    status = req.params.get("Status")  # Capitalized
    if status is None:
        status = req.params.get("status")  # Lowercase
    
    limit = req.params.get("limit", "10")
    
    # Check for all possible task type parameter naming conventions
    task_type = req.params.get("taskType")  # camelCase
    if task_type is None:
        task_type = req.params.get("task_type")  # snake_case
    if task_type is None:
        task_type = req.params.get("TaskType")  # PascalCase
    
    logger.info(f"Parsed parameters - integration_id: {integration_id}, status: {status}, limit: {limit}, task_type: {task_type}")
    
    try:
        # Convert limit to integer
        limit = int(limit)
    except ValueError:
        limit = 10
    
    # Validate required parameters
    if not integration_id:
        return func.HttpResponse(
            json.dumps(create_json_response("Missing required parameter: integration_id", 400)),
            mimetype="application/json",
            status_code=400
        )
    
    try:
        # Initialize BackgroundProcessor
        processor = BackgroundProcessor()
        
        # Try different formats of the integration ID
        logger.info(f"Retrieving tasks for integration_id: {integration_id}")
        
        # First try with the original integration_id
        tasks = processor.get_tasks_by_org(integration_id, True)  # Include completed tasks
        
        # If no tasks found, try with different formats of the integration ID
        if not tasks:
            logger.warning(f"No tasks found with original integration_id: {integration_id}")
            
            # Try without dashes
            no_dashes_id = integration_id.replace('-', '')
            logger.info(f"Trying without dashes: {no_dashes_id}")
            tasks = processor.get_tasks_by_org(no_dashes_id, True)
            
            # If still no tasks, try uppercase
            if not tasks:
                uppercase_id = integration_id.upper()
                logger.info(f"Trying uppercase: {uppercase_id}")
                tasks = processor.get_tasks_by_org(uppercase_id, True)
                
                # If still no tasks, try lowercase
                if not tasks:
                    lowercase_id = integration_id.lower()
                    logger.info(f"Trying lowercase: {lowercase_id}")
                    tasks = processor.get_tasks_by_org(lowercase_id, True)
        
        # Debug: Check if we're getting any tasks at all
        if not tasks:
            logger.warning(f"No tasks found for integration_id after trying all variations")
            # Return empty result early
            response_data = {
                "success": True,
                "data": [],
                "message": f"No tasks found for integration_id: {integration_id} (tried various formats)",
                "timestamp": datetime.now().isoformat()
            }
            response = func.HttpResponse(
                json.dumps(response_data),
                mimetype="application/json",
                status_code=200
            )
            return cors_middleware(req, response)
        else:
            logger.info(f"Found {len(tasks)} tasks for integration_id")
        
        # Apply all filters before limiting results
        original_count = len(tasks)
        logger.info(f"Original task count: {original_count}")
        
        # Log the first few tasks to understand their structure
        if tasks:
            logger.info(f"First task: {json.dumps(tasks[0])}")
            logger.info(f"First task keys: {list(tasks[0].keys())}")
            
            # Log task_type and status fields specifically
            for i, task in enumerate(tasks[:3]):  # Log first 3 tasks
                logger.info(f"Task {i} - task_type: {task.get('task_type', 'NOT_FOUND')}, "
                           f"TaskType: {task.get('TaskType', 'NOT_FOUND')}, "
                           f"taskType: {task.get('taskType', 'NOT_FOUND')}, "
                           f"status: {task.get('status', 'NOT_FOUND')}, "
                           f"Status: {task.get('Status', 'NOT_FOUND')}")
        
        # Filter by status if provided
        if status:
            logger.info(f"Filtering tasks by status: {status}")
            
            # Make status filtering more robust
            logger.info(f"Filtering tasks by status: {status}")
            
            # Handle various status variations
            status_lower = status.lower()
            
            # Define common status variations
            completed_variations = ["complete", "completed", "done", "finished"]
            pending_variations = ["pending", "in progress", "running", "in-progress"]
            failed_variations = ["failed", "error", "failure", "failed"]
            
            # Determine target status variations to check
            target_status_variations = []
            if status_lower in completed_variations:
                target_status_variations = completed_variations
                logger.info(f"Detected 'completed' status, will check for variations: {target_status_variations}")
            elif status_lower in pending_variations:
                target_status_variations = pending_variations
                logger.info(f"Detected 'pending' status, will check for variations: {target_status_variations}")
            elif status_lower in failed_variations:
                target_status_variations = failed_variations
                logger.info(f"Detected 'failed' status, will check for variations: {target_status_variations}")
            else:
                # For any other status, just use the provided value
                target_status_variations = [status_lower]
                logger.info(f"Using exact status match: {target_status_variations}")
            
            # Check all possible field names for status
            filtered_tasks = []
            for task in tasks:
                # Get status using all possible field names
                task_status = task.get("status", "")
                if not task_status:
                    task_status = task.get("Status", "")
                
                # Compare case-insensitively against all variations
                task_status_lower = task_status.lower()
                logger.info(f"Checking task status: '{task_status_lower}' against variations: {target_status_variations}")
                
                if any(variation in task_status_lower for variation in target_status_variations):
                    logger.info(f"Task status matched! Adding to filtered tasks.")
                    filtered_tasks.append(task)
                else:
                    logger.info(f"Task status did not match any variation.")
            
            tasks = filtered_tasks
                
            logger.info(f"After status filtering: {len(tasks)} tasks")

        # Filter by task_type if provided (case-insensitive)
        if task_type:
            logger.info(f"Filtering tasks by task_type: {task_type}")
            task_type_lower = task_type.lower()
            
            # Check for common variations of health_check
            health_check_variations = ["health_check", "healthcheck", "health-check", "healthCheck", "HealthCheck"]
            is_health_check = task_type_lower in ["health_check", "healthcheck", "health-check"]
            
            if is_health_check:
                logger.info(f"Detected health_check task type, will check for variations: {health_check_variations}")
            
            # Log the first few tasks to see their structure
            if tasks:
                logger.info(f"Sample task structure after status filtering: {json.dumps(tasks[0]) if tasks else 'No tasks'}")
                logger.info(f"Sample task keys after status filtering: {list(tasks[0].keys()) if tasks else 'No tasks'}")
            
            # Check all possible field names for task type
            filtered_tasks = []
            for task in tasks:
                # Get task type using all possible field names
                task_task_type = task.get("task_type", "")
                if not task_task_type:
                    task_task_type = task.get("TaskType", "")
                if not task_task_type:
                    task_task_type = task.get("taskType", "")
                
                # Compare case-insensitively
                task_type_lower_value = task_task_type.lower()
                logger.info(f"Comparing task_type: '{task_type_lower_value}' with target: '{task_type_lower}'")
                
                # If we're looking for health_check, check for variations
                if is_health_check:
                    if any(variation in task_type_lower_value for variation in health_check_variations):
                        logger.info(f"Task matched a health_check variation! Adding to filtered tasks.")
                        filtered_tasks.append(task)
                    else:
                        logger.info(f"Task did not match any health_check variation.")
                else:
                    # Normal comparison for other task types
                    if task_type_lower_value == task_type_lower:
                        logger.info(f"Task matched! Adding to filtered tasks.")
                        filtered_tasks.append(task)
                    else:
                        logger.info(f"Task did not match task_type filter.")
            
            tasks = filtered_tasks
            logger.info(f"After task_type filtering: {len(tasks)} tasks")

        # Sort by timestamp (newest first)
        tasks = sorted(tasks, key=lambda x: x.get("Timestamp", ""), reverse=True)
        
        # Log before limiting
        logger.info(f"Found {len(tasks)} matching tasks before applying limit of {limit}")
        
        # Ensure limit is an integer
        try:
            limit_int = int(limit)
        except (ValueError, TypeError):
            logger.warning(f"Invalid limit value: {limit}, using default of 10")
            limit_int = 10
        
        # Limit the number of results AFTER all filtering is done
        tasks = tasks[:limit_int]
        
        logger.info(f"Returning {len(tasks)} tasks after applying limit of {limit_int}")
        
        # Return tasks
        response_data = {
            "success": True,
            "data": tasks,
            "timestamp": datetime.now().isoformat()
        }
        
        response = func.HttpResponse(
            json.dumps(response_data),
            mimetype="application/json",
            status_code=200
        )
        
        # Apply CORS middleware
        return cors_middleware(req, response)
    except Exception as e:
        error_message = f"Error getting task status: {str(e)}"
        logger.error(error_message)
        
        response = func.HttpResponse(
            json.dumps(create_json_response(error_message, 500)),
            mimetype="application/json",
            status_code=500
        )
        
        # Apply CORS middleware
        return cors_middleware(req, response)
