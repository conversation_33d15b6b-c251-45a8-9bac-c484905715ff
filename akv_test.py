# Pre-req

## Install the requirements  
## - azure-keyvault-secrets
## - azure-identity
## - azure-cli

## The login to azure via command line 
## - az login

## Now run the code 
## - python akv_test.py

from azure.identity import DefaultAzureCredential
from azure.keyvault.secrets import SecretClient

credential = DefaultAzureCredential()

secret_client = SecretClient(vault_url="https://akv-atomsec-dev.vault.azure.net/", credential=credential)
secret = secret_client.get_secret("test")

print(secret.name)
print(secret.value)