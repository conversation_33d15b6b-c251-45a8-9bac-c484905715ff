"""
Main Function App Entry Point

This is the main entry point for the Azure Functions application.
It implements a hybrid approach that combines:
1. Standard Azure Functions with blueprints for existing functionality
2. FastAPI through ASGI integration for enhanced API features

Best practices implemented:
- Blueprint-based organization for existing functionality
- FastAPI for enhanced API features
- Automatic OpenAPI documentation
- Request validation with Pydantic models
- Better error handling
- Consolidated proxy functions by functional group
"""

import logging
import json
import azure.functions as func
from shared.cors_middleware import cors_middleware
from shared.utils import create_json_response

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# ===== 1. Authentication and Authorization =====
from blueprints.auth import bp as auth_bp
from blueprints.azure_ad_auth import bp as azure_ad_auth_bp
from blueprints.user_profile import bp as user_profile_bp

# ===== 2. Integration Management =====
from blueprints.integration import bp as integration_bp
from blueprints.integration_tabs import bp as integration_tabs_bp
from blueprints.scan import bp as scan_bp

# ===== 3. Security Analysis =====
from blueprints.security_health_check_simplified import bp as security_health_check_bp
from blueprints.security_data import bp as security_data_bp
from blueprints.security_analysis import bp as security_analysis_bp
from blueprints.profile_metadata_fixed import bp as profile_metadata_bp

# ===== 4. Account Management =====
# Account management endpoints are now handled by the API router
from blueprints.organization import bp as organization_bp

# ===== 5. Task Management =====
from task_management import bp as task_management_bp

# Import task processor functions
import logging
from shared.background_processor import (
    BackgroundProcessor,
    TASK_STATUS_RUNNING,
    TASK_STATUS_COMPLETED,
    TASK_STATUS_FAILED,
    TASK_TYPE_HEALTH_CHECK,
    TASK_TYPE_METADATA_EXTRACTION,
    TASK_TYPE_NOTIFICATION,
    TASK_TYPE_SFDC_AUTHENTICATE,
    TASK_TYPE_PROFILES_PERMISSION_SETS,
    TASK_TYPE_PERMISSION_SETS
)
from task_processor import (
    process_overview_task,
    process_health_check_task,
    process_profiles_task,
    process_metadata_extraction_task,
    process_notification_task,
    process_sfdc_authenticate_task,
    process_profiles_permission_sets_task,
    process_mfa_enforcement_task,
    TASK_TYPE_MFA_ENFORCEMENT,
    process_device_activation_task,
    process_login_ip_ranges_task,
    process_login_hours_task,
    process_session_timeout_task,
    process_api_whitelisting_task,
    TASK_TYPE_DEVICE_ACTIVATION,
    TASK_TYPE_LOGIN_IP_RANGES,
    TASK_TYPE_LOGIN_HOURS,
    TASK_TYPE_SESSION_TIMEOUT,
    TASK_TYPE_API_WHITELISTING,
    process_password_policy_task,
    TASK_TYPE_PASSWORD_POLICY,
    process_pmd_task,
    TASK_TYPE_PMD_APEX_SECURITY
)

# ===== 6. Key Vault Management =====
# Key vault endpoints are now handled by the API router

# ===== 7. System and Utilities =====
from blueprints.general import bp as general_bp

# Create the standard Function App for existing blueprints
app = func.FunctionApp(http_auth_level=func.AuthLevel.ANONYMOUS)

# Register all blueprints with error handling
try:
    # ===== 1. Authentication and Authorization =====
    app.register_functions(auth_bp)
    app.register_functions(azure_ad_auth_bp)
    app.register_functions(user_profile_bp)

    # ===== 2. Integration Management =====
    app.register_functions(integration_bp)
    app.register_functions(integration_tabs_bp)
    app.register_functions(scan_bp)

    # ===== 3. Security Analysis =====
    app.register_functions(security_health_check_bp)
    app.register_functions(security_data_bp)
    app.register_functions(security_analysis_bp)
    app.register_functions(profile_metadata_bp)

    # ===== 4. Account Management =====
    # Account management endpoints are now handled by the API router
    app.register_functions(organization_bp)

    # ===== 5. Task Management =====
    app.register_functions(task_management_bp)

    # ===== 6. Key Vault Management =====
    # Key vault endpoints are now handled by the API router

    # ===== 7. System and Utilities =====
    app.register_functions(general_bp)

    logging.info("Successfully registered all blueprints")
except Exception as e:
    logging.error(f"Error registering blueprints: {str(e)}")
    # Continue with partial functionality rather than failing completely
    pass

# Note: The FastAPI integration is handled in WrapperFunction/__init__.py
# We don't need to create an ASGI Function App here

# Define separate queue trigger functions for each priority level but with shared processing logic
@app.queue_trigger(arg_name="msg", queue_name="task-queue-high", connection="AzureWebJobsStorage")
def task_processor_high(msg: func.QueueMessage) -> None:
    """
    Process tasks from high priority queue

    Args:
        msg: Queue message
    """
    process_task_message(msg, "high", "task-queue-high")

@app.queue_trigger(arg_name="msg", queue_name="task-queue-medium", connection="AzureWebJobsStorage")
def task_processor_medium(msg: func.QueueMessage) -> None:
    """
    Process tasks from medium priority queue

    Args:
        msg: Queue message
    """
    process_task_message(msg, "medium", "task-queue-medium")

@app.queue_trigger(arg_name="msg", queue_name="task-queue-low", connection="AzureWebJobsStorage")
def task_processor_low(msg: func.QueueMessage) -> None:
    """
    Process tasks from low priority queue

    Args:
        msg: Queue message
    """
    process_task_message(msg, "low", "task-queue-low")

def process_task_message(msg: func.QueueMessage, priority: str, queue_name: str) -> None:
    """
    Common task processing logic for all priority levels

    Args:
        msg: Queue message
        priority: Priority level (high, medium, low)
        queue_name: Name of the queue that triggered the function
    """
    logging.info(f"Task processor triggered for {priority} priority task from queue: {queue_name}")

    try:
        # Get the message body
        # With base64 encoding, Azure Functions automatically decodes the message
        message_body = msg.get_body().decode('utf-8')
        logging.info(f"Processing {priority}-priority task: {message_body}")

        # Parse message
        import json
        task_data = json.loads(message_body)

        # Extract task information
        task_id = task_data.get("task_id")
        task_type = task_data.get("task_type")
        org_id = task_data.get("org_id")
        user_id = task_data.get("user_id")
        params = task_data.get("params", {})
        execution_log_id = task_data.get("execution_log_id")

        logging.info(f"Processing {priority}-priority task {task_id} of type {task_type} for organization {org_id}")

        # Initialize background processor
        processor = BackgroundProcessor()

        # Update task status to running
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=10,
            message="Task started"
        )

        # Process task based on type
        if task_type == TASK_TYPE_HEALTH_CHECK:
            process_health_check_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_METADATA_EXTRACTION:
            process_metadata_extraction_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_NOTIFICATION:
            process_notification_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_SFDC_AUTHENTICATE:
            process_sfdc_authenticate_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_PROFILES_PERMISSION_SETS:
            process_profiles_permission_sets_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_MFA_ENFORCEMENT:
            process_mfa_enforcement_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_DEVICE_ACTIVATION:
            process_device_activation_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_LOGIN_IP_RANGES:
            process_login_ip_ranges_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_LOGIN_HOURS:
            process_login_hours_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_SESSION_TIMEOUT:
            process_session_timeout_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_API_WHITELISTING:
            process_api_whitelisting_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_PASSWORD_POLICY:
            process_password_policy_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif task_type == TASK_TYPE_PMD_APEX_SECURITY:
            logging.info(f"Processing PMD Apex Security task: {task_id}")
            process_pmd_task(processor, task_id, org_id, user_id, params, execution_log_id)
        else:
            logging.warning(f"Unknown task type: {task_type}")
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=100,
                message=f"Unknown task type: {task_type}"
            )
            return

        # Update task status to completed
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_COMPLETED,
            progress=100,
            message="Task completed successfully"
        )

        logging.info(f"Task {task_id} processed successfully")
    except Exception as e:
        logging.error(f"Error processing task: {str(e)}")

        # Update task status to failed
        try:
            processor = BackgroundProcessor()
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=100,
                message=f"Error processing task: {str(e)}"
            )
        except Exception as inner_e:
            logging.error(f"Error updating task status: {str(inner_e)}")

# Log successful initialization
logging.info("Successfully initialized hybrid Function App with FastAPI ASGI integration")

# Move the catch-all route to the very end
@app.route(route="{*path}", methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"])
async def api_router(req: func.HttpRequest, context: func.Context) -> func.HttpResponse:
    """
    Main API router function that handles all API requests

    This consolidated router function replaces multiple individual proxy functions.
    It routes requests to the appropriate handler based on the path and method.

    Args:
        req: HTTP request
        context: Function context

    Returns:
        func.HttpResponse: HTTP response from the appropriate handler
    """
    # Get the full path from route parameters
    path = req.route_params.get("path", "")
    method = req.method

    logging.info(f'API Router processing request: {method} /{path}')
    logging.info(f'Request URL: {req.url}')
    logging.info(f'Request route params: {req.route_params}')

    # Log request headers for debugging (excluding sensitive info)
    headers_dict = dict(req.headers)
    if 'Authorization' in headers_dict:
        headers_dict['Authorization'] = 'REDACTED'
    logging.info(f"Request headers: {headers_dict}")

    # Explicitly handle /home and /info to ensure they are not caught by the catch-all 404
    if path == "home" and method == "GET":
        # Assuming the 'home' function is defined in this file or correctly imported
        # If 'home' is defined in the same file and not part of a class:
        return home(req) # Directly call the 'home' function
    elif path == "info" and method == "GET":
        from blueprints.general import system_info # Ensure this import is correct
        return system_info(req)

    try:
        # ===== 1. Authentication Routes =====
        if path.startswith("auth/"):
            endpoint = path[5:]  # Remove "auth/" prefix
            logging.info(f'Routing to auth endpoint: {endpoint}, method: {method}')

            # Handle Azure AD auth endpoints
            if endpoint.startswith("azure/"):
                # Check if we're in production - disable Azure auth endpoints in production
                # In production, frontend uses platform authentication instead
                from shared.config import is_local_dev

                if not is_local_dev():
                    logging.info(f'Azure auth endpoint {endpoint} disabled in production - frontend uses platform auth')
                    return func.HttpResponse(
                        json.dumps(create_json_response(
                            "Azure authentication endpoints are disabled in production. Frontend uses platform authentication.",
                            503
                        )),
                        mimetype="application/json",
                        status_code=503
                    )

                # Development only: Consolidated Azure AD auth endpoints
                logging.info(f'Development environment: Processing Azure auth endpoint {endpoint}')
                from blueprints.azure_ad_auth import azure_login, azure_callback, azure_me

                if endpoint == "azure/login":
                    return azure_login(req)
                elif endpoint == "azure/callback":
                    return azure_callback(req)
                elif endpoint == "azure/me":
                    return azure_me(req)
                else:
                    return func.HttpResponse(
                        json.dumps(create_json_response(f"Unsupported Azure AD auth endpoint: {endpoint}", 404)),
                        mimetype="application/json",
                        status_code=404
                    )
            # Handle standard auth endpoints
            elif endpoint == "signup" and method == "POST":
                from blueprints.auth import signup
                return signup(req)
            elif endpoint == "login" and method == "POST":
                from blueprints.auth import login
                return login(req)
            elif endpoint == "token/refresh" and method == "POST":
                from blueprints.auth import refresh_token
                return refresh_token(req)
            else:
                return func.HttpResponse(
                    json.dumps(create_json_response(f"Unsupported auth endpoint or method: {endpoint} {method}", 404)),
                    mimetype="application/json",
                    status_code=404
                )

        # ===== 2. Integration Management Routes =====
        # Integration endpoints are now handled by the FastAPI ASGI app
        # These routes are defined in routers/integration_router.py and processed by the WrapperFunction/__init__.py
        elif path.startswith("integration/") or path == "integrations":
            logging.info(f'Routing to FastAPI integration endpoint: {path}, method: {method}')

            # Special case for test-connection and connect which are still handled by the original implementation
            if path == "integration/test-connection" and method == "POST":
                logging.info(f'Handling test-connection endpoint directly')
                from blueprints.integration import test_connection
                return test_connection(req)
            elif path == "integration/connect" and method == "POST":
                logging.info(f'Handling connect endpoint directly')
                from blueprints.integration import connect_integration
                return connect_integration(req)
            # Special case for the integrations endpoint
            elif path == "integrations" and method == "GET":
                logging.info(f'Special handling for integrations endpoint')
                # Use the blueprint implementation for backward compatibility
                from blueprints.integration import get_integrations
                return get_integrations(req)
            # Handle the new scan endpoint format
            elif path.startswith("integration/scan/") and method == "POST":
                logging.info(f'Handling scan endpoint with new format: {path}')
                # Extract integration ID from the path - ensure we get the exact ID without any slashes
                integration_id = path[16:].strip('/')  # Remove "integration/scan/" prefix and any slashes
                logging.info(f'Extracted integration ID: {integration_id}')

                # Log the available integrations in Azurite for debugging
                try:
                    from shared.data_access import TableStorageRepository
                    integration_repo = TableStorageRepository(table_name="Integrations")
                    filter_query = "PartitionKey eq 'integration'"
                    entities = integration_repo.query_entities(filter_query)
                    logging.info(f"Available integrations in Azurite: {len(entities) if entities else 0}")
                    if entities:
                        for entity in entities:
                            logging.info(f"Integration: PartitionKey={entity.get('PartitionKey')}, RowKey={entity.get('RowKey')}, TenantUrl={entity.get('TenantUrl')}")
                except Exception as e:
                    logging.error(f"Error querying integrations: {str(e)}")

                try:
                    # Create a new HttpRequest with the ID in the route_params
                    # We can't modify req.route_params directly as it's immutable
                    from azure.functions import HttpRequest
                    new_headers = dict(req.headers)
                    new_params = dict(req.params)
                    new_route_params = {"id": integration_id}

                    # Create a new request with the same body but updated route params
                    body = req.get_body() if req.get_body() else None
                    new_req = HttpRequest(
                        method=req.method,
                        url=req.url,
                        headers=new_headers,
                        params=new_params,
                        route_params=new_route_params,
                        body=body
                    )

                    # Call the scan_integration function with the new request
                    from blueprints.integration import scan_integration
                    response = scan_integration(new_req)
                    return cors_middleware(req, response)
                except Exception as e:
                    # If there's an error in the scan_integration function, log it and return a success response
                    # This ensures the frontend can continue even if there's an issue with the scan
                    logging.error(f"Error in scan_integration: {str(e)}")
                    import traceback
                    logging.error(f"Traceback: {traceback.format_exc()}")

                    # Return a success response to allow the frontend to continue
                    response = func.HttpResponse(
                        json.dumps(create_json_response({
                            "success": True,
                            "message": "Scan initiated with fallback response. Check logs for details.",
                            "integrationId": integration_id,
                            "healthScore": 85  # Placeholder value
                        })),
                        mimetype="application/json",
                        status_code=200
                    )
                    return cors_middleware(req, response)

            # Handle the new scan-metadata endpoint format
            elif path.startswith("integration/scan-metadata/") and method == "POST":
                logging.info(f'Handling scan-metadata endpoint with new format: {path}')
                # Extract integration ID from the path - ensure we get the exact ID without any slashes
                integration_id = path[24:].strip('/')  # Remove "integration/scan-metadata/" prefix and any slashes
                logging.info(f'Extracted integration ID: {integration_id}')

                # Create a new HttpRequest with the ID in the route_params
                # We can't modify req.route_params directly as it's immutable
                from azure.functions import HttpRequest
                new_headers = dict(req.headers)
                new_params = dict(req.params)
                new_route_params = {"id": integration_id}

                # Create a new request with the same body but updated route params
                body = req.get_body() if req.get_body() else None
                new_req = HttpRequest(
                    method=req.method,
                    url=req.url,
                    headers=new_headers,
                    params=new_params,
                    route_params=new_route_params,
                    body=body
                )

                try:
                    from blueprints.scan_metadata_fixed import scan_metadata_fixed
                    logging.info("Using fixed scan_metadata function")
                    response = scan_metadata_fixed(new_req)
                except ImportError:
                    logging.warning("Fixed scan_metadata function not found, using original implementation")
                    from blueprints.integration import scan_metadata
                    response = scan_metadata(new_req)
                return cors_middleware(req, response)
            # Handle the delete integration endpoint
            elif method == "DELETE" and path.startswith("integration/") and len(path.split('/')) == 2: # e.g. integration/some-id
                integration_id_from_path = path.split('/')[1]
                logging.info(f'Handling delete integration endpoint directly for ID: {integration_id_from_path}')

                from azure.functions import HttpRequest
                new_headers = dict(req.headers)
                new_params = dict(req.params)
                # Important: The blueprint expects the ID in route_params['id']
                new_route_params = {"id": integration_id_from_path}

                body = req.get_body() if req.get_body() else None
                new_req = HttpRequest(
                    method=req.method,
                    url=req.url,
                    headers=new_headers,
                    params=new_params,
                    route_params=new_route_params,
                    body=body
                )
                from blueprints.integration import delete_integration
                response = delete_integration(new_req)
                return cors_middleware(req, response)
            else:
                # Log the path for debugging
                logging.info(f'Forwarding to ASGI app: {path}, method: {method}')
                # Forward to the ASGI app via the WrapperFunction
                from WrapperFunction import main as wrapper_main
                return await wrapper_main(req, context)

        elif path.startswith("integrations/"):
            # Return a 410 Gone status for the old format to indicate it's no longer supported
            logging.info(f'Received request using deprecated format: {path}')
            response = func.HttpResponse(
                json.dumps(create_json_response({
                    "success": False,
                    "message": "This endpoint format is deprecated. Please use the new format: integration/scan/{id} or integration/scan-metadata/{id}"
                }, 410)),
                mimetype="application/json",
                status_code=410
            )
            return cors_middleware(req, response)
            
        # ===== 3. Policies Result Route =====
        elif path.startswith("policies-result/"):
            logging.info(f'Routing to policies-result endpoint: {path}, method: {method}')
            integration_id = path[16:].strip('/')  # Remove "policies-result/" prefix and any slashes
            logging.info(f'Extracted integration ID for policies-result: {integration_id}')
            from azure.functions import HttpRequest
            new_headers = dict(req.headers)
            new_params = dict(req.params)
            new_route_params = {"integration_id": integration_id}
            body = req.get_body() if req.get_body() else None
            new_req = HttpRequest(
                method=req.method,
                url=req.url,
                headers=new_headers,
                params=new_params,
                route_params=new_route_params,
                body=body
            )
            from blueprints.security_analysis import get_policies_result
            response = get_policies_result(new_req)
            return cors_middleware(req, response)
        # ===== 3a. Profiles Permissions Route =====
        elif path.startswith("profiles-permissions/"):
            logging.info(f'Routing to profiles-permissions endpoint: {path}, method: {method}')
            org_id = path[20:].strip('/')  # Remove "profiles-permissions/" prefix and any slashes
            logging.info(f'Extracted org_id for profiles-permissions: {org_id}')
            from azure.functions import HttpRequest
            new_headers = dict(req.headers)
            new_params = dict(req.params)
            new_route_params = {"org_id": org_id}
            body = req.get_body() if req.get_body() else None
            new_req = HttpRequest(
                method=req.method,
                url=req.url,
                headers=new_headers,
                params=new_params,
                route_params=new_route_params,
                body=body
            )
            from blueprints.profiles_permissions import get_profiles_permissions
            response = get_profiles_permissions(new_req)
            return cors_middleware(req, response)

        # ===== 4. User Profile Routes =====
        elif path.startswith("user/"):
            action = path[5:]  # Remove "user/" prefix
            logging.info(f'Routing to user profile endpoint: {action}, method: {method}')

            if action == "profile":
                from blueprints.user_profile import get_user_profile, update_user_profile
                if method == "GET":
                    return get_user_profile(req)
                elif method == "PUT":
                    return update_user_profile(req)
                else:
                    return func.HttpResponse(
                        json.dumps(create_json_response("Method not allowed", 405)),
                        mimetype="application/json",
                        status_code=405
                    )
            elif action == "password" and method == "POST":
                from blueprints.user_profile import change_password
                return change_password(req)
            else:
                return func.HttpResponse(
                    json.dumps(create_json_response(f"Unsupported user profile action: {action}", 404)),
                    mimetype="application/json",
                    status_code=404
                )

        # ===== 4. Task Management Routes =====
        elif path.startswith("tasks/"):
            logging.info(f'Routing to consolidated task management endpoint: {path}, method: {method}')

            # Use the consolidated task management function from task_management.py
            from task_management import task_management_api

            # Update route parameters to include the action
            action = path[6:]  # Remove "tasks/" prefix
            req.route_params["action"] = action

            # Call the consolidated function
            return task_management_api(req)
        
        # Handle the task-status endpoint separately
        elif path == "task-status" and method == "GET":
            logging.info(f'Routing to task-status endpoint: {path}, method: {method}')
            
            # Import the task_status_api function from task_management.py
            from task_management import task_status_api
            
            # Call the function directly
            return task_status_api(req)

        # ===== 5. Account Management Routes =====
        # Account management endpoints (accounts, users, roles) are now handled by the FastAPI ASGI app
        # These routes are defined in account_management_asgi.py and processed by the WrapperFunction/__init__.py

        # ===== 8. Organization Management Routes =====
        elif path == "orgs" and method == "GET":
            logging.info(f'Routing to organizations endpoint, method: {method}')

            try:
                # List organizations
                from blueprints.organization import get_orgs
                return get_orgs(req)
            except Exception as e:
                error_message = f"Error in get organizations: {str(e)}"
                logging.error(error_message)
                return _create_error_response(error_message, 500)

        # ===== 9. Key Vault Routes =====
        elif path.startswith("key-vault/"):
            action = path[10:]  # Remove "key-vault/" prefix
            logging.info(f'Routing to key vault endpoint: {action}, method: {method}')

            if action == "secrets" and method == "POST":
                from blueprints.key_vault_endpoints import store_secret_endpoint
                return store_secret_endpoint(req)
            elif action == "create" and method == "POST":
                from blueprints.key_vault_endpoints import create_key_vault_endpoint
                return create_key_vault_endpoint(req)
            elif action == "access-policy" and method == "POST":
                from blueprints.key_vault_endpoints import add_access_policy_endpoint
                return add_access_policy_endpoint(req)
            elif action == "client-credentials" and method == "POST":
                from blueprints.key_vault_endpoints import store_client_credentials_endpoint
                return store_client_credentials_endpoint(req)
            else:
                return func.HttpResponse(
                    json.dumps(create_json_response(f"Unsupported key vault action: {action}", 404)),
                    mimetype="application/json",
                    status_code=404
                )

        # ===== 10. Organization Management Routes =====
        elif method == "POST":
            org_action = path

            logging.info(f'Routing to organization action: {org_action}, method: {method}')

            if org_action == "connect-org":
                # Connect to organization
                from blueprints.organization import connect_org
                return connect_org(req)
            elif org_action == "disconnect-org":
                # Disconnect from organization
                from blueprints.organization import disconnect_org
                return disconnect_org(req)
            elif org_action == "rescan-org":
                # Rescan organization
                from blueprints.organization import rescan_org
                return rescan_org(req)

        # If no route matched, return 404
        return func.HttpResponse(
            json.dumps(create_json_response(f"Route not found: {path}", 404)),
            mimetype="application/json",
            status_code=404
        )

    except Exception as e:
        error_message = f"Error in API router: {str(e)}"
        logging.error(error_message)
        import traceback
        logging.error(f"Traceback: {traceback.format_exc()}")

        return func.HttpResponse(
            json.dumps(create_json_response(error_message, 500)),
            mimetype="application/json",
            status_code=500
        )

# Helper functions for error responses
def _create_error_response(message, status_code=400):
    """Create a standardized error response"""
    from datetime import datetime

    error_response = {
        "success": False,
        "statusCode": status_code,
        "timestamp": datetime.now().isoformat(),
        "message": message
    }

    return func.HttpResponse(
        body=json.dumps(error_response),
        mimetype="application/json",
        status_code=status_code
    )

# ===== 5. Debug and System Proxy Functions =====

@app.route(route="home")
def home(req: func.HttpRequest) -> func.HttpResponse:
    """
    Home page for the function app with Swagger-like API documentation

    Args:
        req: HTTP request

    Returns:
        func.HttpResponse: HTTP response with HTML content
    """
    logging.info('Processing home page request...')

    # Import API documentation helpers
    from shared.api_utils import ApiDocumentation, ApiEndpoint

    # Create API documentation
    api_docs = ApiDocumentation(
        title="AtomSec API",
        description="AtomSec API for Salesforce security analysis",
        version="1.0.0"
    )

    # Add authentication endpoints
    api_docs.add_endpoint(ApiEndpoint(
        path="/api/auth/login",
        method="POST",
        summary="User login",
        description="Authenticate a user with email and password",
        tags=["Authentication"],
        request_body={
            "type": "object",
            "properties": {
                "email": {"type": "string", "format": "email"},
                "password": {"type": "string", "format": "password"}
            },
            "required": ["email", "password"]
        }
    ))

    api_docs.add_endpoint(ApiEndpoint(
        path="/api/auth/signup",
        method="POST",
        summary="User signup",
        description="Register a new user",
        tags=["Authentication"],
        request_body={
            "type": "object",
            "properties": {
                "email": {"type": "string", "format": "email"},
                "password": {"type": "string", "format": "password"},
                "name": {"type": "string"}
            },
            "required": ["email", "password", "name"]
        }
    ))

    api_docs.add_endpoint(ApiEndpoint(
        path="/api/auth/token/refresh",
        method="POST",
        summary="Refresh token",
        description="Refresh an authentication token",
        tags=["Authentication"],
        request_body={
            "type": "object",
            "properties": {
                "refresh_token": {"type": "string"}
            },
            "required": ["refresh_token"]
        }
    ))

    api_docs.add_endpoint(ApiEndpoint(
        path="/api/auth/azure/login",
        method="GET",
        summary="Microsoft login",
        description="Authenticate with Microsoft Azure AD",
        tags=["Authentication"]
    ))

    # Add organization endpoints
    api_docs.add_endpoint(ApiEndpoint(
        path="/api/orgs",
        method="GET",
        summary="List organizations",
        description="Get a list of connected Salesforce organizations",
        tags=["Organization"]
    ))

    api_docs.add_endpoint(ApiEndpoint(
        path="/api/org-details",
        method="GET",
        summary="Organization details",
        description="Get detailed information about a Salesforce organization",
        tags=["Organization"],
        request_params=[
            {
                "name": "instanceUrl",
                "in": "query",
                "description": "Salesforce instance URL",
                "required": True,
                "schema": {"type": "string", "format": "uri"}
            }
        ]
    ))

    api_docs.add_endpoint(ApiEndpoint(
        path="/api/connect-org",
        method="POST",
        summary="Connect organization",
        description="Connect to a Salesforce organization (FastAPI endpoint)",
        tags=["Organization"],
        request_body={
            "type": "object",
            "properties": {
                "name": {"type": "string"},
                "instanceUrl": {"type": "string", "format": "uri"},
                "type": {"type": "string", "default": "Salesforce"},
                "description": {"type": "string"}
            },
            "required": ["name", "instanceUrl"]
        }
    ))

    # Security health check endpoints have been removed as requested

    # Security health check endpoints are now provided by the security_analysis blueprint
    # at /api/api/health-score and /api/api/health-risks

    # Add user profile endpoints
    api_docs.add_endpoint(ApiEndpoint(
        path="/api/user/profile",
        method="GET",
        summary="Get user profile",
        description="Get current user profile information",
        tags=["User Profile"]
    ))

    api_docs.add_endpoint(ApiEndpoint(
        path="/api/user/profile",
        method="PUT",
        summary="Update user profile",
        description="Update current user profile information",
        tags=["User Profile"],
        request_body={
            "type": "object",
            "properties": {
                "firstName": {"type": "string"},
                "lastName": {"type": "string"},
                "contact": {"type": "string"},
                "jobTitle": {"type": "string"},
                "company": {"type": "string"},
                "state": {"type": "string"},
                "country": {"type": "string"}
            }
        }
    ))

    api_docs.add_endpoint(ApiEndpoint(
        path="/api/user/password",
        method="POST",
        summary="Change password",
        description="Change current user password",
        tags=["User Profile"],
        request_body={
            "type": "object",
            "properties": {
                "currentPassword": {"type": "string", "format": "password"},
                "newPassword": {"type": "string", "format": "password"}
            },
            "required": ["currentPassword", "newPassword"]
        }
    ))

    # Health endpoint has been removed as requested

    api_docs.add_endpoint(ApiEndpoint(
        path="/api/info",
        method="GET",
        summary="System info",
        description="Get detailed system information",
        tags=["System"]
    ))

    # Add key vault endpoints
    api_docs.add_endpoint(ApiEndpoint(
        path="/api/key-vault/secrets",
        method="POST",
        summary="Store secret",
        description="Store a secret in Azure Key Vault",
        tags=["Key Vault"],
        request_body={
            "type": "object",
            "properties": {
                "name": {"type": "string"},
                "value": {"type": "string"}
            },
            "required": ["name", "value"]
        }
    ))

    # Generate OpenAPI specification
    openapi_spec = api_docs.to_dict()

    # Create Swagger UI HTML
    html_content = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>AtomSec API Documentation</title>
        <link rel="stylesheet" href="https://unpkg.com/swagger-ui-dist@4.5.0/swagger-ui.css" />
        <style>
            body {{
                margin: 0;
                padding: 0;
                font-family: Arial, sans-serif;
            }}
            .swagger-ui .topbar {{
                background-color: #0066cc;
                padding: 10px 0;
            }}
            .swagger-ui .info {{
                margin: 20px 0;
            }}
            .swagger-ui .info h2 {{
                color: #0066cc;
            }}
            .swagger-ui .opblock-tag {{
                background-color: #f5f5f5;
                padding: 10px;
                border-radius: 5px;
                margin: 10px 0;
            }}
            .swagger-ui .opblock {{
                border-radius: 5px;
                margin: 10px 0;
            }}
            .swagger-ui .opblock-get {{
                background-color: rgba(97, 175, 254, 0.1);
            }}
            .swagger-ui .opblock-post {{
                background-color: rgba(73, 204, 144, 0.1);
            }}
            .swagger-ui .opblock-put {{
                background-color: rgba(252, 161, 48, 0.1);
            }}
            .swagger-ui .opblock-delete {{
                background-color: rgba(249, 62, 62, 0.1);
            }}
            .swagger-ui .opblock-summary-method {{
                border-radius: 3px;
            }}
            .swagger-ui .opblock-summary {{
                padding: 5px;
            }}
            .swagger-ui .opblock-summary-description {{
                font-size: 14px;
                color: #333;
            }}
            .swagger-ui .parameters-container {{
                padding: 10px;
            }}
            .swagger-ui .parameter__name {{
                font-weight: bold;
            }}
            .swagger-ui .parameter__in {{
                color: #888;
                font-size: 12px;
            }}
            .swagger-ui .btn {{
                border-radius: 3px;
                padding: 5px 10px;
            }}
            .swagger-ui .execute {{
                background-color: #0066cc;
                color: white;
            }}
            .swagger-ui .response-col_status {{
                font-weight: bold;
            }}
            .swagger-ui .response-col_description {{
                font-size: 14px;
            }}
            .swagger-ui .model {{
                padding: 10px;
                background-color: #f5f5f5;
                border-radius: 5px;
            }}
            .swagger-ui .model-title {{
                font-weight: bold;
                color: #0066cc;
            }}
            .swagger-ui .model-box {{
                padding: 5px;
            }}
            .swagger-ui .model-property {{
                margin: 5px 0;
            }}
            .swagger-ui .model-property-name {{
                font-weight: bold;
            }}
            .swagger-ui .model-property-type {{
                color: #888;
                font-size: 12px;
            }}
            .swagger-ui .model-property-description {{
                font-size: 14px;
            }}
            .swagger-ui .model-example {{
                padding: 10px;
                background-color: #f5f5f5;
                border-radius: 5px;
                margin-top: 10px;
            }}
            .swagger-ui .model-example-value {{
                font-family: monospace;
                font-size: 14px;
            }}
            .swagger-ui .model-example-title {{
                font-weight: bold;
                color: #0066cc;
            }}
            .swagger-ui .model-example-description {{
                font-size: 14px;
            }}
            .swagger-ui .model-example-value pre {{
                background-color: #f5f5f5;
                padding: 10px;
                border-radius: 5px;
                overflow-x: auto;
            }}
            .swagger-ui .model-example-value code {{
                font-family: monospace;
                font-size: 14px;
            }}
            .swagger-ui .model-example-value .token.punctuation {{
                color: #888;
            }}
            .swagger-ui .model-example-value .token.property {{
                color: #0066cc;
            }}
            .swagger-ui .model-example-value .token.string {{
                color: #008000;
            }}
            .swagger-ui .model-example-value .token.number {{
                color: #aa0000;
            }}
            .swagger-ui .model-example-value .token.boolean {{
                color: #aa0000;
            }}
            .swagger-ui .model-example-value .token.null {{
                color: #aa0000;
            }}
            .swagger-ui .model-example-value .token.keyword {{
                color: #aa0000;
            }}
            .swagger-ui .model-example-value .token.operator {{
                color: #888;
            }}
            .swagger-ui .model-example-value .token.comment {{
                color: #888;
            }}
            .swagger-ui .model-example-value .token.function {{
                color: #0066cc;
            }}
            .swagger-ui .model-example-value .token.important {{
                color: #aa0000;
            }}
            .swagger-ui .model-example-value .token.entity {{
                color: #aa0000;
            }}
            .swagger-ui .model-example-value .token.url {{
                color: #0066cc;
            }}
            .swagger-ui .model-example-value .token.symbol {{
                color: #aa0000;
            }}
            .swagger-ui .model-example-value .token.regex {{
                color: #008000;
            }}
            .swagger-ui .model-example-value .token.variable {{
                color: #0066cc;
            }}
            .swagger-ui .model-example-value .token.constant {{
                color: #aa0000;
            }}
            .swagger-ui .model-example-value .token.attr-name {{
                color: #0066cc;
            }}
            .swagger-ui .model-example-value .token.attr-value {{
                color: #008000;
            }}
            .swagger-ui .model-example-value .token.builtin {{
                color: #0066cc;
            }}
            .swagger-ui .model-example-value .token.class-name {{
                color: #0066cc;
            }}
            .swagger-ui .model-example-value .token.namespace {{
                color: #0066cc;
            }}
            .swagger-ui .model-example-value .token.prolog {{
                color: #888;
            }}
            .swagger-ui .model-example-value .token.doctype {{
                color: #888;
            }}
            .swagger-ui .model-example-value .token.cdata {{
                color: #888;
            }}
            .swagger-ui .model-example-value .token.tag {{
                color: #0066cc;
            }}
            .swagger-ui .model-example-value .token.selector {{
                color: #0066cc;
            }}
            .swagger-ui .model-example-value .token.atrule {{
                color: #0066cc;
            }}
        </style>
    </head>
    <body>
        <div id="swagger-ui"></div>

        <script src="https://unpkg.com/swagger-ui-dist@4.5.0/swagger-ui-bundle.js"></script>
        <script>
            window.onload = function() {{
                const ui = SwaggerUIBundle({{
                    spec: {json.dumps(openapi_spec)},
                    dom_id: '#swagger-ui',
                    deepLinking: true,
                    presets: [
                        SwaggerUIBundle.presets.apis,
                        SwaggerUIBundle.SwaggerUIStandalonePreset
                    ],
                    layout: "BaseLayout",
                    docExpansion: "list",
                    defaultModelsExpandDepth: 1,
                    defaultModelExpandDepth: 1,
                    defaultModelRendering: "model",
                    displayRequestDuration: true,
                    showExtensions: true,
                    showCommonExtensions: true,
                    tagsSorter: "alpha",
                    operationsSorter: "alpha"
                }});
                window.ui = ui;
            }}
        </script>
    </body>
    </html>
    """

    response = func.HttpResponse(
        html_content,
        mimetype="text/html",
        status_code=200
    )

    # Apply CORS middleware
    return cors_middleware(req, response)

