import azure.functions as func
import logging
import json
from blueprints.integration import connect_integration

def main(req: func.HttpRequest) -> func.HttpResponse:
    """
    Azure Functions HTTP trigger function for connect endpoint
    
    This function is a direct handler for the connect endpoint.
    It calls the connect_integration function from the integration blueprint.
    
    Args:
        req: HTTP request
        
    Returns:
        func.HttpResponse: HTTP response
    """
    logging.info('Processing connect request directly...')
    
    # Call the connect_integration function directly
    return connect_integration(req)
