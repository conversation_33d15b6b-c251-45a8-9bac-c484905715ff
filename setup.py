from setuptools import setup, find_packages

def read_requirements(filename):
    with open(filename, 'r') as file:
        return [line.strip() for line in file 
                if line.strip() and not line.startswith('#') and not line.startswith('-')]

setup(
    name="atomsec-func-sfdc",
    version="0.1.0",
    packages=find_packages(exclude=['tests', 'examples']),
    install_requires=read_requirements('requirements.txt'),
    extras_require={
        'dev': [
            'pytest>=7.4.4',
            'pytest-asyncio>=0.23.5',
            'hypothesis>=6.98.0',
        ]
    },
    python_requires='>=3.8,<3.12',
    entry_points={
        'console_scripts': [
            'atomsec-func=function_app:main',
        ],
    },
    classifiers=[
        'Development Status :: 3 - Alpha',
        'Intended Audience :: Developers',
        'Programming Language :: Python :: 3.8',
        'Programming Language :: Python :: 3.9',
        'Programming Language :: Python :: 3.10',
        'Programming Language :: Python :: 3.11',
    ],
)
