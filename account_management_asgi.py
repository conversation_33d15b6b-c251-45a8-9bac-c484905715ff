"""
Account Management ASGI Application

This module provides a FastAPI application specifically for account management endpoints.
It is designed to be integrated with Azure Functions via the ASGI middleware.
"""

import logging
from fastapi import FastAP<PERSON>, HTTPException, status, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, <PERSON>
from typing import Optional, Any, List, Dict
from datetime import datetime, timezone
import random

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger('account_management_asgi')

# Import shared modules
from shared.config import is_local_dev
from shared.data_access import get_table_storage_repository
from shared.utils import create_json_response

# Define Pydantic models
class AccountCreate(BaseModel):
    name: str = Field(..., description="Account name")

class UserCreate(BaseModel):
    name: str = Field(..., description="User name")
    email: str = Field(..., description="User email")
    phone: Optional[str] = Field(None, description="User phone")
    account_id: str = Field(..., description="Account ID")

class UserRoleAssign(BaseModel):
    role_id: str = Field(..., description="Role ID")

class ApiResponse(BaseModel):
    success: bool = Field(..., description="Success status")
    statusCode: int = Field(..., description="HTTP status code")
    timestamp: str = Field(..., description="Response timestamp")
    data: Any = Field(None, description="Response data")
    message: Optional[str] = Field(None, description="Response message")

# Create FastAPI app
app = FastAPI(
    title="Account Management API",
    description="API for account management",
    version="1.0.0",
    # Don't expose docs at the root level to avoid conflicts
    docs_url="/api/account-docs",
    redoc_url="/api/account-redoc",
    openapi_url="/api/account-openapi.json"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create a middleware to log all requests
class LoggingMiddleware:
    async def __call__(self, request, call_next):
        logger.info(f"Request: {request.method} {request.url.path}")
        logger.info(f"Request headers: {dict(request.headers)}")

        # Log request body if available
        try:
            body = await request.body()
            if body:
                logger.info(f"Request body: {body.decode('utf-8')}")
        except Exception as e:
            logger.info(f"Could not log request body: {str(e)}")

        logger.info(f"Request query params: {dict(request.query_params)}")
        logger.info(f"Request path params: {dict(request.path_params)}")

        # Process the request
        response = await call_next(request)

        logger.info(f"Response status code: {response.status_code}")
        logger.info(f"Response headers: {dict(response.headers)}")

        return response

# Add logging middleware
app.middleware("http")(LoggingMiddleware())

# Define API routes for accounts
# Support multiple patterns for maximum compatibility
@app.get("/api/accounts", response_model=ApiResponse)
@app.get("/api/api/accounts", response_model=ApiResponse)
@app.get("/api/api/api/accounts", response_model=ApiResponse)
@app.get("/accounts", response_model=ApiResponse)
async def get_accounts(include_inactive: bool = False, request: Request = None):
    """
    Get all accounts endpoint

    This endpoint retrieves all accounts from the database.
    If the user is an admin, all accounts are returned.
    Otherwise, only the accounts the user belongs to are returned.
    """
    logger.info('Processing get accounts request...')

    try:
        # Get current user from authorization header
        from shared.auth_utils import get_token_from_header, decode_token

        # Get token from header
        auth_header = request.headers.get("Authorization") if request else None
        token = None
        if auth_header:
            parts = auth_header.split()
            if len(parts) == 2 and parts[0].lower() == "bearer":
                token = parts[1]

        # Decode token to get user email
        user_email = None
        if token:
            from shared.auth_utils import decode_token
            payload = decode_token(token)
            if payload:
                user_email = payload.get("sub")

        logger.info(f"Current user email: {user_email}")

        # Check if user is admin
        from shared.user_repository import is_user_admin, get_user_account_by_email
        is_admin = False
        if user_email:
            is_admin = is_user_admin(user_email)
            logger.info(f"User {user_email} is admin: {is_admin}")

        # Get accounts
        accounts = []

        # Use Azure Table Storage for local development
        account_repo = get_table_storage_repository('account')
        if not account_repo:
            logger.error("Account table repository not available")

            # Return an error
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Account table repository not available"
            )
        else:
            # Query accounts
            filter_query = "IsActive eq true" if not include_inactive else None
            logger.info(f"Querying accounts with filter: {filter_query}")

            try:
                # Get the table client directly
                table_client = account_repo.table_client

                # First, try to get all entities to see if the table has any data
                logger.info("Querying all entities in the account table using direct table client")

                try:
                    # Query all entities
                    all_entities = list(table_client.query_entities(""))
                    logger.info(f"Total entities in account table: {len(all_entities)}")

                    if all_entities:
                        logger.info("Sample entities in account table:")
                        for i, entity in enumerate(all_entities[:3]):  # Log up to 3 entities
                            logger.info(f"Entity {i+1}: {entity}")
                        if len(all_entities) > 3:
                            logger.info(f"... and {len(all_entities) - 3} more entities")

                    # Now query with the filter if specified
                    if filter_query:
                        logger.info(f"Querying with filter: {filter_query}")
                        entities = list(table_client.query_entities(filter_query))
                    else:
                        entities = all_entities

                    logger.info(f"Found {len(entities)} account entities matching filter")

                    # Log the raw entities for debugging
                    for entity in entities:
                        logger.info(f"Account entity matching filter: {entity}")
                except Exception as qe:
                    logger.error(f"Error querying entities with table client: {str(qe)}")
                    entities = []
            except Exception as e:
                logger.error(f"Error accessing table client: {str(e)}")
                entities = []

            # Convert entities to accounts
            for entity in entities:
                account = {
                    "ID": entity.get("RowKey"),
                    "Name": entity.get("Name", ""),
                    "CreatedAt": entity.get("CreatedAt", ""),
                    "IsActive": entity.get("IsActive", True)
                }
                accounts.append(account)

            logger.info(f"Converted {len(accounts)} account entities to accounts")

        # Return accounts
        return {
            "success": True,
            "statusCode": 200,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data": accounts
        }
    except Exception as e:
        logger.error(f"Error getting accounts: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting accounts: {str(e)}"
        )

@app.post("/api/accounts", response_model=ApiResponse, status_code=status.HTTP_201_CREATED)
@app.post("/api/api/accounts", response_model=ApiResponse, status_code=status.HTTP_201_CREATED)
@app.post("/api/api/api/accounts", response_model=ApiResponse, status_code=status.HTTP_201_CREATED)
@app.post("/accounts", response_model=ApiResponse, status_code=status.HTTP_201_CREATED)
async def create_account(account: AccountCreate):
    """
    Create account endpoint

    This endpoint creates a new account in the database.
    """
    logger.info('Processing create account request...')

    try:
        # Extract account data
        name = account.name

        if not name:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Name is required"
            )

        # Generate account ID
        account_id = str(random.randint(1000, 9999))

        # Create entity
        created_at = datetime.now(timezone.utc)

        # Use Azure Table Storage for local development
        account_repo = get_table_storage_repository('account')
        if not account_repo:
            logger.error("Account table repository not available")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Account table repository not available"
            )

        entity = {
            "PartitionKey": "account",
            "RowKey": account_id,
            "Name": name,
            "CreatedAt": created_at.isoformat(),
            "IsActive": True
        }

        # Log the entity being inserted
        logger.info(f"Inserting account entity: {entity}")

        # Insert entity using direct table client access
        try:
            # Get the table client directly
            table_client = account_repo.table_client

            # Insert the entity directly
            logger.info(f"Inserting entity directly using table_client.upsert_entity: {entity}")
            table_client.upsert_entity(entity)
            logger.info(f"Successfully inserted account entity with ID {account_id}")

            # Verify the entity was inserted
            try:
                logger.info(f"Verifying entity with PartitionKey={entity['PartitionKey']}, RowKey={entity['RowKey']}")
                verification_entity = table_client.get_entity(entity['PartitionKey'], entity['RowKey'])
                logger.info(f"Verified account entity exists: {verification_entity}")
            except Exception as ve:
                logger.error(f"Error verifying account entity: {str(ve)}")
                # Continue anyway since the insert might have succeeded
        except Exception as ie:
            logger.error(f"Exception during upsert_entity: {str(ie)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error creating account: {str(ie)}"
            )

        # Return created account
        created_account = {
            "ID": account_id,
            "Name": name,
            "CreatedAt": created_at.isoformat(),
            "IsActive": True
        }

        return {
            "success": True,
            "statusCode": 201,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data": created_account
        }
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error creating account: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating account: {str(e)}"
        )

@app.get("/api/accounts/{account_id}/users", response_model=ApiResponse)
@app.get("/api/api/accounts/{account_id}/users", response_model=ApiResponse)
@app.get("/api/api/api/accounts/{account_id}/users", response_model=ApiResponse)
async def get_users_for_account(account_id: str):
    """
    Get users for an account endpoint

    This endpoint retrieves all users for a specific account.
    """
    logger.info(f'Processing get users for account {account_id} request...')

    try:
        # Get users for account
        users = []

        # Use Azure Table Storage for local development
        user_repo = get_table_storage_repository('UserAccount')
        if user_repo:
            # Query users
            filter_query = f"AccountId eq '{account_id}'"
            entities = user_repo.query_entities(filter_query)

            # Convert entities to users
            for entity in entities:
                user = {
                    "UserId": entity.get("RowKey"),
                    "Name": entity.get("Name", ""),
                    "Email": entity.get("Email", ""),
                    "Phone": entity.get("Phone", ""),
                    "AccountId": entity.get("AccountId", ""),
                    "CreatedAt": entity.get("CreatedAt", ""),
                    "IsActive": entity.get("IsActive", True)
                }

                # Get roles for user
                roles = []
                role_repo = get_table_storage_repository('userroles')
                if role_repo:
                    filter_query = f"UserId eq '{user['UserId']}'"
                    role_entities = role_repo.query_entities(filter_query)

                    for role_entity in role_entities:
                        role_id = role_entity.get("RoleId", "")

                        # Get role details
                        role_details_repo = get_table_storage_repository('roles')
                        if role_details_repo:
                            filter_query = f"RowKey eq '{role_id}'"
                            role_details_entities = role_details_repo.query_entities(filter_query)

                            if role_details_entities:
                                role_details = role_details_entities[0]
                                role = {
                                    "RoleId": role_id,
                                    "Rolename": role_details.get("Rolename", ""),
                                    "Description": role_details.get("Description", "")
                                }
                                roles.append(role)

                user["Roles"] = roles
                users.append(user)

        # Return users
        return {
            "success": True,
            "statusCode": 200,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data": users
        }
    except Exception as e:
        logger.error(f"Error getting users for account: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting users for account: {str(e)}"
        )

@app.get("/api/roles", response_model=ApiResponse)
@app.get("/api/api/roles", response_model=ApiResponse)
@app.get("/api/api/api/roles", response_model=ApiResponse)
@app.get("/roles", response_model=ApiResponse)
async def get_roles():
    """
    Get all roles endpoint

    This endpoint retrieves all roles from the database.
    """
    logger.info('Processing get roles request...')

    try:
        # Get roles
        roles = []

        # Define standard roles
        standard_roles = [
            {
                "RoleId": "1",
                "Rolename": "Admin",
                "Description": "Administrator role with full access"
            },
            {
                "RoleId": "2",
                "Rolename": "Account Admin",
                "Description": "Can manage users and integrations within their account"
            },
            {
                "RoleId": "3",
                "Rolename": "Integration Manager",
                "Description": "Can add and manage integrations"
            },
            {
                "RoleId": "4",
                "Rolename": "Viewer",
                "Description": "Read-only access to integrations and their data"
            }
        ]

        # Use Azure Table Storage for local development
        role_repo = get_table_storage_repository('roles')
        if not role_repo:
            logger.error("Role table repository not available")

            # Return standard roles
            roles = standard_roles
        else:
            # Query roles
            entities = role_repo.query_entities()

            # Convert entities to roles
            for entity in entities:
                role = {
                    "RoleId": entity.get("RowKey"),
                    "Rolename": entity.get("Rolename", ""),
                    "Description": entity.get("Description", "")
                }
                roles.append(role)

            # If no roles found, create and return standard roles
            if not roles:
                # Create standard roles in the database
                for role in standard_roles:
                    role_entity = {
                        "PartitionKey": "role",
                        "RowKey": role["RoleId"],
                        "Rolename": role["Rolename"],
                        "Description": role["Description"]
                    }
                    role_repo.insert_entity(role_entity)

                # Return standard roles
                roles = standard_roles

        # Return roles
        return {
            "success": True,
            "statusCode": 200,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data": roles
        }
    except Exception as e:
        logger.error(f"Error getting roles: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting roles: {str(e)}"
        )

@app.post("/api/users", response_model=ApiResponse, status_code=status.HTTP_201_CREATED)
@app.post("/api/api/users", response_model=ApiResponse, status_code=status.HTTP_201_CREATED)
@app.post("/api/api/api/users", response_model=ApiResponse, status_code=status.HTTP_201_CREATED)
async def create_user(user: UserCreate, request: Request = None):
    """
    Create user endpoint

    This endpoint creates a new user in the database.
    """
    logger.info('Processing create user request...')

    try:
        # Extract user data
        name = user.name
        email = user.email
        phone = user.phone
        account_id = user.account_id

        if not name or not email or not account_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Name, email, and account_id are required"
            )

        # Get current user from authorization header to check permissions
        auth_header = request.headers.get("Authorization") if request else None
        token = None
        if auth_header:
            parts = auth_header.split()
            if len(parts) == 2 and parts[0].lower() == "bearer":
                token = parts[1]

        # Decode token to get user email
        current_user_email = None
        if token:
            from shared.auth_utils import decode_token
            payload = decode_token(token)
            if payload:
                current_user_email = payload.get("sub")

        logger.info(f"Current user email: {current_user_email}")

        # Check if current user is admin
        from shared.user_repository import is_user_admin
        is_admin = False
        if current_user_email:
            is_admin = is_user_admin(current_user_email)
            logger.info(f"User {current_user_email} is admin: {is_admin}")

        # Only admins can create users
        if not is_admin and current_user_email:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only administrators can create users"
            )

        # Use Azure Table Storage for local development
        user_repo = get_table_storage_repository('UserAccount')
        if not user_repo:
            logger.error("User table repository not available")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create user"
            )

        # Check if user already exists
        filter_query = f"Email eq '{email}'"
        entities = user_repo.query_entities(filter_query)

        if entities:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User with this email already exists"
            )

        # Check if account exists
        account_repo = get_table_storage_repository('account')
        if account_repo:
            filter_query = f"RowKey eq '{account_id}'"
            account_entities = account_repo.query_entities(filter_query)

            if not account_entities:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Account with ID {account_id} does not exist"
                )

        # Generate user ID
        user_id = str(random.randint(1000, 9999))

        # Create entity
        created_at = datetime.now(timezone.utc)
        entity = {
            "PartitionKey": "user",
            "RowKey": user_id,
            "Name": name,
            "Email": email,
            "Phone": phone or "",
            "AccountId": account_id,
            "CreatedAt": created_at.isoformat(),
            "IsActive": True
        }

        # Insert entity
        success = user_repo.insert_entity(entity)
        if not success:
            logger.error("Failed to insert user entity")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create user"
            )

        # Return created user
        created_user = {
            "UserId": user_id,
            "Name": name,
            "Email": email,
            "Phone": phone or "",
            "AccountId": account_id,
            "CreatedAt": created_at.isoformat(),
            "IsActive": True,
            "Roles": []
        }

        return {
            "success": True,
            "statusCode": 201,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data": created_user
        }
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error creating user: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating user: {str(e)}"
        )

@app.post("/api/users/{user_id}/roles", response_model=ApiResponse)
@app.post("/api/api/users/{user_id}/roles", response_model=ApiResponse)
@app.post("/api/api/api/users/{user_id}/roles", response_model=ApiResponse)
async def assign_role_to_user(user_id: str, role_assignment: UserRoleAssign, request: Request = None):
    """
    Assign role to user endpoint

    This endpoint assigns a role to a user.
    Only administrators can assign roles.
    """
    logger.info(f'Processing assign role to user {user_id} request...')

    try:
        # Get current user from authorization header to check permissions
        auth_header = request.headers.get("Authorization") if request else None
        token = None
        if auth_header:
            parts = auth_header.split()
            if len(parts) == 2 and parts[0].lower() == "bearer":
                token = parts[1]

        # Decode token to get user email
        current_user_email = None
        if token:
            from shared.auth_utils import decode_token
            payload = decode_token(token)
            if payload:
                current_user_email = payload.get("sub")

        logger.info(f"Current user email: {current_user_email}")

        # Check if current user is admin
        from shared.user_repository import is_user_admin
        is_admin = False
        if current_user_email:
            is_admin = is_user_admin(current_user_email)
            logger.info(f"User {current_user_email} is admin: {is_admin}")

        # Only admins can assign roles
        if not is_admin and current_user_email:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only administrators can assign roles"
            )

        # Extract role data
        role_id = role_assignment.role_id

        if not role_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Role ID is required"
            )

        # Check if user exists
        user_repo = get_table_storage_repository('UserAccount')
        if user_repo:
            # Query all entities (not efficient, but works for local dev)
            user_entities = user_repo.query_entities("")

            # Find entity with matching user ID
            matching_user_entities = [e for e in user_entities if e.get("RowKey") == user_id]

            if not matching_user_entities:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"User with ID {user_id} not found"
                )

        # Check if role exists
        role_details_repo = get_table_storage_repository('roles')
        role_details = None

        if role_details_repo:
            filter_query = f"RowKey eq '{role_id}'"
            role_details_entities = role_details_repo.query_entities(filter_query)

            if not role_details_entities:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Role with ID {role_id} not found"
                )
            else:
                role_details = role_details_entities[0]

        # Use Azure Table Storage for local development
        role_repo = get_table_storage_repository('userroles')
        if not role_repo:
            logger.error("User role table repository not available")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to assign role"
            )

        # Check if role assignment already exists
        filter_query = f"UserId eq '{user_id}' and RoleId eq '{role_id}'"
        entities = role_repo.query_entities(filter_query)

        if entities:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Role already assigned to user"
            )

        # Create entity
        import uuid
        assignment_id = str(uuid.uuid4())
        entity = {
            "PartitionKey": "userrole",
            "RowKey": assignment_id,
            "UserId": user_id,
            "RoleId": role_id,
            "AssignedAt": datetime.now(timezone.utc).isoformat()
        }

        # Insert entity
        success = role_repo.insert_entity(entity)
        if not success:
            logger.error("Failed to insert user role entity")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to assign role"
            )

        # Return assigned role
        assigned_role = {
            "RoleId": role_id,
            "Rolename": role_details.get("Rolename", "") if role_details else "",
            "Description": role_details.get("Description", "") if role_details else ""
        }

        return {
            "success": True,
            "statusCode": 200,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data": assigned_role
        }
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error assigning role to user: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error assigning role to user: {str(e)}"
        )