<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="0" skipped="18" tests="64" time="3.458" timestamp="2025-05-06T15:27:32.528977+05:30" hostname="DESKTOP-OPJM3SV"><testcase classname="tests.test_WrapperFunction" name="test_fastapi_sample" time="0.001"><skipped type="pytest.skip" message="WrapperFunction moved to examples/ and not actively deployed">C:\Users\<USER>\Downloads\A2OM\live-atomsec\atomsec-func-sfdc\tests\test_WrapperFunction.py:14: WrapperFunction moved to examples/ and not actively deployed</skipped></testcase><testcase classname="tests.test_WrapperFunction" name="test_fastapi_hello" time="0.001"><skipped type="pytest.skip" message="WrapperFunction moved to examples/ and not actively deployed">C:\Users\<USER>\Downloads\A2OM\live-atomsec\atomsec-func-sfdc\tests\test_WrapperFunction.py:20: WrapperFunction moved to examples/ and not actively deployed</skipped></testcase><testcase classname="tests.test_WrapperFunction" name="test_fastapi_hello_different_name" time="0.000"><skipped type="pytest.skip" message="WrapperFunction moved to examples/ and not actively deployed">C:\Users\<USER>\Downloads\A2OM\live-atomsec\atomsec-func-sfdc\tests\test_WrapperFunction.py:26: WrapperFunction moved to examples/ and not actively deployed</skipped></testcase><testcase classname="tests.test_auth.TestAuth" name="test_create_user_from_repository" time="0.005" /><testcase classname="tests.test_auth.TestAuth" name="test_get_user" time="0.005" /><testcase classname="tests.test_auth.TestAuth" name="test_hash_password" time="0.003" /><testcase classname="tests.test_auth.TestAuth" name="test_signup" time="0.005" /><testcase classname="tests.test_auth_fixed.TestAuth" name="test_create_user_from_repository" time="0.005" /><testcase classname="tests.test_auth_fixed.TestAuth" name="test_get_user" time="0.005" /><testcase classname="tests.test_auth_fixed.TestAuth" name="test_hash_password" time="0.002" /><testcase classname="tests.test_auth_fixed.TestAuth" name="test_signup" time="0.006" /><testcase classname="tests.test_blueprint_registration.TestBlueprintRegistration" name="test_all_blueprints_registered" time="0.006" /><testcase classname="tests.test_blueprint_registration.TestBlueprintRegistration" name="test_blueprint_functions" time="0.003" /><testcase classname="tests.test_blueprint_registration.TestBlueprintRegistration" name="test_blueprint_functions_availability" time="0.002"><skipped type="pytest.skip" message="Skipping deployment tests. Set RUN_DEPLOYMENT_TESTS=true to run.">C:\Users\<USER>\Downloads\A2OM\live-atomsec\atomsec-func-sfdc\tests\test_blueprint_registration.py:250: Skipping deployment tests. Set RUN_DEPLOYMENT_TESTS=true to run.</skipped></testcase><testcase classname="tests.test_blueprint_registration.TestBlueprintRegistration" name="test_deployed_functions_availability" time="0.002"><skipped type="pytest.skip" message="Skipping deployment tests. Set RUN_DEPLOYMENT_TESTS=true to run.">C:\Users\<USER>\Downloads\A2OM\live-atomsec\atomsec-func-sfdc\tests\test_blueprint_registration.py:163: Skipping deployment tests. Set RUN_DEPLOYMENT_TESTS=true to run.</skipped></testcase><testcase classname="tests.test_blueprint_registration.TestBlueprintRegistration" name="test_function_routes" time="0.009" /><testcase classname="tests.test_blueprint_registration.TestBlueprintRegistration" name="test_proxy_functions" time="0.003" /><testcase classname="tests.test_blueprint_registration.TestBlueprintRegistration" name="test_proxy_functions_availability" time="0.002"><skipped type="pytest.skip" message="Skipping deployment tests. Set RUN_DEPLOYMENT_TESTS=true to run.">C:\Users\<USER>\Downloads\A2OM\live-atomsec\atomsec-func-sfdc\tests\test_blueprint_registration.py:205: Skipping deployment tests. Set RUN_DEPLOYMENT_TESTS=true to run.</skipped></testcase><testcase classname="tests.test_deployment_verification.TestDeploymentVerification" name="test_blueprint_functions_availability" time="0.002"><skipped type="pytest.skip" message="Skipping deployment tests. Set RUN_DEPLOYMENT_TESTS=true to run.">C:\Users\<USER>\Downloads\A2OM\live-atomsec\atomsec-func-sfdc\tests\test_deployment_verification.py:99: Skipping deployment tests. Set RUN_DEPLOYMENT_TESTS=true to run.</skipped></testcase><testcase classname="tests.test_deployment_verification.TestDeploymentVerification" name="test_deployed_functions_availability" time="0.002"><skipped type="pytest.skip" message="Skipping deployment tests. Set RUN_DEPLOYMENT_TESTS=true to run.">C:\Users\<USER>\Downloads\A2OM\live-atomsec\atomsec-func-sfdc\tests\test_deployment_verification.py:29: Skipping deployment tests. Set RUN_DEPLOYMENT_TESTS=true to run.</skipped></testcase><testcase classname="tests.test_deployment_verification.TestDeploymentVerification" name="test_proxy_functions_availability" time="0.002"><skipped type="pytest.skip" message="Skipping deployment tests. Set RUN_DEPLOYMENT_TESTS=true to run.">C:\Users\<USER>\Downloads\A2OM\live-atomsec\atomsec-func-sfdc\tests\test_deployment_verification.py:61: Skipping deployment tests. Set RUN_DEPLOYMENT_TESTS=true to run.</skipped></testcase><testcase classname="tests.test_dummy" name="test_dummy" time="0.002" /><testcase classname="tests.test_fastapi" name="test_health_check" time="0.022" /><testcase classname="tests.test_fastapi" name="test_get_accounts" time="0.019" /><testcase classname="tests.test_fastapi" name="test_create_account" time="0.003"><skipped type="pytest.skip" message="Skipping test_create_account in CI pipeline">C:\Users\<USER>\Downloads\A2OM\live-atomsec\atomsec-func-sfdc\tests\test_fastapi.py:42: Skipping test_create_account in CI pipeline</skipped></testcase><testcase classname="tests.test_fastapi" name="test_get_roles" time="0.015" /><testcase classname="tests.test_fastapi" name="test_create_user" time="0.002"><skipped type="pytest.skip" message="Skipping test_create_user in CI pipeline">C:\Users\<USER>\Downloads\A2OM\live-atomsec\atomsec-func-sfdc\tests\test_fastapi.py:60: Skipping test_create_user in CI pipeline</skipped></testcase><testcase classname="tests.test_fastapi" name="test_assign_role_to_user" time="0.004"><skipped type="pytest.skip" message="Skipping role assignment test until mock table storage is properly implemented">C:\Users\<USER>\Downloads\A2OM\live-atomsec\atomsec-func-sfdc\tests\test_fastapi.py:66: Skipping role assignment test until mock table storage is properly implemented</skipped></testcase><testcase classname="tests.test_fastapi_integration" name="test_fastapi_docs" time="0.001"><skipped type="pytest.skip" message="FastAPI integration not yet enabled in production">C:\Users\<USER>\Downloads\A2OM\live-atomsec\atomsec-func-sfdc\tests\test_fastapi_integration.py:14: FastAPI integration not yet enabled in production</skipped></testcase><testcase classname="tests.test_fastapi_integration" name="test_fastapi_openapi" time="0.001"><skipped type="pytest.skip" message="FastAPI integration not yet enabled in production">C:\Users\<USER>\Downloads\A2OM\live-atomsec\atomsec-func-sfdc\tests\test_fastapi_integration.py:21: FastAPI integration not yet enabled in production</skipped></testcase><testcase classname="tests.test_fastapi_integration" name="test_fastapi_health" time="0.000"><skipped type="pytest.skip" message="FastAPI integration not yet enabled in production">C:\Users\<USER>\Downloads\A2OM\live-atomsec\atomsec-func-sfdc\tests\test_fastapi_integration.py:34: FastAPI integration not yet enabled in production</skipped></testcase><testcase classname="tests.test_fastapi_integration" name="test_fastapi_accounts" time="0.000"><skipped type="pytest.skip" message="FastAPI integration not yet enabled in production">C:\Users\<USER>\Downloads\A2OM\live-atomsec\atomsec-func-sfdc\tests\test_fastapi_integration.py:45: FastAPI integration not yet enabled in production</skipped></testcase><testcase classname="tests.test_fastapi_integration" name="test_fastapi_create_account" time="0.001"><skipped type="pytest.skip" message="FastAPI integration not yet enabled in production">C:\Users\<USER>\Downloads\A2OM\live-atomsec\atomsec-func-sfdc\tests\test_fastapi_integration.py:58: FastAPI integration not yet enabled in production</skipped></testcase><testcase classname="tests.test_general.TestGeneralFunctions" name="test_health_check_success" time="0.004" /><testcase classname="tests.test_general.TestGeneralFunctions" name="test_system_info_success" time="0.004" /><testcase classname="tests.test_integration_tabs.TestIntegrationTabs" name="test_guest_user_risks_endpoint" time="0.005" /><testcase classname="tests.test_integration_tabs.TestIntegrationTabs" name="test_health_check_endpoint" time="0.073" /><testcase classname="tests.test_integration_tabs.TestIntegrationTabs" name="test_overview_endpoint" time="0.014" /><testcase classname="tests.test_integration_tabs.TestIntegrationTabs" name="test_profiles_endpoint" time="0.016" /><testcase classname="tests.test_my_work" name="test_placeholder" time="0.000"><skipped type="pytest.skip" message="Requires deployed API - removed from CI pipeline">C:\Users\<USER>\Downloads\A2OM\live-atomsec\atomsec-func-sfdc\tests\test_my_work.py:7: Requires deployed API - removed from CI pipeline</skipped></testcase><testcase classname="tests.test_organization.TestOrganization" name="test_connect_org" time="0.004" /><testcase classname="tests.test_organization.TestOrganization" name="test_disconnect_org" time="0.004" /><testcase classname="tests.test_organization.TestOrganization" name="test_get_orgs" time="0.005" /><testcase classname="tests.test_organization.TestOrganization" name="test_rescan_org" time="0.005" /><testcase classname="tests.test_profile_metadata" name="test_profile_metadata_success" time="0.029" /><testcase classname="tests.test_profile_metadata" name="test_profile_metadata_token_failure" time="0.006" /><testcase classname="tests.test_scan.TestScan" name="test_get_mock_scan_results" time="0.003" /><testcase classname="tests.test_scan.TestScan" name="test_get_scan_results" time="0.005" /><testcase classname="tests.test_scan.TestScan" name="test_scan_accounts" time="0.007" /><testcase classname="tests.test_scan.TestScan" name="test_scan_history" time="0.007" /><testcase classname="tests.test_security_analysis.TestSecurityAnalysis" name="test_calculate_health_score" time="0.003" /><testcase classname="tests.test_security_analysis.TestSecurityAnalysis" name="test_health_risks" time="0.003" /><testcase classname="tests.test_security_analysis.TestSecurityAnalysis" name="test_health_score" time="0.003" /><testcase classname="tests.test_security_analysis.TestSecurityAnalysis" name="test_permission_sets" time="0.003" /><testcase classname="tests.test_security_analysis.TestSecurityAnalysis" name="test_profiles" time="0.003" /><testcase classname="tests.test_security_health_check.TestSecurityHealthCheck" name="test_security_health_check_success" time="0.097" /><testcase classname="tests.test_security_health_check.TestSecurityHealthCheck" name="test_security_health_check_token_failure" time="0.004" /><testcase classname="tests.test_security_health_check.TestSecurityHealthCheck" name="test_security_risks_by_type_success" time="0.008" /><testcase classname="tests.test_user_repository.TestUserRepository" name="test_authenticate_user_case_insensitive" time="0.005" /><testcase classname="tests.test_user_repository.TestUserRepository" name="test_authenticate_user_local_failure" time="0.005" /><testcase classname="tests.test_user_repository.TestUserRepository" name="test_authenticate_user_local_success" time="0.004" /><testcase classname="tests.test_user_repository.TestUserRepository" name="test_create_user_local" time="0.005" /><testcase classname="tests.test_user_repository.TestUserRepository" name="test_get_user_account_by_email_local" time="0.003" /><testcase classname="tests.test_user_repository.TestUserRepository" name="test_hash_password" time="0.003" /></testsuite></testsuites>