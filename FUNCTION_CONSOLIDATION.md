# Function Consolidation Guide

This document explains the changes made to reduce the number of Azure Functions in the application.

## Problem

The application had too many individual proxy functions, which caused:
1. Difficulty in managing and maintaining the codebase
2. Issues with Azure Functions not displaying correctly in the Azure Portal
3. Slower cold start times due to the large number of functions

## Solution

We implemented a consolidated approach with the following changes:

### 1. Created a Main API Router Function

We created a single, powerful router function that handles multiple API endpoints based on patterns in the URL path:

```python
@app.route(route="api/{*path}", methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"])
def api_router(req: func.HttpRequest) -> func.HttpResponse:
    """
    Main API router function that handles all API requests
    
    This consolidated router function replaces multiple individual proxy functions.
    It routes requests to the appropriate handler based on the path and method.
    """
    # Get the full path from route parameters
    path = req.route_params.get("path", "")
    method = req.method
    
    # Route based on path patterns
    if path.startswith("auth/"):
        # Handle authentication routes
        ...
    elif path.startswith("api/integration/"):
        # Handle integration routes
        ...
    elif path.startswith("api/user/"):
        # Handle user profile routes
        ...
    # etc.
```

### 2. Enhanced the WrapperFunction

We updated the WrapperFunction to handle more routes directly through the FastAPI ASGI app and to forward requests to the main API router:

```python
# Handle API routes that were previously handled by proxy functions
if (route.startswith('api/api/') or 
    route.startswith('api/auth/') or 
    route.startswith('api/user/') or 
    route.startswith('api/tasks/') or
    route.startswith('api/orgs') or
    route.startswith('api/connect-org') or
    route.startswith('api/disconnect-org') or
    route.startswith('api/rescan-org') or
    route.startswith('api/integration/')):
    
    logger.info(f"Routing API request {req.method} /{route} to main API router")
    
    # Modify the route to use the consolidated API router
    if route.startswith('api/'):
        modified_route = route[4:]  # Remove 'api/' prefix
        
        # Update the route parameter
        req.route_params['path'] = modified_route
        
        # Forward to the main API router function
        from function_app import api_router
        return api_router(req)
```

### 3. Removed Individual Proxy Functions

We removed the following individual proxy functions that were consolidated into the main API router:

- `auth_proxy`
- `integration_proxy`
- `integration_scan_proxy`
- `integration_scan_metadata_proxy`
- `user_profile_proxy`
- `tasks_proxy`
- `accounts_proxy`
- `account_users_proxy`
- `create_user_proxy`
- `assign_user_role_proxy`
- `roles_proxy`
- `get_orgs_proxy`
- `org_action_proxy`

## Benefits

1. **Reduced Function Count**: The number of Azure Functions has been significantly reduced
2. **Improved Maintainability**: Code is more organized and easier to maintain
3. **Better Performance**: Fewer functions means faster cold start times
4. **Simplified Routing**: All API routing logic is centralized in one place

## How to Add New Endpoints

To add new endpoints to the application:

1. Add a new condition to the `api_router` function in `function_app.py`:

```python
elif path.startswith("api/new-feature/"):
    # Handle new feature routes
    action = path[15:]  # Remove "api/new-feature/" prefix
    
    if action == "some-action" and method == "POST":
        from blueprints.new_feature import some_action
        return some_action(req)
    # etc.
```

2. If needed, update the WrapperFunction to recognize the new route pattern:

```python
if (route.startswith('api/api/') or 
    # ... existing patterns ...
    route.startswith('api/new-feature/')):
    # ... existing code ...
```

## Testing

When testing the consolidated functions:

1. Test all existing endpoints to ensure they still work correctly
2. Pay special attention to endpoints that use route parameters
3. Verify that CORS middleware is still applied correctly
4. Check that error handling works as expected
