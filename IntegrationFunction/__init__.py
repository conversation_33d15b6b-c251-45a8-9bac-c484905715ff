import azure.functions as func
import logging
import json
import re
from function_app import app

# This file is required for Azure Functions to discover the function
# It routes requests to the appropriate proxy function based on the route parameter

def main(req: func.HttpRequest) -> func.HttpResponse:
    """
    Azure Functions HTTP trigger function for integration endpoints

    This function routes requests to the appropriate proxy function based on the route parameter.
    It handles the following routes:
    - /api/api/integration/test-connection (POST)
    - /api/api/integration/connect (POST)
    - /api/api/integrations/{id}/scan (POST)

    Args:
        req: HTTP request

    Returns:
        func.HttpResponse: HTTP response
    """
    logging.info('Processing integration request...')

    # Get the route parameter
    route = req.route_params.get('route', '')
    logging.info(f'Route parameter: {route}')

    # Get the full URL for debugging
    full_url = req.url
    logging.info(f'Full URL: {full_url}')

    # Extract the path from the URL
    path = req.url.split('?')[0].split('/api/api/integration')[-1] if '/api/api/integration' in req.url else route
    logging.info(f'Extracted path: {path}')

    # Route the request to the appropriate proxy function
    if (route == '/test-connection' or path == '/test-connection' or 'test-connection' in req.url) and req.method == 'POST':
        logging.info('Routing to test-connection handler')
        # Import the test_connection function directly
        from blueprints.integration import test_connection
        return test_connection(req)
    elif (route == '/connect' or path == '/connect' or 'connect' in req.url) and req.method == 'POST' and 'test-connection' not in req.url:
        logging.info('Routing to connect handler')
        # Import the connect_integration function directly
        from blueprints.integration import connect_integration
        return connect_integration(req)
    elif route.startswith('integrations/') and route.endswith('/scan') and req.method == 'POST':
        # Handle the /api/api/integrations/{id}/scan route
        # The route parameter will be 'integrations/{id}/scan'
        logging.info(f'Routing to scan handler for route: {route}')
        return app.integration_scan_proxy(req)
    else:
        # Log the unhandled route for debugging
        logging.warning(f'Unhandled route: {route}, path: {path}, method: {req.method}')

        # Return 404 for unknown routes
        return func.HttpResponse(
            json.dumps({"success": False, "message": f"Route not found: {route}", "path": path, "url": full_url}),
            mimetype="application/json",
            status_code=404
        )
