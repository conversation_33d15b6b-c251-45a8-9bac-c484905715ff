import azure.functions as func
import logging
import sys
import os

# Configure logging
logger = logging.getLogger('roles_management_function')
logger.setLevel(logging.INFO)

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the FastAPI app
try:
    from account_management_asgi import app as account_app
    logger.info("Successfully imported account management FastAPI app for roles")
except ImportError as e:
    logger.error(f"Error importing account management FastAPI app for roles: {str(e)}")
    logger.error(f"Python path: {sys.path}")

    # Create a dummy FastAPI app as last resort
    import fastapi
    account_app = fastapi.FastAPI()
    logger.warning("Using dummy FastAPI app as fallback")

# Create an ASGI handler for the FastAPI app
async def main(req: func.HttpRequest, context: func.Context) -> func.HttpResponse:
    """
    Azure Functions HTTP trigger function for roles management API

    This function is a wrapper for the FastAPI application.
    It uses the ASGI integration to run the FastAPI app within Azure Functions.

    Args:
        req: HTTP request
        context: Function context (not used directly but required by Azure Functions)

    Returns:
        func.HttpResponse: HTTP response
    """
    try:
        # Log detailed request information
        logger.info(f"Processing roles management request for {req.url}")
        logger.info(f"Request method: {req.method}")
        logger.info(f"Request route: {req.route_params.get('route', 'No route param')}")

        # Use our custom ASGI middleware for better path handling
        from shared.asgi_middleware import AsgiMiddleware
        asgi_handler = AsgiMiddleware(account_app)

        # Process the request through the ASGI handler
        response = asgi_handler.handle(req)

        # Log response information
        logger.info(f"Response status code: {response.status_code}")

        return response
    except Exception as e:
        logger.error(f"Error processing roles management request: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

        # Return error response
        return func.HttpResponse(
            body=f"Error processing roles management request: {str(e)}",
            status_code=500
        )
