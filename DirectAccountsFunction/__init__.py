import logging
import azure.functions as func
import json
from datetime import datetime, timezone

def main(req: func.HttpRequest) -> func.HttpResponse:
    """
    Direct accounts function that bypasses ASGI
    
    This function returns a hardcoded list of accounts without using the ASGI middleware.
    """
    logging.info('DirectAccountsFunction processed a request.')
    
    # Log detailed request information
    logging.info(f"Request URL: {req.url}")
    logging.info(f"Request method: {req.method}")
    logging.info(f"Request headers: {dict(req.headers)}")
    logging.info(f"Request params: {dict(req.params)}")
    logging.info(f"Request route_params: {dict(req.route_params)}")
    
    # Create a hardcoded list of accounts
    accounts = [
        {
            "ID": "1001",
            "Name": "Test Account 1",
            "CreatedAt": datetime.now(timezone.utc).isoformat(),
            "IsActive": True
        },
        {
            "ID": "1002",
            "Name": "Test Account 2",
            "CreatedAt": datetime.now(timezone.utc).isoformat(),
            "IsActive": True
        }
    ]
    
    response_data = {
        "success": True,
        "statusCode": 200,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "data": accounts
    }
    
    return func.HttpResponse(
        json.dumps(response_data),
        mimetype="application/json",
        status_code=200
    )
