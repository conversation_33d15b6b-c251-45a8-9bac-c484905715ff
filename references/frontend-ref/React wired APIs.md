Server Endpoints Used by the React Client

Authentication Endpoints
/auth/login - POST - User login
/auth/signup - POST - User registration
/auth/token/refresh - POST - Refresh authentication token

Organization Management Endpoints
/api/orgs - GET - Fetch all organizations (with optional includeInactive parameter)
/api/disconnect-org - POST - Disconnect an organization
/api/rescan-org - POST - Trigger a rescan of an organization

Salesforce Security Analysis Endpoints
/api/health-score - GET - Fetch health score for a Salesforce instance
/api/health-risks - GET - Fetch security risks for a Salesforce instance
/api/profiles - GET - Fetch Salesforce profiles
/api/permission-sets - GET - Fetch Salesforce permission sets

Other Endpoints
/scan/accounts - GET - Fetch accounts with health check scores (used in ScanResults component)
/Security_Health_Check - GET - Fetch security health check data
/Profile_Metadata - GET - Fetch profile metadata

Test/Example Endpoints
/sample - GET - Example endpoint from FastAPI wrapper
/hello/{name} - GET - Parameterized example endpoint
/httpExample - GET - Example HTTP trigger function
/default_template - GET - Blueprint example endpoint