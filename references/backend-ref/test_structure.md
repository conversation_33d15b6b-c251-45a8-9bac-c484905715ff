# Test Structure Documentation

This document describes the test structure for the AtomSec Azure Functions application.

## Test Types

The application has two main types of tests:

1. **Unit Tests**: Test individual functions in isolation using mocks
2. **Integration Tests**: Test the deployed API endpoints

## Test Files

### Unit Tests

- `tests/test_profile_metadata.py`: Tests for the profile metadata blueprint
- `tests/test_security_health_check.py`: Tests for the security health check blueprint
- `tests/test_general.py`: Tests for the general utilities blueprint

### Integration Tests

- `tests/test_my_work.py`: Tests for the deployed API endpoints
- `tests/test_WrapperFunction.py`: Tests for the FastAPI integration (disabled by default)

### Utility Files

- `tests/make_requests.py`: Utility functions for making HTTP requests to the deployed API

## Running Tests

### Running Tests Locally

To run tests locally:

```bash
# Set the base_url environment variable
export base_url="http://localhost:7071/"

# Run all tests
pytest tests/

# Run specific test files
pytest tests/test_general.py
```

### Running Tests in the Pipeline

The pipeline runs tests in two stages:

1. Integration tests that call the deployed API
2. Unit tests for the blueprints

```yaml
- script: |
    pip install pytest pytest-azurepipelines pytest-asyncio
    export base_url="https://func-atomsec-sfdc-dev-stage.azurewebsites.net/"
    # Run integration tests first
    pytest tests/test_my_work.py -v
    # Then run unit tests for the blueprints
    pytest tests/test_profile_metadata.py tests/test_security_health_check.py tests/test_general.py -v
  displayName: 'Run Tests via. pytest'
```

## Test Dependencies

The tests require the following dependencies:

- `pytest`: The test framework
- `pytest-asyncio`: For testing async functions
- `pytest-azurepipelines`: For Azure DevOps integration
- `requests`: For making HTTP requests

These dependencies are listed in the `requirements.txt` file.

## Mocking

The unit tests use the `unittest.mock` module to mock external dependencies:

- `patch`: For patching functions and methods
- `MagicMock`: For creating mock objects

Example:

```python
@patch('blueprints.profile_metadata.get_salesforce_access_token')
async def test_profile_metadata_token_failure(self, mock_get_token):
    # Mock the Salesforce token failure
    mock_get_token.return_value = (None, None)
    
    # Create a mock HTTP request
    req = func.HttpRequest(
        method='GET',
        body=None,
        url='/api/profile_metadata',
        params={}
    )
    
    # Call the function
    func_call = profile_metadata.build().get_user_function()
    response = await func_call(req)
    
    # Check the response
    self.assertEqual(response.status_code, 500)
    self.assertIn('Failed to obtain Salesforce access token', response.get_body().decode())
    
    # Verify the mock was called
    mock_get_token.assert_called_once()
```

## Disabled Tests

Some tests are disabled by default because they test functionality that is not actively deployed:

```python
@pytest.mark.skip(reason="WrapperFunction moved to examples/ and not actively deployed")
def test_fastapi_sample():
    response = make_request("sample")
    assert response.status_code == 200
    assert "info" in response.json()
```

To enable these tests, remove the `@pytest.mark.skip` decorator.
