# Azure Function App Structure and Billing Model

## Function App Structure in the Repository

After analyzing the codebase, I've identified the following function app structure:

### 1. Main Function App (Current Implementation)

The main function app is defined in `function_app.py` and uses the blueprint pattern to organize functions:

```python
# Create the function app
app = func.FunctionApp(http_auth_level=func.AuthLevel.ANONYMOUS)

# Register all blueprints
app.register_functions(profile_metadata_bp)
app.register_functions(security_health_check_bp)
app.register_functions(general_bp)
```

This is the primary function app that is deployed to Azure as `func-atomsec-sfdc-dev`.

### 2. Legacy/Test Function Apps

The repository also contains several other function app definitions that appear to be either legacy code or test implementations:

1. **Root-level app in `__init__.py`**:
   ```python
   __app__ = func.FunctionApp(http_auth_level=func.AuthLevel.ANONYMOUS)
   __app__.register_blueprint(ProfileMetadataApp)
   __app__.register_blueprint(test_bp)
   ```

2. **TestFunctionApp**:
   ```python
   app = func.FunctionApp()
   
   @app.route(route="HttpExample", auth_level=func.AuthLevel.ANONYMOUS)
   def HttpExample(req: func.HttpRequest) -> func.HttpResponse:
       # Function implementation
   ```

3. **ProfileMetadataApp**:
   ```python
   app = func.FunctionApp()
   
   @app.route(route="Profile_Metadata", auth_level=func.AuthLevel.ANONYMOUS)
   def profile_metadata(req: func.HttpRequest) -> func.HttpResponse:
       # Function implementation
   ```

4. **WrapperFunction** (FastAPI integration):
   ```python
   app = fastapi.FastAPI()
   
   @app.get("/sample")
   async def index():
       # Function implementation
   ```

### Actual Deployment

Based on the pipeline configuration (`pipeline-func-sfdc-dev.yml`), only one function app is actually deployed to Azure:

```yaml
- task: AzureFunctionApp@2
  inputs:
    connectedServiceNameARM: 'sc-atomsec-dev-backend'
    appType: 'functionAppLinux'
    appName: 'func-atomsec-sfdc-dev'
    package: '$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).zip'
    runtimeStack: 'PYTHON|3.11'
    deploymentMethod: 'auto'
    deployToSlotOrASE: true
    resourceGroupName: 'atomsec-dev-backend'
    slotName: 'stage'
```

The deployment process:
1. Packages the entire repository
2. Deploys to a staging slot (`func-atomsec-sfdc-dev-stage`)
3. Runs tests against the staging deployment
4. Swaps the staging slot with production

## Azure Functions Billing Model

Azure Functions offers a serverless compute service that allows you to run code on-demand without having to explicitly provision or manage infrastructure. The billing model is consumption-based, meaning you only pay for what you use.

### Consumption Plan Billing Components

1. **Execution Time**:
   - Measured in GB-seconds (memory allocated × execution time in seconds)
   - First 400,000 GB-seconds per month are free
   - After free grant: ~$0.000016/GB-second (varies by region)

2. **Executions (Invocations)**:
   - First 1 million executions per month are free
   - After free grant: ~$0.20 per million executions

3. **Additional Resources**:
   - Storage Account: Used for function app code and logs
   - Network: Outbound data transfer

### Example Cost Calculation

For a function app with:
- 2 million executions per month
- Average execution time of 500ms
- 256MB memory allocation

**Execution Cost**:
- GB-seconds: 2,000,000 × 0.5s × 0.256GB = 256,000 GB-seconds
- Within free grant of 400,000 GB-seconds, so $0

**Invocation Cost**:
- 2 million executions - 1 million free = 1 million billable executions
- 1 million executions × $0.20 per million = $0.20

**Total**: $0.20 per month (plus storage and network costs)

### Premium Plan Option

For workloads that need to avoid cold starts or require more memory/longer execution times:

- Pre-warmed instances to eliminate cold starts
- More powerful hardware
- Longer execution timeouts (up to 60 minutes)
- Pricing based on instance size and number of instances

### Cost Optimization Strategies

1. **Function Design**:
   - Keep functions small and focused
   - Minimize execution time
   - Share code through common libraries

2. **Memory Settings**:
   - Use the minimum memory needed
   - Test different memory configurations to find optimal settings

3. **Timeout Configuration**:
   - Set appropriate timeouts to prevent runaway functions
   - Current setting in host.json: `"functionTimeout": "00:10:00"`

4. **Concurrency Settings**:
   - Optimize concurrency for better performance
   - Current settings in host.json:
     ```json
     "concurrency": {
       "dynamicConcurrencyEnabled": true,
       "snapshotPersistenceEnabled": true
     }
     ```

5. **Monitoring**:
   - Use Application Insights to identify performance issues
   - Set up alerts for unusual patterns

## Recommendation

Based on the current structure, I recommend:

1. **Consolidate to a Single Function App**: Continue with the blueprint-based approach in the main `function_app.py`
2. **Remove Legacy Code**: Clean up unused function app definitions to avoid confusion
3. **Monitor Consumption**: Set up cost alerts to monitor function app usage
4. **Optimize Cold Starts**: Consider using the Premium plan if cold starts are an issue for critical functions
