# **Optimizing package.xml for Comprehensive Salesforce Security Analysis via Client Credentials Flow**

## **1\. Introduction: The Role of Metadata in Salesforce Security Analysis**

Salesforce metadata serves as the definitive blueprint for an organization's configuration, encompassing its data schema, business processes, user interface logic, and, most critically, its entire security model.1 This metadata is not merely descriptive; it is "data that describes other data" and actively dictates how the Salesforce organization functions, including all mechanisms for authorization and access control.1 A robust and comprehensive security posture assessment of any Salesforce org is therefore fundamentally reliant on an in-depth analysis of this metadata. Such analysis is essential to identify misconfigurations, uncover potential vulnerabilities, detect deviations from established security best practices, and ensure ongoing compliance with internal and external security policies.

This report is specifically tailored to a scenario where a security analysis product authenticates to a Salesforce organization using the OAuth 2.0 Client Credentials Flow. Subsequent to authentication, this product utilizes a package.xml manifest file in conjunction with the Salesforce Metadata API to retrieve the org's structural and configuration blueprint for detailed security examination.1 The Metadata API is the Salesforce-designated programmatic interface for managing and transferring metadata components between different Salesforce environments or between an org and a local file system.1

The primary objective of this document is to furnish expert-level guidance on the meticulous construction of an optimal package.xml file. This guidance aims to ensure that all metadata components critical to security are retrieved, thereby facilitating a thorough, accurate, and comprehensive security assessment of the target Salesforce organization.

The selection of the OAuth 2.0 Client Credentials Flow for authentication by the security product carries significant implications. This flow is inherently designed for server-to-server integrations where no end-user is directly involved in the authentication process at runtime.3 Consequently, the identity and, more importantly, the permissions of the "Run As" user—configured within the ConnectedApp or ExternalClientApplication metadata component that facilitates this flow—become points of paramount security interest.5 The permissions assigned to this designated "Run As" user directly define the entire scope of data and metadata that the security product can access and analyze. Therefore, the security of the integration point itself, represented by the ConnectedApp or ExternalClientApplication and its associated RunAs user, forms a foundational aspect of the overall security assessment.

Furthermore, the very efficacy of any security analysis predicated on metadata is directly proportional to the completeness and accuracy of the metadata retrieved. If critical metadata types or specific components are inadvertently omitted from the package.xml manifest, the analysis will suffer from blind spots. These omissions can lead to an incomplete understanding of the org's security posture, potentially resulting in a false sense of security and the failure to identify significant vulnerabilities.

## **2\. Understanding package.xml for Security Audits**

The package.xml manifest file is the cornerstone of interacting with the Salesforce Metadata API for retrieve and deploy operations. A precise understanding of its structure and capabilities is essential for effective security analysis.

### **Core Structure of package.xml**

The manifest file is an XML document with a specific structure recognized by the Metadata API 7:

* **\<Package xmlns="http://soap.sforce.com/2006/04/metadata"\>**: This is the root element of the manifest file, declaring the namespace for Salesforce metadata.  
* **\<types\>**: This element acts as a container for specifying a group of metadata components that belong to the same metadata type. A single package.xml file can include multiple \<types\> elements, allowing for the definition of various categories of metadata to be retrieved or deployed in one operation.7  
* **\<members\>**: As a child element of \<types\>, \<members\> specifies which individual components of the metadata type (defined by the sibling \<name\> element) are to be included in the operation. This tag can contain an asterisk (\*) to denote all members of that type, or it can list specific component names (e.g., \<members\>Account\</members\>, \<members\>MyCustomProfile\</members\>, \<members\>MyCustomObject\_\_c\</members\>).7  
* **\<name\>**: Also a child element of \<types\>, \<name\> specifies the official API name of the Salesforce metadata type being targeted (e.g., \<name\>CustomObject\</name\>, \<name\>Profile\</name\>, \<name\>ApexClass\</name\>).7  
* **\<version\>**: This element specifies the API version number that the Metadata API should use when processing the manifest file (e.g., \<version\>61.0\</version\>). The choice of API version is critical, as the structure, availability, and behavior of metadata components can change significantly between Salesforce releases and their corresponding API versions.9 The version declared in the package.xml dictates how Salesforce interprets the metadata components listed and which attributes are available for retrieval.

### **Leveraging Wildcards (\*) Effectively and Its Limitations**

The asterisk (\*) wildcard, when used within the \<members\> tag, provides a convenient and powerful method for retrieving all components of a specified metadata type. For instance, using \<members\>\*\</members\> for the ApexClass type will retrieve all Apex classes in the org 8, and for CustomObject it will retrieve all custom objects.7

However, a crucial limitation exists: the wildcard character is **not universally supported** across all metadata types.11 Attempting to use \* for a metadata type that does not support it will not result in an error during the retrieve call; instead, it will simply lead to no components of that type being retrieved. This can create significant and easily overlooked gaps in a security analysis if not properly understood. For a security product aiming for comprehensive analysis, awareness of these limitations is paramount.

The following table lists common metadata types that do not support the wildcard character, necessitating explicit enumeration of their members in the package.xml file:

**Table 2.1: Metadata Types Not Supporting Wildcard (\*) in package.xml**

| Metadata Type API Name |
| :---- |
| ActionOverride |
| AnalyticSnapshot |
| CustomField |
| CustomLabel |
| Dashboard |
| Document |
| EmailTemplate |
| Folder |
| FolderShare |
| GlobalPicklistValue |
| Letterhead |
| ListView |
| Metadata |
| MetadataWithContent |
| NamedFilter |
| Package |
| PersonalJourneySettings |
| Picklist |
| ProfileActionOverride |
| RecordType |
| Report |
| SearchLayouts |
| SearchingSettings |
| SharingBaseRule |
| SharingReason |
| SharingRecalculation |
| StandardValueSet |
| Territory2Settings |
| ValidationRule |
| WebLink |

*11*

The non-support of wildcards for fundamental types such as CustomField, ValidationRule, and RecordType carries significant implications. For example, if a security analysis relies on reviewing all custom field definitions for sensitive data patterns or FLS configurations, a package.xml entry like \<types\>\<members\>\*\</members\>\<name\>CustomField\</name\>\</types\> would fail to retrieve any custom fields. Instead, each custom field would need to be explicitly listed (which is impractical) or, more commonly, retrieved as part of its parent CustomObject definition. This necessitates a more sophisticated strategy for manifest creation, potentially involving dynamic generation or the use of broader parent types that encapsulate these components.

### **Strategies for Comprehensive Metadata Retrieval**

Achieving a truly comprehensive metadata retrieval for security analysis requires strategic approaches:

1. **Manifest Generation Commands**: Salesforce CLI offers powerful commands such as sf project generate manifest \--from-org \<alias\> (or its older counterpart sfdx force:source:manifest:create).12 These commands can inspect the target Salesforce org and generate a package.xml file that explicitly lists all metadata components currently present in that org. This method provides an excellent starting point for ensuring that no existing components are missed.  
2. **Handling Retrieval Limits by Splitting package.xml Files**: The Salesforce Metadata API imposes limits on the number of files or the total size of components that can be retrieved in a single API call (often cited around 10,000 files or a specific ZIP file size limit).12 For large and complex Salesforce organizations, a manifest generated to include all metadata can easily exceed these limits. The recommended strategy in such cases is to divide the comprehensive package.xml into multiple, smaller manifest files. Each smaller file targets a subset of the total metadata, allowing the retrieval to be performed in manageable chunks. Examples of this approach, such as "1-of-2" and "2-of-2" files, are available in community resources like the GitHub repository mentioned in 14 and.14  
3. **Iterative Refinement and Prioritization**: While an initial full metadata pull establishes a comprehensive baseline, subsequent security analyses might focus on specific high-risk areas, recently modified components, or newly introduced metadata types. The initial comprehensive manifest can serve as a master list from which smaller, more targeted package.xml files are derived for such focused audits.

The API version declared within the package.xml (e.g., \<version\>61.0\</version\>) is not merely a formality; it critically influences how the Salesforce platform interprets the metadata structure and determines which features and attributes are recognized during the retrieval process.9 Salesforce introduces new features, metadata types, and attributes with each seasonal release, tied to new API versions.9 For instance, a security-relevant attribute within the Flow metadata type, such as runInMode (which dictates the flow's execution context regarding user permissions), might only be available and correctly interpreted by the Metadata API from a specific version onwards.10 If an older API version (e.g., \<version\>47.0\</version\>) is specified in the package.xml, this newer attribute might not be retrieved, or its value could be defaulted or ignored by the system. This would lead to an inaccurate or incomplete security assessment of the Flow's operational context. Therefore, it is imperative that the security analysis product utilizes the latest feasible API version that is supported by both the product itself and the target Salesforce organizations to ensure all relevant security settings and metadata structures are accurately captured.

## **3\. Critical Metadata Types for Security Analysis via Client Credentials Flow**

A robust security analysis of a Salesforce org requires the examination of a wide array of metadata components. The following sections detail the most critical metadata types, categorized by their primary security domain. For each type, its significance to security, key attributes or elements for analysis, and the corresponding package.xml specification are provided.

### **3.1. Authentication and Authorization Mechanisms**

These components govern how users and systems authenticate to Salesforce and what they are authorized to access at a high level. Given the context of authentication via Client Credentials Flow, these are particularly important.

* **ConnectedApp**  
  * **Security Importance**: ConnectedApp components are central to managing OAuth-based integrations, including the Client Credentials Flow. They define the identity of the external application, its permitted OAuth scopes (which translate to permissions), and various security policies such as IP restrictions, session management, and, critically for this flow, the designated "Run As" user. Misconfigurations in a ConnectedApp can create significant vulnerabilities, potentially leading to unauthorized access or granting overly broad permissions to the integrated application.5  
  * **Key Attributes/Elements for Analysis**:  
    * oauthConfig \> isClientCredentialFlowEnabled (or similar boolean flag indicating flow enablement): Must be true for the Client Credentials Flow to be active for this application..5  
    * oauthConfig \> clientCredentialsFlowExecutionUser: The username of the Salesforce user whose context the Client Credentials Flow operates under. This is a **critical security checkpoint**. The permissions granted to this user directly dictate the effective permissions of the connected application when using this flow.5  
    * oauthConfig \> scopes \> scope: A list of OAuth scopes granted to the application (e.g., api, full, refresh\_token, openid, custom\_permissions). The principle of least privilege must be rigorously applied; overly broad scopes like full are a significant concern.5  
    * oauthPolicy \> ipRelaxation: Controls IP range restrictions for token issuance. Values typically include ENFORCE\_IP\_RESTRICTIONS or options to bypass them.  
    * oauthPolicy \> refreshTokenPolicy: Defines the policy for issuing and managing refresh tokens, including their expiration.  
    * sessionPolicy \> sessionTimeout: Specifies the duration of sessions initiated by this application.  
    * sessionPolicy \> blockSessionsUntilAuthentication: If true, may require re-authentication under certain conditions, enhancing security.  
    * oauthConfig \> consumerKey: The client ID of the application (retrieved in metadata).  
    * The consumerSecret is not directly retrieved in the metadata XML for security reasons. However, its existence and the org's policies for its management and rotation are vital.5  
  * **package.xml Specification**:  
    XML  
    \<types\>  
        \<members\>\*\</members\> \<name\>ConnectedApp\</name\>  
    \</types\>

  * The security configuration of a ConnectedApp using the Client Credentials Flow is deeply intertwined with the permissions of its clientCredentialsFlowExecutionUser. A comprehensive security analysis must not only scrutinize the OAuth scopes defined in the ConnectedApp but also meticulously audit the Profile and any PermissionSet assignments of this specific execution user. For example, if the ConnectedApp has the api scope, and its clientCredentialsFlowExecutionUser is assigned the System Administrator profile, the application effectively gains system-level privileges. This linkage makes the RunAs user's permissions a primary target for detailed review.  
  * Furthermore, the ConnectedApp metadata itself can represent an attack vector. If an adversary with sufficient deployment privileges (e.g., through compromised CI/CD pipeline credentials or an administrator's compromised session) can modify a ConnectedApp's metadata, they could potentially alter the clientCredentialsFlowExecutionUser to a more privileged account, relax IP restrictions, or broaden OAuth scopes. This would allow them to escalate the privileges of an already integrated (or a newly malicious) application. Consequently, auditing changes to ConnectedApp metadata components is an important aspect of maintaining org security.  
* **ExternalClientApplication (ECA)**  
  * **Security Importance**: ECAs offer a more recent and structured mechanism for managing server-to-server integrations, including those utilizing OAuth flows like Client Credentials. They are designed to provide a more granular and potentially more secure way to define headless integrations compared to traditional ConnectedApp components.16  
  * **Key Attributes/Elements for Analysis**:  
    * distributionState: Indicates whether the ECA is Local to the current org or Packaged for distribution to other orgs.17  
    * isProtected: A boolean flag indicating if the ECA component is protected, which affects its modifiability in subscriber orgs if it's part of a managed package.17  
    * Associated ECA OAuth Plugin Settings (typically found in ExtlClntAppOauthSettings metadata type linked to the ECA):  
      * oauthConfig \> scopes: Similar to ConnectedApp scopes, defining the permissions granted to the ECA.16  
      * oauthConfig \> isClientCredentialsFlowEnabled (or equivalent boolean): Must be enabled for the Client Credentials Flow.  
      * oauthConfig \> runAsUser: The username of the Salesforce user under whose context the flow operates, analogous to clientCredentialsFlowExecutionUser in ConnectedApp.  
    * ExtlClntAppGlobalOauthSettings: May be used for advanced or shared OAuth configurations across multiple ECAs.16  
  * **package.xml Specification**:  
    XML  
    \<types\>  
        \<members\>\*\</members\> \<name\>ExternalClientApplication\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\> \<name\>ExtlClntAppOauthSettings\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>ExtlClntAppGlobalOauthSettings\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>ExtlClntAppOauthConfigurablePolicies\</name\>  
    \</types\>

  .16

  * ECAs signify an evolution in Salesforce's approach to managing headless integrations, offering a more complex but potentially more granular and secure framework than traditional ConnectedApps. Their multi-component nature—comprising the ECA definition itself, OAuth settings files, and policy files—necessitates that a security product can accurately trace configurations across these interrelated metadata types.17 A holistic security assessment requires connecting these disparate pieces to form a complete understanding of an ECA's security configuration.  
  * Consistent with ConnectedApps, the runAsUser configured for an ECA directly dictates its operational privileges within Salesforce. The security of the ECA is therefore critically dependent on the permissions granted to this user via their Profile and any assigned PermissionSets. An audit of these user-level permissions is essential.  
* **AuthProvider**  
  * **Security Importance**: AuthProvider components define configurations for third-party authentication providers. These are used when Salesforce acts as a relying party for Single Sign-On (SSO) (e.g., users logging into Salesforce with their Google or corporate SAML credentials) or when Salesforce itself needs to authenticate to other systems using OAuth (often in conjunction with NamedCredentials). Misconfiguration of an AuthProvider can lead to authentication bypasses, exposure of sensitive information, or denial of service for login functionalities.20  
  * **Key Attributes/Elements for Analysis**:  
    * providerType: Specifies the type of authentication provider, such as Salesforce, Facebook, Google, OpenIdConnect, or Custom. A Custom provider type indicates that an Apex class implements the authentication logic.20  
    * consumerKey and consumerSecret: These are the Client ID and Client Secret, respectively, obtained from the external identity provider. While the consumerSecret itself is typically not retrieved directly in the metadata for security reasons 22, the configuration method and the fact that these credentials are managed here are important.  
    * authorizeUrl, tokenUrl, userInfoUrl: These are the critical endpoint URLs for OAuth or OpenID Connect based providers. Their accuracy, use of HTTPS, and the trustworthiness of the domains are vital for secure operation.23  
    * executionUser: The username of the Salesforce user under whose context the registration handler Apex class runs, if one is specified.  
    * registrationHandler: The API name of an Apex class that handles user provisioning or linking when a user logs in via this AuthProvider for the first time. The security of this Apex class is paramount.  
    * customMetadataType: If the AuthProvider's configuration values are stored in records of a Custom Metadata Type, this field will reference it.20  
    * usePkce: A boolean indicating if the Proof Key for Code Exchange (PKCE) extension for OAuth is enabled, which enhances security for public clients.23  
  * **package.xml Specification**:  
    XML  
    \<types\>  
        \<members\>\*\</members\> \<name\>AuthProvider\</name\>  
    \</types\>

  * Custom AuthProvider configurations that involve Apex code—either through a Custom provider type utilizing an AuthProviderPluginClass 20 or via a registrationHandler Apex class 23—introduce a significant potential attack surface. If the designated Apex class contains vulnerabilities (e.g., insecurely creating user records, assigning default high-privilege profiles, or improperly handling errors), the entire authentication mechanism facilitated by that AuthProvider can be compromised. Thus, any AuthProvider metadata that references an Apex class must trigger a detailed security review of that corresponding ApexClass metadata.  
  * The overall security of AuthProvider configurations also heavily depends on the integrity and security of the external endpoints specified (e.g., authorizeUrl, tokenUrl). These URLs must use HTTPS and point to legitimate, trusted identity provider services. Any deviation could expose sensitive authentication data or redirect users to malicious sites.  
* **NamedCredential & ExternalCredential**  
  * **Security Importance**: These components represent Salesforce's recommended best practice for securely defining and managing callout endpoints and their associated authentication parameters. They abstract sensitive information like API keys, usernames, passwords, or OAuth tokens away from Apex code and declarative automation tools, significantly enhancing the security of integrations with external services.24  
  * **Key Attributes/Elements for Analysis** 24:  
    * **NamedCredential**:  
      * endpoint: The base URL of the external service. This must be an HTTPS URL for secure communication.  
      * principalType: Determines the authentication context. Common values include Anonymous (no authentication from Salesforce's side, relies on endpoint), PerUser (requires each user to authenticate individually to the external system), or NamedUser (uses a specific, pre-defined user's credentials for the external system).  
      * protocol: Specifies the authentication mechanism, such as Password (for basic authentication), OAuth 2.0 (for OAuth flows), JWT (for JWT Bearer Token Flow), or JWTExchange.  
      * authProvider: If the protocol is OAuth 2.0, this field links to an AuthProvider component that manages the OAuth token flow.  
      * externalCredential: In the newer model, this field links to an ExternalCredential component that handles the detailed authentication parameters.  
    * **ExternalCredential**:  
      * authenticationProtocol: Defines the protocol used (e.g., OAuth, Custom, AwsSigV4).  
      * oauthScope: If authenticationProtocol is OAuth, this specifies the space-separated string of OAuth scopes required for the external service.  
      * principalType: (PerUser, NamedPrincipal). Defines how principals (identities) are managed for this credential.  
      * parameters: A list of name-value pairs used for authentication. For instance, for a Custom protocol, this might include an API key name (though the sensitive value itself is typically stored securely elsewhere if using private external credentials, not directly in this metadata).  
  * **package.xml Specification** 24:  
    XML  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>NamedCredential\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>ExternalCredential\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\> \<name\>PermissionSet\</name\>  
    \</types\>  
    (If a NamedCredential's protocol is OAuth and it references an authProvider, ensure that the AuthProvider metadata type is also retrieved.)  
  * Salesforce has been evolving its model for handling callout authentication. The introduction of the ExternalCredential type, designed to work in conjunction with NamedCredential, represents a shift towards more granular, flexible, and secure management of these parameters.24 This newer pattern separates the definition of the endpoint (primarily in NamedCredential) from the specifics of the authentication mechanism (handled by ExternalCredential). Security analysis should identify the usage of this modern pattern and flag any continued reliance on older, less secure methods of storing credentials for callouts (e.g., in Custom Settings, Custom Labels, or hardcoded in Apex).  
  * Access to utilize a NamedCredential, particularly one that employs a NamedPrincipal or a PerUser ExternalCredential, is often governed by PermissionSets. These PermissionSets explicitly grant users or profiles the ability to use specific ExternalCredentialPrincipal configurations.24 Therefore, retrieving and analyzing these related PermissionSets is crucial for understanding which users or automated processes are authorized to initiate callouts via these secure integration points.  
* **Certificate**  
  * **Security Importance**: Digital certificates play a vital role in various Salesforce security functions. They are used to establish trust for SAML Single Sign-On, authenticate the Salesforce org to external systems (e.g., when using the JWT Bearer Flow for OAuth, or for mutual TLS authentication), and digitally sign outbound messages to ensure their integrity and authenticity. Compromised, expired, or improperly configured certificates can lead to disruptions in service, authentication failures, or severe security breaches.25  
  * **Key Attributes/Elements for Analysis**:  
    * masterLabel, developerName: User-friendly and API names for the certificate.  
    * keySize: The bit length of the certificate's cryptographic key (e.g., 2048, 4096). Larger key sizes generally offer stronger security.  
    * expirationDate: The date on which the certificate will expire. Monitoring this is critical to prevent service interruptions. (While the actual date value might not be directly in the retrieved .crt file itself, its metadata representation should be checked, or this information should be cross-referenced from the Salesforce UI).  
    * caSigned: A boolean value indicating whether the certificate is signed by a trusted Certificate Authority (true) or is self-signed (false). Self-signed certificates are generally not recommended for production integrations with external parties.  
    * The actual certificate content (public key, and the private key for certificates generated by Salesforce) is not fully exposed in a human-readable way in the metadata XML but is part of the component.  
  * **package.xml Specification**:  
    XML  
    \<types\>  
        \<members\>\*\</members\> \<name\>Certificate\</name\>  
    \</types\>  
    (26 and 30 also list PublicKeyCertificate as a metadata type, which seems to be related or a newer type specifically for managing public keys. The Metadata Coverage Report 30 should be consulted for the exact type name based on API version).  
  * While the private key of a certificate is a highly sensitive asset and is not directly exposed or exportable through metadata for security reasons, the metadata *about* the certificate (its name, issuer, expiry, type) provides crucial information for a security audit.25 For instance, if a ConnectedApp is configured for the JWT Bearer Flow, it will reference a specific certificate. If that certificate is nearing its expiration date, the authentication flow will eventually fail. Similarly, if an organization uses SAML for SSO, a certificate is integral to the trust relationship. Knowing which certificates exist, their properties, and where they are used helps in mapping out dependencies, identifying potential points of failure, and assessing the strength of the cryptographic configurations.

### **3.2. User Access Controls and Permissions**

These metadata components form the core of Salesforce's security model, defining what individual users can see and do within the platform.

* **Profile**  
  * **Security Importance**: Profiles are a foundational element of user access control in Salesforce. They define a user's baseline permissions, including object-level permissions (Create, Read, Update, Delete \- CRUD), field-level security (FLS), access to Apex classes and Visualforce pages, broad system permissions, login hours restrictions, and allowed login IP ranges. Overly permissive profiles are a common and significant security risk.7  
  * **Key Attributes/Elements for Analysis**:  
    * \<objectPermissions\>: Contains sub-elements like allowCreate, allowRead, allowEdit, allowDelete, viewAllRecords, and modifyAllRecords for each object.  
    * \<fieldPermissions\>: Defines field-level security with sub-elements editable and readable for specific fields.33  
    * \<classAccesses\>: Specifies which ApexClass components the profile can execute, with enabled flag.  
    * \<pageAccesses\>: Specifies which ApexPage (Visualforce pages) the profile can access, with enabled flag.  
    * \<userPermissions\>: A collection of boolean flags for numerous system-level permissions, such as ModifyAllData, ViewAllData, CustomizeApplication, APIAEnabled, ManageUsers, PasswordNeverExpires.37  
    * \<loginIpRanges\>: Defines allowed IP address ranges from which users with this profile can log in.36  
    * \<loginHours\>: Specifies the hours during which users with this profile can log in.  
    * \<sessionSettings\>: Profile-specific session settings like sessionTimeout or forceRelogin, which can override org-wide defaults if more restrictive.  
  * **package.xml Specification**:  
    XML  
    \<types\>  
        \<members\>\*\</members\> \<name\>Profile\</name\>  
    \</types\>

  .31

  * Profiles granting powerful system permissions like ModifyAllData or ViewAllData effectively bypass most other record-level sharing mechanisms (OWDs, Sharing Rules) for users assigned to them. The permissions of the clientCredentialsFlowExecutionUser's profile are therefore of paramount importance to the security of the integration. If this user has ModifyAllData, the integrated application essentially has carte blanche to manipulate data across the org (for objects to which the profile has CRUD access). This makes Profile metadata, particularly for integration users and administrative users, a top-priority item for security review.  
  * Salesforce has been actively promoting a shift towards using Permission Sets and Permission Set Groups for more granular and flexible access control, reserving Profiles for defining only the most basic, foundational access (e.g., default record types, page layouts, app visibility).32 A security analysis should identify profiles that still carry excessive or highly specific permissions that could be more appropriately managed via Permission Sets, thereby adhering better to the principle of least privilege.  
* **PermissionSet & PermissionSetGroup**  
  * **Security Importance**: PermissionSets provide a flexible and additive way to grant users specific permissions beyond what their assigned Profile allows. PermissionSetGroups allow for the bundling of multiple PermissionSets, simplifying permission management for users with similar job roles or responsibilities. They can also include "muting" functionality to revoke specific permissions that might be granted by one of the included sets. These components are essential for implementing the principle of least privilege and for creating a scalable and maintainable security model.24  
  * **Key Attributes/Elements for Analysis** (Similar structure to Profile for specific permissions):  
    * objectPermissions, fieldPermissions, classAccesses, pageAccesses, userPermissions.  
    * tabSettings: Defines visibility for custom and standard tabs.  
    * applicationVisibilities: Controls access to specific applications.  
    * externalCredentialPrincipalAccesses: Grants access to use specific ExternalCredentialPrincipal configurations, crucial for callout security.24  
    * For PermissionSetGroup:  
      * permissionSets: A list of API names of the PermissionSets included in the group.  
      * mutingPermissionSets: A list of API names of PermissionSets used to mute (revoke) specific permissions that might be granted by the permissionSets included in the group.42  
      * status: (Active or Draft).  
  * **package.xml Specification**:  
    XML  
    \<types\>  
        \<members\>\*\</members\> \<name\>PermissionSet\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\> \<name\>PermissionSetGroup\</name\>  
    \</types\>  
    (31 provides an example package-permissionset.xml. Note that MutingPermissionSet is conceptually a PermissionSet used in a muting context within a PermissionSetGroup 44).  
  * The Salesforce DX source format offers the capability (currently in beta) to decompose PermissionSet metadata into smaller, more manageable files.41 This addresses the challenge of reviewing and version-controlling large, monolithic PermissionSet XML files. While package.xml retrieves the traditional monolithic version, awareness of this trend towards decomposition is valuable, as it indicates Salesforce's direction for improving the manageability of complex permission structures.  
  * The muting feature within PermissionSetGroups 44 introduces a layer of complexity in permission analysis. Determining a user's net effective permissions requires understanding not only the "positive" permissions granted by all included sets in a group but also accurately subtracting any permissions that are "muted" by designated muting permission sets. This calculation can be intricate but is essential for an accurate security assessment.  
* **Role**  
  * **Security Importance**: Role components define the role hierarchy within a Salesforce org. This hierarchy is a key factor in record access visibility, as it typically allows users in higher roles to view and edit records owned by users in roles below them. Roles are also frequently used as criteria in Sharing Rules to grant broader record access.2  
  * **Key Attributes/Elements for Analysis**:  
    * name: The API name of the role.  
    * parentRole: The API name of the parent role in the hierarchy, which defines the reporting structure.  
    * opportunityAccessForAccountOwner, caseAccessLevel, contactAccessLevel: These settings define default access levels to related child records (Opportunities, Cases, Contacts) for the owners of parent Account records, based on the role hierarchy.  
  * **package.xml Specification**:  
    XML  
    \<types\>  
        \<members\>\*\</members\> \<name\>Role\</name\>  
    \</types\>

  .31

  * The structure of the role hierarchy itself—whether it's very flat (few levels) or excessively deep and complex—can have significant implications for both sharing performance and the overall manageability of the security model. While the metadata explicitly defines the structure, the security analysis tool should infer potential issues. For example, an overly flat hierarchy might not accurately reflect the organization's true access segmentation needs, potentially leading to an over-reliance on other sharing mechanisms or overly broad access at lower levels. Conversely, a very deep hierarchy can complicate sharing rule calculations.  
* **Group**  
  * **Security Importance**: Group components, particularly Public Groups, represent collections of users, roles, roles and subordinates, or even other groups. They are primarily used as the "Share To" entity in Sharing Rules to grant record access to a defined set of members. They can also be used in manual sharing and other access control features.31  
  * **Key Attributes/Elements for Analysis**:  
    * name: The API name of the group.  
    * type: Indicates the type of group, such as Regular (manually managed members), Role, RoleAndSubordinates, Territory, TerritoryAndSubordinates.  
    * doesIncludeBosses: A boolean that, if true for certain group types, implicitly includes the managers of the group members in the group's effective membership.  
    * The actual members of a Regular group are not part of this metadata type but are managed as data. However, for role-based groups, the definition is implicit.  
  * **package.xml Specification**:  
    XML  
    \<types\>  
        \<members\>\*\</members\> \<name\>Group\</name\>  
    \</types\>

  .31

  * Groups that are defined with very broad constituencies, such as an "All Internal Users" group (if one exists and is populated dynamically or includes all roles), can significantly impact data security when used in sharing rules. If such a group is used to grant access in a sharing rule, it can effectively flatten the security model for the affected records, potentially granting wider access than intended by the OWD settings. The definition and membership criteria of groups used in sharing rules are therefore critical points of analysis.

### **3.3. Data Security and Visibility**

These metadata components are fundamental to controlling who can access which records and specific fields within those records.

* **CustomObject** (This type is used for both custom objects and for retrieving settings of standard objects)  
  * **Security Importance**: The metadata for a CustomObject (or a standard object specified by name) defines critical object-level settings. Most importantly for data security, it includes the Organization-Wide Default (OWD) sharing setting, which establishes the baseline record access level for all records of that object type. It also contains information about field history tracking.7  
  * **Key Attributes/Elements for Analysis**:  
    * \<sharingModel\>: This tag defines the OWD for the object. Common values include Private, PublicRead, PublicReadOnly (equivalent to PublicRead), PublicReadWrite, PublicFullAccess, ReadWriteTransfer (for Leads/Cases), and ControlledByParent (for detail objects in master-detail relationships or specific standard objects like Contact).49 This is a cornerstone of record-level security.  
    * \<externalSharingModel\>: Defines the OWD for the object specifically for external users (e.g., Experience Cloud users).  
    * \<enableHistory\>: A boolean indicating if field history tracking is enabled for the object overall.49 Individual fields also have a trackHistory attribute.  
    * \<fields\> (sub-elements): While Field-Level Security (FLS) is primarily defined in Profile and PermissionSet metadata, the field definitions within the CustomObject metadata indicate attributes like trackHistory (for individual field history tracking) and data type, which are relevant for understanding data sensitivity.  
    * \<historyRetentionPolicy\>: A sub-element defining the Field Audit Trail policy for the object, including archiveAfterMonths and archiveRetentionYears.49  
  * **package.xml Specification**:  
    XML  
    \<types\>  
        \<members\>\*\</members\> \<members\>Account\</members\> \<members\>Contact\</members\>  
        \<members\>Lead\</members\>  
        \<members\>Opportunity\</members\>  
        \<members\>Case\</members\>  
        \<name\>CustomObject\</name\>  
    \</types\>

  .7

  * Organization-Wide Defaults (OWDs), represented by the \<sharingModel\> tag, are the absolute foundation of Salesforce's record-level security model.51 OWDs establish the most restrictive level of access to records of a particular object type. All other record-sharing mechanisms—such as the role hierarchy, sharing rules, teams, and manual sharing—can only *open up* access from this baseline; they generally cannot restrict access further than what the OWD permits (with some exceptions for guest user specific settings). Therefore, if the OWD for a sensitive object is set to a permissive level like PublicReadWrite 51, it creates a significant security challenge, as it means all internal users can, by default, view and edit all records of that object type. Identifying and flagging such permissive OWDs is a critical function of a security analysis.  
  * The \<historyRetentionPolicy\> sub-element within the CustomObject metadata 49 is also crucial from a compliance and auditing perspective. It defines the retention policies for Field Audit Trail, which allows for tracking changes to field values over much longer periods than standard field history. Inadequate configuration of this policy can hinder forensic investigations into security incidents or lead to non-compliance with data retention regulations.  
* **Field-Level Security (FLS)**  
  * **Security Importance**: FLS is not a distinct metadata type for retrieval but is a critical security setting defined within Profile and PermissionSet metadata. It controls whether specific fields on an object are visible (readable) or modifiable (editable) for users assigned that profile or permission set. FLS is essential for protecting sensitive data at a granular level.  
  * **Key Attributes/Elements for Analysis**: Found within Profile and PermissionSet XML:  
    * \<fieldPermissions\>: This parent tag contains individual field permission entries.  
      * \<field\>: The API name of the field (e.g., Account.AnnualRevenue, MyCustomObject\_\_c.SSN\_\_c).  
      * \<editable\>: Boolean (true or false).  
      * \<readable\>: Boolean (true or false). .33  
  * **package.xml Specification**: FLS settings are retrieved as part of Profile and PermissionSet metadata. To perform a comprehensive FLS analysis, the definitions of the objects and fields themselves (CustomObject type) are also needed to understand the context (e.g., field data type, sensitivity).  
    XML  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>Profile\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>PermissionSet\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\> \<members\>Account\</members\> \<name\>CustomObject\</name\>  
    \</types\>

  * Effective FLS ensures that users only see and interact with the data fields necessary for their job functions. For example, a sensitive field like a Social Security Number should have readable and editable set to false for most profiles, with access granted only through specific, justified Permission Sets.  
* **SharingRules** (This is an umbrella term; specific types include SharingOwnerRule, SharingCriteriaRule, SharingTerritoryRule, SharingGuestRule)  
  * **Security Importance**: Sharing rules are used to grant broader access to records than what is permitted by the Organization-Wide Defaults (OWDs). They can extend access based on record ownership, criteria defined on record fields, territory membership, or for guest users accessing Experience Cloud sites. While essential for collaborative work, poorly designed or overly permissive sharing rules can lead to unintended data exposure.7  
  * **Key Attributes/Elements for Analysis**:  
    * For SharingOwnerRule:  
      * sharedTo: Specifies who receives access (e.g., roles, public groups, territories).  
      * sharedFrom: Specifies the owners whose records are being shared (e.g., roles, public groups).  
      * accessLevel: The level of access granted (e.g., Read, Edit).  
    * For SharingCriteriaRule:  
      * criteriaItems: Defines the conditions (field, operation, value) that records must meet to be shared.54  
      * sharedTo: Specifies who receives access.  
      * accessLevel: The level of access granted.  
    * SharingGuestRule is particularly sensitive as it controls data access for unauthenticated users on public sites or portals.  
  * **package.xml Specification**:  
    XML  
    \<types\>  
        \<members\>Account.\*\</members\> \<name\>SharingRules\</name\> \</types\>  
    Alternatively, more granular types can be specified if needed, though SharingRules for an object is common:  
    XML  
    \<types\>  
        \<members\>Account.MyCriteriaRule\</members\>  
        \<name\>SharingCriteriaRule\</name\>  
    \</types\>  
    \<types\>  
        \<members\>Case.CaseOwnerGroupRule\</members\>  
        \<name\>SharingOwnerRule\</name\>  
    \</types\>

  .41

  * An excessive number of sharing rules, particularly complex criteria-based rules, on frequently transacted objects can lead to performance degradation, sometimes referred to as "sharing recalculation storms." While the metadata defines the rules, a security analysis tool could infer potential performance issues or excessive complexity if the volume of rules for an object is very high.  
  * SharingGuestRule components demand particularly close scrutiny. Misconfigurations in guest user sharing rules are a common cause of data exposure to unauthenticated users on Experience Cloud sites or older Salesforce Sites, making them a high-priority area for security audits.  
* **SharingSet**  
  * **Security Importance**: SharingSet components are specifically designed for Experience Cloud sites (formerly Communities or Portals). They grant high-volume external site users access to records (e.g., Cases, Custom Objects) based on their relationship (via lookup fields) to an Account or Contact record they are associated with. These are critical for controlling data visibility for external users, especially in B2C or partner portal scenarios.59  
  * **Key Attributes/Elements for Analysis**:  
    * profiles: A list of user profiles to which this sharing set applies. Typically, these are external user profiles (e.g., Customer Community User, Partner Community User).  
    * accessMappings: Defines how objects are shared. Key sub-elements include:  
      * object: The API name of the object whose records are being shared.  
      * accessLevel: The level of access granted (e.g., Read, Edit).  
      * userField: A lookup field on the User object (e.g., ContactId) that links the site user to a Contact or Account.  
      * accountField (or contactField): A lookup field on the target object that links it back to the Account or Contact identified by userField.  
    * objectMappings: An alternative way to define mappings for specific objects.  
  * **package.xml Specification**:  
    XML  
    \<types\>  
        \<members\>\*\</members\> \<name\>SharingSet\</name\>  
    \</types\>

  * SharingSets are a powerful tool for enabling necessary data access for external users but can inadvertently grant broad access if the lookup relationships and profile assignments are not carefully configured. For example, if a SharingSet grants access to all Cases related to a user's Account, and that Account has thousands of Cases, the external user gains access to all of them. The precision of the accessMappings is key to ensuring that external users only see the records relevant to them.  
* **FieldRestrictionRule**  
  * **Security Importance**: FieldRestrictionRule is a newer metadata type that allows for the definition of rules to restrict access to the data within specific fields based on criteria, even if Field-Level Security (FLS) would otherwise grant visibility or editability. This provides a more dynamic and granular level of data control beyond static FLS settings.30  
  * **Key Attributes/Elements for Analysis**:  
    * active: Boolean, indicates if the rule is currently active.  
    * masterLabel: A descriptive label for the rule.  
    * userCriteria and recordFilter: Define the conditions based on user attributes or record data that determine when the restriction applies.  
    * targetEntity: The API name of the sObject to which the rule applies.  
    * targetFields: A list of API names of the fields on the targetEntity whose values are to be restricted or masked.  
    * restrictionValue (or similar, depending on API version): Defines how the field value is treated (e.g., masked, completely hidden).  
  * **package.xml Specification**:  
    XML  
    \<types\>  
        \<members\>\*\</members\> \<name\>FieldRestrictionRule\</name\>  
    \</types\>

  * Field Restriction Rules introduce a dynamic layer to data security. Unlike FLS, which is generally static for a given user's profile and permission sets, these rules can change field accessibility based on the context of the specific record being viewed or the user viewing it. This is a powerful capability for data protection but also adds complexity to determining a user's net effective access to field data. Security analysis must incorporate these rules to get an accurate picture of data visibility.

### **3.4. Org-Wide Security Configurations**

These settings apply globally to the Salesforce organization and establish foundational security parameters.

* **SecuritySettings**  
  * **Security Importance**: This metadata type acts as a container for a wide range of organization-wide security settings. It includes critical configurations such as password policies, session settings, network access controls (org-wide trusted IP ranges), and various other security preferences that impact the entire org.7  
  * **Key Attributes/Elements for Analysis**:  
    * passwordPolicies: Contains sub-elements like minPasswordLength, passwordComplexity (e.g., UpperLowerCaseNumericSpecialCharacters), passwordHistory (number of previous passwords remembered), passwordExpiration (e.g., NinetyDays), lockoutInterval (duration of lockout after failed attempts), maxLoginAttempts.69  
    * sessionSettings: Includes attributes like sessionTimeout (e.g., TwoHours), disableTimeoutWarning, enableCSRFOnGet, enableCSRFOnPost, requireHttpOnly (for session cookies), clickjackProtectionLevel (for various contexts like NonSetup, Setup, Visualforce).64  
    * networkAccess: Defines org-wide trusted IP ranges. This is often a sub-element within SecuritySettings but can also be managed via the distinct NetworkAccess metadata type.36  
    * sharingSettings \> enableExternalSharingModel: A boolean indicating if the external sharing model (separate OWDs for external users) is enabled.  
    * Other settings such as enableExtendedBrowserCaching, enableRequireHttpsConnection (ensures all connections are HTTPS).  
  * **package.xml Specification**:  
    XML  
    \<types\>  
        \<members\>Security\</members\> \<name\>SecuritySettings\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>NetworkAccess\</name\>  
    \</types\>  
    (7 lists "Security Settings" as a sample, implying the Settings type with a member Security. However, 30 specifically refer to SecuritySettings as the metadata type name. The Metadata Coverage Report 30 should be the definitive source for the correct type name based on API version).  
  * Weak passwordPolicies (e.g., allowing short passwords, no complexity requirements, infrequent expiry, or minimal history) directly elevate the risk of credential compromise through brute-force attacks or password reuse. Similarly, lax sessionSettings (e.g., excessively long timeout durations, disabling CSRF protection, or not enforcing HttpOnly for session cookies) increase the window of opportunity for attackers to hijack active sessions or exploit web vulnerabilities.64  
  * It's important to note that while SecuritySettings define org-wide defaults for session policies, Profile metadata can contain ProfileSessionSetting overrides. Typically, the more restrictive setting (either org-wide or profile-specific) will apply to the user. A comprehensive session security analysis must therefore consider both.  
* **RemoteSiteSetting**  
  * **Security Importance**: RemoteSiteSetting components define a whitelist of external domains (URLs) that Salesforce is permitted to make requests to. This includes callouts from Apex, requests made by Visualforce pages, or Lightning components. Properly configured Remote Site Settings are crucial for preventing Server-Side Request Forgery (SSRF)-like vulnerabilities, where Apex code might be tricked into making requests to unintended or malicious internal or external endpoints.71  
  * **Key Attributes/Elements for Analysis**:  
    * url: The specific URL or domain pattern that is whitelisted (e.g., https://api.example.com).  
    * isActive: A boolean indicating if the remote site setting is currently active and enforced.  
    * disableProtocolSecurity: A critical boolean flag. If set to true, it allows Salesforce to make callouts to the specified URL using HTTP instead of HTTPS. This is a significant security risk as it permits unencrypted communication and should almost always be false.72  
  * **package.xml Specification**:  
    XML  
    \<types\>  
        \<members\>\*\</members\> \<name\>RemoteSiteSetting\</name\>  
    \</types\>

  .72

  * Overly permissive RemoteSiteSetting URLs, such as those using wildcards excessively (e.g., https://\*.com or http://\*), can largely negate their security benefit by allowing connections to a vast range of unintended endpoints. Each Remote Site Setting entry should be as specific as possible to the required external service. The disableProtocolSecurity flag being true is a major red flag, as it allows sensitive data to be transmitted unencrypted over HTTP.  
* **CspTrustedSite**  
  * **Security Importance**: CspTrustedSite components define trusted sites for Content Security Policy (CSP) directives. CSP is a powerful browser security mechanism that helps prevent Cross-Site Scripting (XSS), clickjacking, and other code injection attacks by specifying which external domains are allowed sources for various types of web content (scripts, styles, images, fonts, frames, etc.) loaded by Visualforce pages and Lightning components.72  
  * **Key Attributes/Elements for Analysis**:  
    * endpointUrl: The URL of the trusted site or domain.72  
    * isActive: A boolean indicating if the trusted site definition is currently active.72  
    * context: Defines the scope where this CSP trusted site applies (e.g., All, VisualForce, LightningExperience, ExperienceBuilderSites).72  
    * CSP Directives: A series of boolean flags like isApplicableToConnectSrc (for connect-src), isApplicableToFrameSrc (for frame-src), isApplicableToImgSrc (for img-src), isApplicableToScriptSrc (for script-src), isApplicableToStyleSrc (for style-src), etc. These specify which CSP directives the endpointUrl applies to.72  
  * **package.xml Specification**:  
    XML  
    \<types\>  
        \<members\>\*\</members\> \<name\>CspTrustedSite\</name\>  
    \</types\>

  .72

  * CSP Trusted Sites are particularly crucial for securing Experience Cloud sites and any Visualforce or Lightning pages that might embed or interact with external content or scripts. A missing or overly permissive CSP (e.g., trusting overly broad domains like \* for script sources) can severely weaken the security of these pages, making them vulnerable to XSS attacks if any of the trusted sources are compromised or serve malicious content. The context field 72 is vital for correctly scoping where each CSP rule applies, ensuring that trust is granted only where necessary.

### **3.5. Custom Code and Automation Security**

Customizations, while powerful, can introduce significant security vulnerabilities if not developed and configured with security best practices in mind.

* **ApexClass & ApexTrigger**  
  * **Security Importance**: Apex is Salesforce's proprietary programming language, used to implement custom business logic, complex validation rules, and integrations. Apex code can perform Data Manipulation Language (DML) operations, make callouts to external services, and programmatically enforce (or bypass) sharing rules. Vulnerabilities in Apex code can lead to data breaches, unauthorized data modification, or denial of service. Apex Triggers execute automatically in response to DML events (insert, update, delete, undelete) on records, and can have a significant impact on data integrity and system behavior.8  
  * **Key Attributes/Elements for Analysis**:  
    * apiVersion: The API version against which the Apex class or trigger was saved. This version can influence Apex runtime behavior, including access to newer language features and adherence to evolving security enforcement by the platform.9  
    * status: Indicates the deployment status of the class or trigger (e.g., Active, Inactive, Deleted). Inactive code might still pose latent risks if it can be reactivated without proper review.81  
    * Sharing Keywords (within the code body): The declaration of sharing context (e.g., with sharing, without sharing, inherited sharing) at the class level is critical for determining how record access permissions are enforced during execution.  
    * The actual Apex code body (retrieved as part of the .cls or .trigger file associated with the metadata component) is the primary target for static code analysis to identify vulnerabilities such as SOQL/SOSL injection, Cross-Site Scripting (XSS) in Visualforce controllers, improper exception handling, exposure of sensitive data in debug logs, or insecure cryptographic practices.  
  * **package.xml Specification**:  
    XML  
    \<types\>  
        \<members\>\*\</members\> \<name\>ApexClass\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\> \<name\>ApexTrigger\</name\>  
    \</types\>

  .8

  * Apex classes declared with the without sharing keyword operate in a system context that bypasses the running user's record-level sharing rules and, in some cases, field-level security for query results. While sometimes necessary for specific business logic (e.g., aggregating data the user might not directly see), this capability is inherently powerful and potentially dangerous if not carefully controlled. Apex code running without sharing, especially if it's exposed to or callable by users with limited privileges (e.g., guest users in an Experience Cloud site, or internal users with restricted data access), can lead to privilege escalation or unintended data exposure. Analyzing the sharing context declared in Apex classes is therefore a fundamental security check.80  
  * The apiVersion of an Apex class or trigger 9 is also significant because Salesforce occasionally introduces changes in Apex runtime behavior or security enforcement that are tied to specific API versions. Code running on very old API versions might not benefit from newer platform-level security protections or might exhibit behaviors that are now considered insecure.  
* **LightningComponentBundle (LWC)**  
  * **Security Importance**: LWCs are the modern framework for building user interfaces in Salesforce. Security considerations for LWCs include secure DOM manipulation to prevent XSS, proper handling of data passed to and from Apex controllers, secure usage of third-party JavaScript libraries, and ensuring that components exposed in different contexts (like Experience Builder) do not inadvertently leak sensitive information.9  
  * **Key Attributes/Elements for Analysis** (primarily from the component's .js-meta.xml configuration file 85):  
    * isExposed: A boolean. If true, the LWC is made available for use in declarative builders like Lightning App Builder or Experience Builder. If false, it's typically an internal component not directly placeable by admins.85  
    * targets \> target: Defines the specific contexts where an exposed component can be used (e.g., lightning\_\_AppPage, lightning\_\_RecordPage, lightningCommunity\_\_Page). The target dictates the potential audience and exposure of the component.85  
    * capabilities \> capability \> lightningCommunity\_\_RelaxedCSP: If this capability is present and enabled for a component in a managed package, it allows the component to operate with a more relaxed Content Security Policy in Experience Builder sites where Lightning Locker might be disabled. This has direct security implications as it widens the scope of allowed external resources.85  
    * The JavaScript code within the LWC bundle (the .js file) requires careful analysis for client-side vulnerabilities, such as improper handling of user inputs, insecure use of innerHTML, or vulnerabilities in any third-party libraries used.  
    * Interactions with Apex controllers (methods annotated with @AuraEnabled) must ensure that the Apex methods themselves are secure (e.g., respect FLS and CRUD, prevent SOQL injection, use appropriate sharing context).  
  * **package.xml Specification**:  
    XML  
    \<types\>  
        \<members\>\*\</members\> \<name\>LightningComponentBundle\</name\>  
    \</types\>

  * The combination of the isExposed flag and the defined targets in an LWC's configuration file 85 effectively determines its potential attack surface. A component that is not exposed (isExposed is false) or is only targeted for internal App Pages is generally less risky from an external threat perspective than a component that is exposed and targeted for lightningCommunity\_\_Page, which could be placed on an Experience Cloud site accessible to guest (unauthenticated) users. The security review must consider this exposure context.  
  * LWCs that interact with Apex controllers rely on those server-side methods for data operations. The security of the LWC is therefore dependent on the security of the @AuraEnabled Apex methods it calls. These Apex methods must perform their own FLS and CRUD checks (unless intentionally designed to run with elevated system privileges, which requires even more scrutiny) and be protected against common Apex vulnerabilities.  
* **Flow**  
  * **Security Importance**: Flows are an increasingly powerful declarative automation tool in Salesforce, capable of performing complex business logic, DML operations, calling Apex actions, and interacting with external systems. Incorrectly configured flows, particularly those running in a system context that bypasses user permissions, can introduce significant security risks, such as unauthorized data access/modification or unintended process execution.10  
  * **Key Attributes/Elements for Analysis**:  
    * runInMode: This is a critical attribute that defines the execution context of the flow. Possible values include SystemModeWithoutSharing (flow runs with system permissions, ignoring user sharing rules and FLS for DML), SystemModeWithSharing (flow runs with system permissions but respects user sharing rules and FLS), and UserMode (flow runs in the context of the current user, respecting their permissions).10  
    * processType: Indicates the type of flow (e.g., AutoLaunchedFlow, ScreenFlow, RecordTriggeredFlow). This influences how the flow can be invoked and its typical use cases.  
    * status: The current status of the flow version (e.g., Active, Draft).  
    * Elements within the flow definition that invoke Apex actions, perform DML operations (Create, Update, Get, Delete Records), or make callouts. The security of these actions (especially custom Apex actions) needs to be assessed.  
    * For Screen Flows, analysis of input validation on screen elements is important to prevent injection of malicious data or unexpected behavior.  
  * **package.xml Specification**:  
    XML  
    \<types\>  
        \<members\>\*\</members\> \<name\>Flow\</name\>  
    \</types\>  
    (Note: For older API versions, FlowDefinition was used to manage flow activation 88; however, Flow is the standard type for API version 44.0 and later, and it includes the active version and other settings).  
  * A Flow configured to run in SystemModeWithoutSharing 10 operates with significantly elevated privileges, effectively bypassing the running user's FLS and record sharing rules for its DML operations and queries. While this mode is sometimes necessary to perform legitimate system-level automations that a user might not have direct permission for, it is also a major security checkpoint. If such a flow can be triggered by a user with limited privileges (e.g., a guest user on an Experience Cloud site or an internal user with restricted access), and the flow's logic is not carefully designed to prevent unauthorized data access or modification, it can lead to severe privilege escalation vulnerabilities.  
  * Salesforce is heavily promoting Flow as its primary declarative automation tool, encouraging the migration of functionality from older Workflow Rules and Process Builder.73 This strategic direction means that the security analysis of Flows is becoming increasingly critical to an org's overall security posture.  
* **WorkflowRule** (and associated actions: WorkflowAlert, WorkflowFieldUpdate, WorkflowOutboundMessage, WorkflowTask)  
  * **Security Importance**: Workflow Rules are a legacy automation tool in Salesforce. While Salesforce encourages migration to Flow, many organizations still have a significant number of active Workflow Rules. Their actions, such as field updates, can trigger further automations (like Apex Triggers or other Flows) and have security implications (e.g., changing a record's status, owner, or critical data fields). Outbound messages send data to external endpoints, which must be secure.41  
  * **Key Attributes/Elements for Analysis**:  
    * WorkflowRule:  
      * active: Boolean, indicates if the rule is active.  
      * triggerType: When the rule fires (e.g., onCreateOnly, onCreateOrTriggeringUpdate).  
      * criteriaItems or formula: Defines the conditions that trigger the rule.  
    * WorkflowFieldUpdate:  
      * field: The API name of the field to be updated.  
      * literalValue or formula: The new value for the field.  
      * targetObject: The object on which the field update occurs.  
    * WorkflowOutboundMessage:  
      * endpointUrl: The URL of the external system to which the message is sent. Must be HTTPS and trusted.  
      * includeSessionId: Boolean, if true, the Salesforce session ID is sent with the message, which is a security risk if the endpoint is not secure or trusted.  
      * protected: Boolean, indicates if the component is protected (for managed packages).  
      * integrationUser: The username of the Salesforce user whose context is used for sending the message.  
    * WorkflowAlert (Email Alert): recipients, template (email template used).  
    * WorkflowTask: assignedTo, status, priority.  
  * **package.xml Specification**:  
    XML  
    \<types\>  
        \<members\>\*\</members\> \<name\>Workflow\</name\> \</types\>

  .57

  * Workflow Rules and their associated field updates execute in a system context, meaning they can update fields even if the user who triggered the record change does not have FLS edit permission for those fields. This can be a source of confusion or unintended data modification if not well understood. WorkflowOutboundMessage actions that send data to external endpoints require careful scrutiny of the endpointUrl (must be HTTPS and trusted) and whether includeSessionId is enabled (which should generally be false unless absolutely necessary and the endpoint is highly secure).

### **3.6. Auditing and Compliance**

Metadata related to auditing helps in tracking changes and ensuring compliance.

* **HistoryRetentionPolicy** (defined within CustomObject metadata)  
  * **Security Importance**: This sub-component within an object's metadata defines how long field history tracking data (specifically for Field Audit Trail, which extends standard field history) is retained within Salesforce and subsequently in the archive. Proper configuration is essential for maintaining comprehensive audit trails for compliance purposes and for forensic analysis in the event of a security incident.49  
  * **Key Attributes/Elements for Analysis** (found within the \<historyRetentionPolicy\> tag inside a CustomObject's XML definition):  
    * archiveAfterMonths: The number of months field history data is kept in the live \[ObjectName\]History related list before being moved to the archive.  
    * archiveRetentionYears: The number of years the data is retained in the archive.  
    * description: A textual description of the policy.  
  * **package.xml Specification**: This policy is retrieved as part of the CustomObject metadata for the specific object it applies to.  
    XML  
    \<types\>  
        \<members\>Account\</members\>  
        \<members\>MySensitiveCustomObject\_\_c\</members\>  
        \<name\>CustomObject\</name\>  
    \</types\>  
    49  
  * Inadequate or unconfigured history retention policies for critical objects or fields can severely hamper the ability to investigate past security incidents, trace unauthorized data changes, or demonstrate compliance with data retention regulations that require long-term audit trails. Field Audit Trail 49 significantly extends the standard 18-24 months of field history tracking, and its HistoryRetentionPolicy settings are key to leveraging this capability effectively.

---

**Table 3.1: Client Credentials Flow Configuration \- Key Metadata Touchpoints**

This table maps the essential configuration aspects of the OAuth 2.0 Client Credentials Flow to their corresponding metadata types and specific XML elements or attributes that should be retrieved and analyzed.

| Configuration Aspect | Relevant Metadata Type(s) | Key XML Element/Attribute for Analysis | Security Focus |
| :---- | :---- | :---- | :---- |
| Enable Client Credentials Flow | ConnectedApp | oauthConfig \> isClientCredentialFlowEnabled (boolean) | Verifying the flow is intentionally active for the app. |
| Define Execution User | ConnectedApp | oauthConfig \> clientCredentialsFlowExecutionUser (username) | **Critical**: Permissions of this user define the app's access scope. Cross-reference with user's Profile and PermissionSets. |
| Enable Client Credentials Flow (ECA) | ExtlClntAppOauthSettings (linked to ExternalClientApplication) | oauthConfig \> isClientCredentialsFlowEnabled (boolean) | Verifying flow activity for External Client Applications. |
| Define Execution User (ECA) | ExtlClntAppOauthSettings (linked to ExternalClientApplication) | oauthConfig \> runAsUser (username) | **Critical**: Similar to ConnectedApp, this user's permissions dictate the ECA's access. Audit associated Profile and PermissionSets. |
| Set OAuth Scopes | ConnectedApp, ExtlClntAppOauthSettings | oauthConfig \> scopes \> scope (list of scope names) | Ensuring the principle of least privilege. Scopes like full are high risk. |
| Configure IP Restrictions | ConnectedApp | oauthPolicy \> ipRelaxation, ipRanges (if defined within app) | Restricting token issuance to trusted IP addresses. |
| Session Policies | ConnectedApp | sessionPolicy \> sessionTimeout, blockSessionsUntilAuthentication | Managing session lifetime and security for app-initiated sessions. |
| Consumer Key / Client ID | ConnectedApp | oauthConfig \> consumerKey | Identifying the application. (Secret is not in metadata). |

*5*

## ---

**4\. Constructing a Comprehensive package.xml for Security Analysis**

To perform a thorough security analysis, the package.xml manifest must be carefully constructed to retrieve a wide array of security-relevant metadata types. The following is a consolidated example template, designed to be comprehensive. However, it must be adapted based on the specific Salesforce org's configuration, the API version being targeted, and the specific focus areas of the security analysis.

XML

\<?xml version="1.0" encoding="UTF-8"?\>  
\<Package xmlns\="http://soap.sforce.com/2006/04/metadata"\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>ConnectedApp\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>ExternalClientApplication\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>ExtlClntAppOauthSettings\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>ExtlClntAppGlobalOauthSettings\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>ExtlClntAppOauthConfigurablePolicies\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>AuthProvider\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>NamedCredential\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>ExternalCredential\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>Certificate\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>PublicKeyCertificate\</name\>  
    \</types\>

    \<types\>  
        \<members\>\*\</members\>  
        \<name\>Profile\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>PermissionSet\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>PermissionSetGroup\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>Role\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>Group\</name\>  
    \</types\>

    \<types\>  
        \<members\>\*\</members\> \<members\>Account\</members\> \<members\>Contact\</members\>  
        \<members\>Lead\</members\>  
        \<members\>Opportunity\</members\>  
        \<members\>Case\</members\>  
        \<members\>User\</members\>  
        \<name\>CustomObject\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\> \<name\>SharingRules\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>SharingSet\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>FieldRestrictionRule\</name\>  
    \</types\>

    \<types\>  
        \<members\>Security\</members\> \<name\>SecuritySettings\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\> \<name\>NetworkAccess\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>RemoteSiteSetting\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>CspTrustedSite\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\> \<name\>CorsWhitelistOrigin\</name\>  
    \</types\>

    \<types\>  
        \<members\>\*\</members\>  
        \<name\>ApexClass\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>ApexTrigger\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>LightningComponentBundle\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>Flow\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>Workflow\</name\> \</types\>

    \<types\>  
        \<members\>\*\</members\>  
        \<name\>CustomApplication\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>CustomTab\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>Layout\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\> \<name\>Network\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>CustomSite\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>ExperienceBundle\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>DigitalExperienceBundle\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\>  
        \<name\>DigitalExperienceConfig\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\> \<name\>CustomPermission\</name\>  
    \</types\>  
    \<types\>  
        \<members\>\*\</members\> \<name\>StaticResource\</name\>  
    \</types\>

    \<version\>61.0\</version\> \</Package\>

### **Guidance on Specifying Members**

* **Wildcard (\*) Usage**: As demonstrated, \* is used for many metadata types to retrieve all components. However, always refer to Table 2.1 for types that do not support this and require explicit member listing or retrieval via their parent component.7  
* **CustomObject Type**: It is critical to remember that for the CustomObject metadata type, the wildcard \* will retrieve all *custom* objects. Standard objects (e.g., Account, Contact, Case, User) must be explicitly listed by their API names within the \<members\> tag to be included in the retrieval.7  
* **Types Not Supporting Wildcards**: For metadata types listed in Table 2.1 (e.g., CustomField, ValidationRule, RecordType), their definitions are typically retrieved as part of their parent CustomObject metadata. If attempting to retrieve them as standalone types (which is less common for these specific examples), each member would need to be explicitly named. For instance, to retrieve specific list views, one would list them: \<members\>Account.My\_Account\_ListView\</members\>\<name\>ListView\</name\>. Manually maintaining such lists for a comprehensive scan is challenging.

### **API Version Considerations**

The \<version\> tag in package.xml is of paramount importance. It dictates which version of the Metadata API is used for the retrieve operation.9

* Always specify a recent and specific API version that is supported by both the security analysis tool and the target Salesforce organizations. Avoid relying on default API versions, which might vary.  
* The chosen API version directly affects which metadata fields, sub-components, and even entire metadata types are available for retrieval and how their structure is interpreted. Using an older API version might result in missing newer security features or settings that have been introduced in more recent Salesforce releases.10

A truly "complete" package.xml designed to fetch every conceivable security-relevant metadata component from a large, mature Salesforce org can become exceptionally large. This can lead to challenges with Metadata API retrieval limits, such as the 10,000 file limit or total request size restrictions.12 Therefore, the strategy for constructing the package.xml must balance the ideal of absolute comprehensiveness with practical manageability. This might involve:

* Starting with a broad template like the one provided.  
* Using Salesforce CLI to generate an org-specific manifest and then potentially splitting it if it's too large.12  
* Allowing the security product user to customize the package.xml to focus on specific areas or to exclude voluminous but lower-risk metadata types for routine scans.

The process of identifying which metadata components to include for a thorough security analysis is not a one-time task. As Salesforce continues to innovate and evolve its platform, new metadata types or attributes with security implications will inevitably emerge.30 Consequently, the package.xml used for security analysis, and the analytical logic of the security product itself, must be adaptable and regularly updated to remain effective.

## **5\. Best Practices for Metadata Retrieval in a Security Context**

Effectively retrieving Salesforce metadata for security analysis involves more than just constructing a package.xml file. Adhering to best practices ensures the process is accurate, efficient, and secure.

* Regularly Update Metadata Type Knowledge and package.xml Definitions:  
  Salesforce introduces new metadata types and modifies existing ones with each of its three annual releases.30 It is crucial to stay informed about these changes by regularly reviewing Salesforce release notes, the Metadata API Developer Guide, and the Metadata Coverage Report. The package.xml used by the security product, and its internal logic for parsing and analyzing metadata, must be updated to incorporate new security-relevant types and attributes to maintain the comprehensiveness of the analysis.  
* Adopt a Strategy for Comprehensive vs. Targeted Retrieval:  
  Retrieving all metadata components using a broadly defined package.xml (e.g., one generated by sf project generate manifest 12 or a very inclusive template like those found in community repositories 14\) provides the most complete snapshot of an org's configuration. This is ideal for establishing a security baseline. However, such comprehensive retrievals can be time-consuming and may hit API limits for very large orgs. For subsequent or more focused analyses, a targeted retrieval approach—focusing only on specific high-risk metadata types, components that have recently changed, or areas of particular concern—can be more efficient. A hybrid strategy might be optimal: perform periodic comprehensive retrievals and supplement with more frequent targeted scans.  
* Test Retrieval Logic Across Diverse Org Editions and Configurations:  
  While the Metadata API aims for consistency, the availability or behavior of certain metadata types can sometimes vary subtly by Salesforce edition (e.g., Professional Edition vs. Enterprise Edition vs. Unlimited Edition) or by specific org configurations and enabled features (e.g., ExternalClientApplication availability 5). It is advisable to test the security product's metadata retrieval logic against a variety of org types and configurations to ensure consistent and accurate data gathering.  
* Implement Robust Handling of API Retrieval Limits:  
  As previously discussed, the Metadata API has limits on the number of files and the total size of the retrieved package.12 The security product must be designed to handle these limits gracefully. This might involve programmatically splitting a large retrieval request into multiple smaller ones, guiding the user to provide pre-split package.xml files, or implementing retry logic for transient API issues.  
* Ensure Sufficient Permissions for the Retrieving User/Integration:  
  The user context under which the metadata retrieval occurs (in this case, the RunAs user associated with the Client Credentials Flow via the ConnectedApp or ExternalClientApplication) must possess adequate permissions to read all the metadata types specified in the package.xml. Typically, system permissions like "Modify All Data" or "View All Data," combined with "Customize Application," are sufficient for broad metadata access.5 Insufficient permissions will result in incomplete retrievals or errors.  
* Securely Manage Retrieved Metadata Containing Potential PII:  
  It is important to recognize that metadata itself can sometimes contain Personally Identifiable Information (PII) or other sensitive business information. This might occur in custom object names, custom field labels or API names, report names, email template content, or descriptions within various metadata components.1 The security product, upon retrieving this metadata, becomes a custodian of this potentially sensitive configuration data. It must therefore implement strong security measures for storing and processing this retrieved metadata, including encryption at rest and in transit, robust access controls for the storage systems, and secure handling of analysis results to prevent data leakage.  
* Version Control for Master package.xml Templates:  
  If the security product utilizes standardized package.xml templates for analysis, these templates should be maintained under a version control system (e.g., Git). This allows for tracking changes to the templates over time, documenting the rationale for including or excluding specific metadata types, and facilitating collaboration if multiple developers are involved in maintaining the templates.

The act of retrieving an organization's comprehensive metadata is itself a privileged operation. The credentials used for this retrieval (i.e., the clientId and clientSecret of the ConnectedApp or ExternalClientApplication used for the Client Credentials Flow) are highly sensitive and must be protected rigorously. The system performing the retrieval, and storing these credentials, must be secured against unauthorized access. A compromise of these credentials could allow an attacker to perform the same metadata retrieval, gaining a detailed blueprint of the target org's configurations and potential weaknesses.

Furthermore, metadata analysis provides a snapshot of the org's security posture at a specific point in time. Salesforce orgs are dynamic environments, with configurations changing frequently due to administrative actions, new development, or the installation of AppExchange packages. For ongoing security monitoring and to detect new misconfigurations or emerging threats, the metadata retrieval and analysis process must be repeated on a regular basis.

## **6\. Conclusion and Recommendations**

The meticulous construction of a package.xml manifest is fundamental to conducting an effective and comprehensive security analysis of a Salesforce organization. By strategically targeting the appropriate metadata types, a security product can gain deep insights into an org's security posture, identify vulnerabilities, and help enforce best practices.

**Key Takeaways**:

1. **Metadata is the Blueprint**: Salesforce metadata defines the entirety of an org's configuration, including its security controls. Analyzing this metadata is non-negotiable for a deep security assessment.1  
2. **package.xml is the Key**: The package.xml file, in conjunction with the Metadata API, is the primary mechanism for extracting this blueprint.1 Its proper construction is critical.  
3. **Critical Metadata Categories**: A thorough security analysis must focus on metadata related to:  
   * **Authentication and Authorization**: ConnectedApp, ExternalClientApplication, AuthProvider, NamedCredential, ExternalCredential, Certificate.  
   * **User Access Controls**: Profile, PermissionSet, PermissionSetGroup, Role, Group.  
   * **Data Security and Visibility**: CustomObject (for OWDs and field attributes), SharingRules, SharingSet, FieldRestrictionRule.  
   * **Org-Wide Security Configurations**: SecuritySettings, RemoteSiteSetting, CspTrustedSite, NetworkAccess.  
   * **Custom Code and Automation**: ApexClass, ApexTrigger, LightningComponentBundle, Flow, Workflow.  
   * **Auditing and Compliance**: HistoryRetentionPolicy (within CustomObject).  
4. **Client Credentials Flow Context**: When authenticating via Client Credentials Flow, the security of the ConnectedApp or ExternalClientApplication itself, and particularly the permissions of its designated RunAs user, are of paramount importance and form an initial layer of analysis.5  
5. **package.xml Nuances**: Effective use of package.xml requires understanding wildcard (\*) limitations 11, the explicit naming requirement for standard objects within the CustomObject type 7, and the critical impact of API versioning.9

**Recommendations**:

1. **Prioritize Analysis of the Authentication Mechanism**: The security product should first analyze the metadata of the ConnectedApp or ExternalClientApplication it uses for authentication, along with the Profile and PermissionSets of the associated RunAs user. This ensures the analysis tool itself is not leveraging an insecure configuration.  
2. **Adopt a Comprehensive Baseline package.xml**: Utilize a comprehensive package.xml template, such as the example provided in Section 4, as a starting point. This template should be adapted based on the specific API version capabilities and the types of orgs being analyzed.  
3. **Implement Strategies for Wildcard Limitations and Retrieval Limits**: For types not supporting wildcards, ensure they are retrieved via their parent components (e.g., fields within CustomObject). For large orgs, be prepared to split package.xml manifests or guide users on how to do so to avoid API limits.12  
4. **Stay Current with Salesforce Releases**: Establish a process for regularly reviewing Salesforce release notes and the Metadata API Developer Guide. Update the list of targeted metadata types and the analytical logic of the security product to incorporate new security features and metadata components.30  
5. **Emphasize Interconnectedness**: The most critical security insights often arise from understanding the interplay between different metadata components (e.g., how a Profile's permissions affect a Flow run in user mode, or how a ConnectedApp's scopes are realized through its RunAs user's PermissionSets). The security analysis should not treat metadata types in isolation.  
6. **Combine Metadata Analysis with Other Security Measures**: While metadata analysis provides an invaluable "as-configured" view, it should be complemented by other security practices, including regular Salesforce Health Checks 64, runtime event monitoring (using tools like Event Monitoring or Salesforce Shield 64), user activity reviews, and robust user training programs.  
7. **Secure the Retrieved Metadata**: Given that retrieved metadata can contain sensitive configuration details and potentially PII 1, the security product must ensure this data is stored, processed, and transmitted securely.

The Salesforce platform is dynamic, and its security landscape evolves continuously. A security product that leverages metadata analysis must be equally dynamic, adapting its retrieval strategies and analytical capabilities to provide ongoing, relevant, and comprehensive security assessments. By focusing on the critical metadata types outlined in this report and adhering to best practices for their retrieval and analysis, a security product can offer significant value in helping organizations maintain a strong Salesforce security posture.

#### **Works cited**

1. Understanding Metadata API \- Salesforce Developers, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_intro.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_intro.htm)  
2. Use Cases for Metadata API \- Salesforce Developers, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_use\_cases.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_use_cases.htm)  
3. OAuth 2.0 Client Credentials Flow for Server-to-Server Integration, accessed May 7, 2025, [https://help.salesforce.com/s/articleView?id=xcloud.remoteaccess\_oauth\_client\_credentials\_flow.htm\&language=en\_US\&type=5](https://help.salesforce.com/s/articleView?id=xcloud.remoteaccess_oauth_client_credentials_flow.htm&language=en_US&type=5)  
4. Client Credentials Flow | Salesforce Platform APIs \- Postman, accessed May 7, 2025, [https://www.postman.com/salesforce-developers/salesforce-developers/request/sum50tp/client-credentials-flow](https://www.postman.com/salesforce-developers/salesforce-developers/request/sum50tp/client-credentials-flow)  
5. Configure a Connected App for the OAuth 2.0 Client Credentials Flow \- Salesforce Help, accessed May 7, 2025, [https://help.salesforce.com/s/articleView?id=sf.connected\_app\_client\_credentials\_setup.htm\&language=en\_US\&type=5](https://help.salesforce.com/s/articleView?id=sf.connected_app_client_credentials_setup.htm&language=en_US&type=5)  
6. OAuth Authorization Flows \- Salesforce Help, accessed May 7, 2025, [https://help.salesforce.com/articleView?id=remoteaccess\_oauth\_flows.htm\&type=5](https://help.salesforce.com/articleView?id=remoteaccess_oauth_flows.htm&type=5)  
7. Sample package.xml Manifest Files | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/manifest\_samples.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/manifest_samples.htm)  
8. How to retrieve the Apex classes | Salesforce Trailblazer Community, accessed May 7, 2025, [https://trailhead.salesforce.com/trailblazer-community/feed/0D54V00007T44nsSAB](https://trailhead.salesforce.com/trailblazer-community/feed/0D54V00007T44nsSAB)  
9. How to manage Metadata API versions on Salesforce \- Gearset, accessed May 7, 2025, [https://gearset.com/blog/regain-control-of-salesforce-metadata-api-versions/](https://gearset.com/blog/regain-control-of-salesforce-metadata-api-versions/)  
10. Flows metadata that Bypass User Permissions (SFDX) \- Salesforce Stack Exchange, accessed May 7, 2025, [https://salesforce.stackexchange.com/questions/313378/flows-metadata-that-bypass-user-permissions-sfdx](https://salesforce.stackexchange.com/questions/313378/flows-metadata-that-bypass-user-permissions-sfdx)  
11. Salesforce Metadata Types that Do Not Support Wildcard Characters in Package.xml, accessed May 7, 2025, [https://www.asagarwal.com/salesforce-metadata-types-that-do-not-support-wildcard-characters-in-package-xml/](https://www.asagarwal.com/salesforce-metadata-types-that-do-not-support-wildcard-characters-in-package-xml/)  
12. Generate a Manifest and Retrieve Metadata From the Org \- Salesforce Help, accessed May 7, 2025, [https://help.salesforce.com/s/articleView?id=sf.devops\_center\_setup\_seed\_repo\_generate\_manifest.htm\&language=en\_US\&type=5](https://help.salesforce.com/s/articleView?id=sf.devops_center_setup_seed_repo_generate_manifest.htm&language=en_US&type=5)  
13. How to retrieve your entire Salesforce metadata with 2 commands \- Pablo Gonzalez, accessed May 7, 2025, [https://www.pablogonzalez.io/how-to-retrieve-your-entire-salesforce-metadata-with-2-commands/](https://www.pablogonzalez.io/how-to-retrieve-your-entire-salesforce-metadata-with-2-commands/)  
14. package-all-metadata-v53-1-of-2.xml \- GitHub, accessed May 7, 2025, [https://github.com/asagarwal/salesforce-package-xml/blob/main/package-all-metadata-v53-1-of-2.xml](https://github.com/asagarwal/salesforce-package-xml/blob/main/package-all-metadata-v53-1-of-2.xml)  
15. ConnectedApp | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_connectedapp.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_connectedapp.htm)  
16. Configure the External Client App OAuth Settings \- Salesforce Help, accessed May 7, 2025, [https://help.salesforce.com/s/articleView?id=sf.configure\_external\_client\_app\_oauth\_settings.htm\&language=en\_US\&type=5](https://help.salesforce.com/s/articleView?id=sf.configure_external_client_app_oauth_settings.htm&language=en_US&type=5)  
17. Create a Local External Client App with Metadata API, accessed May 7, 2025, [https://help.salesforce.com/s/articleView?id=sf.meta\_create\_a\_local\_external\_client\_app.htm\&language=en\_US\&type=5](https://help.salesforce.com/s/articleView?id=sf.meta_create_a_local_external_client_app.htm&language=en_US&type=5)  
18. Integrate an App for the Token Exchange Flow \- Salesforce Help, accessed May 7, 2025, [https://help.salesforce.com/s/articleView?id=sf.remoteaccess\_token\_exchange\_integrate.htm\&language=en\_US\&type=5](https://help.salesforce.com/s/articleView?id=sf.remoteaccess_token_exchange_integrate.htm&language=en_US&type=5)  
19. Deploy an External Client App That References the Source Org's Global OAuth Settings File, accessed May 7, 2025, [https://help.salesforce.com/s/articleView?id=sf.deploy\_external\_client\_app\_that\_references\_the\_source\_org.htm\&language=en\_US\&type=5](https://help.salesforce.com/s/articleView?id=sf.deploy_external_client_app_that_references_the_source_org.htm&language=en_US&type=5)  
20. AuthProviderPluginClass Class | Apex Reference Guide \- Salesforce Developers, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.apexref.meta/apexref/apex\_class\_Auth\_AuthProviderPluginClass.htm](https://developer.salesforce.com/docs/atlas.en-us.apexref.meta/apexref/apex_class_Auth_AuthProviderPluginClass.htm)  
21. AuthProvider | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_authproviders.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_authproviders.htm)  
22. Configure External Auth Identity | Salesforce Trailblazer Community \- Trailhead, accessed May 7, 2025, [https://trailhead.salesforce.com/trailblazer-community/feed/0D5KX00000MaTzz0AF](https://trailhead.salesforce.com/trailblazer-community/feed/0D5KX00000MaTzz0AF)  
23. Configure an Authentication Provider Using OpenID Connect \- Salesforce Help, accessed May 7, 2025, [https://help.salesforce.com/s/articleView?id=sf.sso\_provider\_openid\_connect.htm\&language=en\_US\&type=5](https://help.salesforce.com/s/articleView?id=sf.sso_provider_openid_connect.htm&language=en_US&type=5)  
24. Package Named Credentials | Named Credentials Packaging Guide ..., accessed May 7, 2025, [https://developer.salesforce.com/docs/platform/named-credentials/guide/nc-package-credentials.html](https://developer.salesforce.com/docs/platform/named-credentials/guide/nc-package-credentials.html)  
25. Certificate | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_certificate.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_certificate.htm)  
26. PublicKeyCertificate | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_publickeycertificate.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_publickeycertificate.htm)  
27. Manage Contact Center Certificates \- Salesforce Help, accessed May 7, 2025, [https://help.salesforce.com/s/articleView?id=service.voice\_manage\_certificates.htm\&language=en\_US\&type=5](https://help.salesforce.com/s/articleView?id=service.voice_manage_certificates.htm&language=en_US&type=5)  
28. Data Classification Metadata Fields \- Salesforce Help, accessed May 7, 2025, [https://help.salesforce.com/s/articleView?id=platform.data\_classification\_metadata\_fields.htm\&language=en\_US\&type=5](https://help.salesforce.com/s/articleView?id=platform.data_classification_metadata_fields.htm&language=en_US&type=5)  
29. Salesforce Metadata API: Your Complete Guide, accessed May 7, 2025, [https://www.salesforceben.com/salesforce-metadata-api-your-complete-guide/](https://www.salesforceben.com/salesforce-metadata-api-your-complete-guide/)  
30. SecuritySettings | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_securitysettings.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_securitysettings.htm)  
31. asagarwal/salesforce-package-xml \- GitHub, accessed May 7, 2025, [https://github.com/asagarwal/salesforce-package-xml](https://github.com/asagarwal/salesforce-package-xml)  
32. Profile Generator: The Missing Piece for SFDX \- Salesforce Ben, accessed May 7, 2025, [https://www.salesforceben.com/profile-generator-the-missing-piece-for-sfdx/](https://www.salesforceben.com/profile-generator-the-missing-piece-for-sfdx/)  
33. How to deploy a new custom field with its field-level security settings | Gearset Help Center, accessed May 7, 2025, [https://docs.gearset.com/en/articles/1381749-how-to-deploy-a-new-custom-field-with-its-field-level-security-settings](https://docs.gearset.com/en/articles/1381749-how-to-deploy-a-new-custom-field-with-its-field-level-security-settings)  
34. Salesforce Metadata API CustomField Set Field Level Security \- Stack Overflow, accessed May 7, 2025, [https://stackoverflow.com/questions/49544295/salesforce-metadata-api-customfield-set-field-level-security](https://stackoverflow.com/questions/49544295/salesforce-metadata-api-customfield-set-field-level-security)  
35. Profile | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_profile.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_profile.htm)  
36. Securing Your Org Through Salesforce IP Ranges: Your Complete ..., accessed May 7, 2025, [https://www.salesforceben.com/securing-your-org-through-salesforce-ip-ranges-your-complete-guide/](https://www.salesforceben.com/securing-your-org-through-salesforce-ip-ranges-your-complete-guide/)  
37. Salesforce Metadata API Automation 2025 \- Oaktree Software, accessed May 7, 2025, [https://www.oaktreecloud.com/salesforce-metadata-api-automation-2025/](https://www.oaktreecloud.com/salesforce-metadata-api-automation-2025/)  
38. New Metadata Types \- Salesforce Help, accessed May 7, 2025, [https://help.salesforce.com/s/articleView?id=release-notes.rn\_pricing\_new\_metadata\_api.htm\&language=en\_US\&release=248\&type=5](https://help.salesforce.com/s/articleView?id=release-notes.rn_pricing_new_metadata_api.htm&language=en_US&release=248&type=5)  
39. Permission Set System Permissions | Salesforce Trailblazer Community \- Trailhead, accessed May 7, 2025, [https://trailhead.salesforce.com/trailblazer-community/feed/0D54V00007Yw6obSAB](https://trailhead.salesforce.com/trailblazer-community/feed/0D54V00007Yw6obSAB)  
40. PermissionSet | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_permissionset.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_permissionset.htm)  
41. Decomposed Metadata Types | Salesforce DX Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.sfdx\_dev.meta/sfdx\_dev/sfdx\_dev\_ws\_decomposed\_md\_types.htm](https://developer.salesforce.com/docs/atlas.en-us.sfdx_dev.meta/sfdx_dev/sfdx_dev_ws_decomposed_md_types.htm)  
42. Evolving Your Salesforce Security Model: Part 2 \- Elements.cloud, accessed May 7, 2025, [https://elements.cloud/blog/evolving-your-salesforce-security-model-part-2-creating-a-permission-set-group-for-a-job-to-be-done/](https://elements.cloud/blog/evolving-your-salesforce-security-model-part-2-creating-a-permission-set-group-for-a-job-to-be-done/)  
43. PermissionSetGroup | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_permissionsetgroup.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_permissionsetgroup.htm)  
44. MutingPermissionSet | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_mutingpermissionset.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_mutingpermissionset.htm)  
45. Role | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_role.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_role.htm)  
46. Deploy Your Experience Cloud Site with the Metadata API \- Salesforce Developers, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.communities\_dev.meta/communities\_dev/networks\_migrating\_from\_sandbox.htm](https://developer.salesforce.com/docs/atlas.en-us.communities_dev.meta/communities_dev/networks_migrating_from_sandbox.htm)  
47. DataCategoryGroup | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_datacategorygroup.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_datacategorygroup.htm)  
48. Group | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_group.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_group.htm)  
49. Examples | Field Audit Trail Implementation Guide \- Salesforce Developers, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.field\_history\_retention.meta/field\_history\_retention/field\_history\_workflow\_examples.htm](https://developer.salesforce.com/docs/atlas.en-us.field_history_retention.meta/field_history_retention/field_history_workflow_examples.htm)  
50. CustomObject | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/customobject.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/customobject.htm)  
51. Organization-Wide Defaults (OWD) in Salesforce \- CRS Info Solutions, accessed May 7, 2025, [https://www.crsinfosolutions.com/mastering-data-access-and-security-in-salesforce-a-comprehensive-guide-to-organization-wide-defaults-owd/](https://www.crsinfosolutions.com/mastering-data-access-and-security-in-salesforce-a-comprehensive-guide-to-organization-wide-defaults-owd/)  
52. Deploy Object's OWD using Metadata(XML) File – SFDCWallah, accessed May 7, 2025, [https://sfdcwallah.com/2020/02/27/deploy-objects-owd-using-metadataxml-file/](https://sfdcwallah.com/2020/02/27/deploy-objects-owd-using-metadataxml-file/)  
53. Update OWD using metadata for custom objects \- Salesforce Stack Exchange, accessed May 7, 2025, [https://salesforce.stackexchange.com/questions/277886/update-owd-using-metadata-for-custom-objects](https://salesforce.stackexchange.com/questions/277886/update-owd-using-metadata-for-custom-objects)  
54. How to deploy Sharing rules using Eclipse \- Salesforce Stack Exchange, accessed May 7, 2025, [https://salesforce.stackexchange.com/questions/105811/how-to-deploy-sharing-rules-using-eclipse](https://salesforce.stackexchange.com/questions/105811/how-to-deploy-sharing-rules-using-eclipse)  
55. CriteriaBasedSharingRule | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_cbsrule.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_cbsrule.htm)  
56. SharingRules | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_sharingrules.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_sharingrules.htm)  
57. SFDX CLI does not deploy Standard SObject workflow elements \-- Deployed Source No results found · Issue \#707 · forcedotcom/cli \- GitHub, accessed May 7, 2025, [https://github.com/forcedotcom/cli/issues/707](https://github.com/forcedotcom/cli/issues/707)  
58. OwnerSharingRule | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_ownersharingrule.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_ownersharingrule.htm)  
59. SharingSettings | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_sharingsettings.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_sharingsettings.htm)  
60. MobileSecurityPolicy | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_mobilesecuritypolicy.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_mobilesecuritypolicy.htm)  
61. SharingSet | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_sharingset.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_sharingset.htm)  
62. Metadata API \- Salesforce Help, accessed May 7, 2025, [https://help.salesforce.com/s/articleView?id=release-notes.rn\_api\_meta.htm\&language=en\_US\&release=240\&type=5](https://help.salesforce.com/s/articleView?id=release-notes.rn_api_meta.htm&language=en_US&release=240&type=5)  
63. StaticResource | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_staticresource.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_staticresource.htm)  
64. Salesforce Security Guide, accessed May 7, 2025, [https://resources.docs.salesforce.com/latest/latest/en-us/sfdc/pdf/salesforce\_security\_impl\_guide.pdf](https://resources.docs.salesforce.com/latest/latest/en-us/sfdc/pdf/salesforce_security_impl_guide.pdf)  
65. Metadata API \- Salesforce Help, accessed May 7, 2025, [https://help.salesforce.com/s/articleView?id=release-notes.rn\_api\_meta.htm\&language=en\_US\&release=246\&type=5](https://help.salesforce.com/s/articleView?id=release-notes.rn_api_meta.htm&language=en_US&release=246&type=5)  
66. Settings | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_settings.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_settings.htm)  
67. UserInterfaceSettings | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_userinterfacesettings.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_userinterfacesettings.htm)  
68. ContentSettings | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_contentsettings.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_contentsettings.htm)  
69. Custom Baseline File Requirements \- Salesforce Help, accessed May 7, 2025, [https://help.salesforce.com/s/articleView?id=sf.security\_custom\_baseline\_file\_requirements.htm\&language=en\_US\&type=5](https://help.salesforce.com/s/articleView?id=sf.security_custom_baseline_file_requirements.htm&language=en_US&type=5)  
70. Set Trusted IP Ranges for Your Organization \- Salesforce Help, accessed May 7, 2025, [https://help.salesforce.com/s/articleView?id=sf.security\_networkaccess.htm\&language=en\_US\&type=5](https://help.salesforce.com/s/articleView?id=sf.security_networkaccess.htm&language=en_US&type=5)  
71. RemoteSiteSetting | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_remotesitesetting.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_remotesitesetting.htm)  
72. How to configure RemoteSiteSetting and CsPTrustedSite metadata ..., accessed May 7, 2025, [https://salesforce.stackexchange.com/questions/310061/how-to-configure-remotesitesetting-and-csptrustedsite-metadata](https://salesforce.stackexchange.com/questions/310061/how-to-configure-remotesitesetting-and-csptrustedsite-metadata)  
73. Metadata API \- Salesforce Help, accessed May 7, 2025, [https://help.salesforce.com/s/articleView?id=release-notes.rn\_api\_meta.htm\&language=en\_US\&release=238\&type=5](https://help.salesforce.com/s/articleView?id=release-notes.rn_api_meta.htm&language=en_US&release=238&type=5)  
74. CspTrustedSite | Tooling API \- Salesforce Developers, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_tooling.meta/api\_tooling/tooling\_api\_objects\_csptrustedsite.htm](https://developer.salesforce.com/docs/atlas.en-us.api_tooling.meta/api_tooling/tooling_api_objects_csptrustedsite.htm)  
75. CspTrustedSite | Object Reference for the Salesforce Platform, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.object\_reference.meta/object\_reference/sforce\_api\_objects\_csptrustedsite.htm](https://developer.salesforce.com/docs/atlas.en-us.object_reference.meta/object_reference/sforce_api_objects_csptrustedsite.htm)  
76. CspTrustedSite | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_csptrustedsite.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_csptrustedsite.htm)  
77. Metadata Components and Types | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_objects\_intro.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_objects_intro.htm)  
78. Trying to create 2GP package version \- getting "Could not infer a metadata type", accessed May 7, 2025, [https://salesforce.stackexchange.com/questions/395983/trying-to-create-2gp-package-version-getting-could-not-infer-a-metadata-type](https://salesforce.stackexchange.com/questions/395983/trying-to-create-2gp-package-version-getting-could-not-infer-a-metadata-type)  
79. ApexClass | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_classes.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_classes.htm)  
80. Metadata | Apex Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex\_metadata.htm](https://developer.salesforce.com/docs/atlas.en-us.apexcode.meta/apexcode/apex_metadata.htm)  
81. An Easier Way to Delete Apex Classes From Production \- Salesforce Ben, accessed May 7, 2025, [https://www.salesforceben.com/way-to-delete-apex-classes-from-production/](https://www.salesforceben.com/way-to-delete-apex-classes-from-production/)  
82. ApexSettings | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_apexsettings.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_apexsettings.htm)  
83. ApexTrigger | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_triggers.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_triggers.htm)  
84. Read single ApexClass/ApexTrigger metadata files without using retrieve call, accessed May 7, 2025, [https://salesforce.stackexchange.com/questions/386166/read-single-apexclass-apextrigger-metadata-files-without-using-retrieve-call](https://salesforce.stackexchange.com/questions/386166/read-single-apexclass-apextrigger-metadata-files-without-using-retrieve-call)  
85. Experience Builder Sites | Use Components in Salesforce Targets ..., accessed May 7, 2025, [https://developer.salesforce.com/docs/platform/lwc/guide/use-config-for-community-builder.html](https://developer.salesforce.com/docs/platform/lwc/guide/use-config-for-community-builder.html)  
86. XML Configuration File Elements | Reference | Lightning Web Components Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/platform/lwc/guide/reference-configuration-tags.html](https://developer.salesforce.com/docs/platform/lwc/guide/reference-configuration-tags.html)  
87. Metadata API \- Salesforce Help, accessed May 7, 2025, [https://help.salesforce.com/s/articleView?id=release-notes.rn\_api\_meta.htm\&language=en\_US\&release=230\&type=5](https://help.salesforce.com/s/articleView?id=release-notes.rn_api_meta.htm&language=en_US&release=230&type=5)  
88. FlowDefinition | Metadata API Developer Guide \- Salesforce Help, accessed May 7, 2025, [https://help.salesforce.com/s/articleView?id=000387737\&type=1](https://help.salesforce.com/s/articleView?id=000387737&type=1)  
89. Flow Components for Metadata Translation \- Salesforce Help, accessed May 7, 2025, [https://help.salesforce.com/s/articleView?id=platform.workbench\_flow\_components.htm\&language=en\_US\&type=5](https://help.salesforce.com/s/articleView?id=platform.workbench_flow_components.htm&language=en_US&type=5)  
90. How to enable and disable flows using Metadata API. \- Trailhead \- Salesforce, accessed May 7, 2025, [https://trailhead.salesforce.com/trailblazer-community/feed/0D54V00007ZBZf5SAH](https://trailhead.salesforce.com/trailblazer-community/feed/0D54V00007ZBZf5SAH)  
91. accessed January 1, 1970, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_flow.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_flow.htm)  
92. Workflow | Metadata API Developer Guide, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_workflow.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_workflow.htm)  
93. Metadata Coverage Report \- Salesforce Developers, accessed May 7, 2025, [https://developer.salesforce.com/docs/metadata-coverage/45/Profile/issues](https://developer.salesforce.com/docs/metadata-coverage/45/Profile/issues)  
94. Salesforce Workflow Rules Explained \- saasguru, accessed May 7, 2025, [https://www.saasguru.co/salesforce-workflow-rules/](https://www.saasguru.co/salesforce-workflow-rules/)  
95. Step 3: Retrieve Components with Metadata API \- Salesforce Developers, accessed May 7, 2025, [https://developer.salesforce.com/docs/atlas.en-us.api\_meta.meta/api\_meta/meta\_quickstart\_retrieve\_use\_retrieve.htm](https://developer.salesforce.com/docs/atlas.en-us.api_meta.meta/api_meta/meta_quickstart_retrieve_use_retrieve.htm)  
96. Salesforce security best practices for business | NordLayer Blog, accessed May 7, 2025, [https://nordlayer.com/blog/salesforce-security-best-practices/](https://nordlayer.com/blog/salesforce-security-best-practices/)