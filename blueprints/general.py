"""
General Blueprint

This module provides general utility functions and endpoints.

Best practices implemented:
- Proper error handling and logging
- Centralized configuration
- Health check endpoint
"""

import logging
import azure.functions as func
import json
import platform
import os
from datetime import datetime

# Import shared modules
from shared.azure_services import is_local_dev
from shared.api_utils import create_json_response, handle_api_exception

# Configure module-level logger
logger = logging.getLogger(__name__)

# Create blueprint
bp = func.Blueprint()

# health_check function removed as requested

@bp.route(route="info")
def system_info(req: func.HttpRequest) -> func.HttpResponse:
    """
    System information endpoint

    Args:
        req: HTTP request

    Returns:
        func.HttpResponse: HTTP response with system information
    """
    logger.info('Processing system info request...')

    try:
        # Collect detailed system information
        system_info = {
            "timestamp": datetime.now().isoformat(),
            "environment": "development" if is_local_dev() else "production",
            "python": {
                "version": platform.python_version(),
                "implementation": platform.python_implementation(),
                "compiler": platform.python_compiler(),
                "build": platform.python_build()
            },
            "system": {
                "platform": platform.platform(),
                "system": platform.system(),
                "release": platform.release(),
                "version": platform.version(),
                "machine": platform.machine(),
                "processor": platform.processor(),
                "node": platform.node()
            },
            "environment_variables": {
                key: value for key, value in os.environ.items()
                if not key.lower() in ["key", "secret", "password", "token", "connection"]
            }
        }

        return create_json_response(
            data=system_info,
            message="System information retrieved successfully",
            status_code=200
        )
    except Exception as e:
        return handle_api_exception(e, status_code=500)


