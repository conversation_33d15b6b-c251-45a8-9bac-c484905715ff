import azure.functions as func
import json
from shared.data_access import TableStorageRepository
from datetime import datetime

def get_profiles_permissions(req: func.HttpRequest) -> func.HttpResponse:
    org_id = req.route_params.get("org_id")
    execution_log_id = req.params.get("execution_log_id")
    if not org_id:
        return func.HttpResponse(
            json.dumps({"success": False, "message": "org_id is required"}),
            mimetype="application/json",
            status_code=400
        )
    if not execution_log_id:
        # Fallback to current logic if not provided
        task_repo = TableStorageRepository(table_name="TaskStatus")
        filter_query = f"PartitionKey eq '{org_id}' and TaskType eq 'profiles_permission_sets'"
        tasks = task_repo.query_entities(filter_query)
        completed_tasks = [t for t in tasks if t.get("Status") == "completed"]
        if not completed_tasks:
            return func.HttpResponse(
                json.dumps({"status": "processing", "message": "Profiles and permissions scan is still running."}),
                mimetype="application/json",
                status_code=200
            )
        latest_task = sorted(completed_tasks, key=lambda t: t.get("CreatedAt", ""), reverse=True)[0]
        execution_log_id = latest_task.get("ExecutionLogId")
    # Now use execution_log_id to fetch results
    policies_repo = TableStorageRepository(table_name="PoliciesResult")
    assignment_repo = TableStorageRepository(table_name="ProfileAssignmentCount")
    policies = policies_repo.query_entities(f"PartitionKey eq '{org_id}' and TaskStatusId eq '{execution_log_id}'")
    assignments = assignment_repo.query_entities(f"PartitionKey eq '{org_id}' and TaskStatusId eq '{execution_log_id}'")
    # Separate profile and permission set assignment counts
    profile_assignments = [a for a in assignments if a.get('Type') == 'ProfilePermissions']
    permissionset_assignments = [a for a in assignments if a.get('Type') == 'ProfilePermissionSetAssignment']
    # Collect all unique permission setting names for dynamic columns
    import json as _json
    all_settings = set()
    for p in policies:
        try:
            for setting in _json.loads(p.get('OrgValue', '[]')):
                if setting.get('SalesforceSetting'):
                    all_settings.add(setting.get('SalesforceSetting'))
        except Exception:
            pass
    return func.HttpResponse(
        json.dumps({
            "status": "completed",
            "settings": sorted(all_settings),
            "policies": [p for p in policies],
            "profileAssignments": profile_assignments,
            "permissionSetAssignments": permissionset_assignments
        }),
        mimetype="application/json",
        status_code=200
    ) 