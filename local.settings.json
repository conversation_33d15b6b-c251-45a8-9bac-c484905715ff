{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;QueueEndpoint=http://127.0.0.1:10001/devstoreaccount1;TableEndpoint=http://127.0.0.1:10002/devstoreaccount1;", "AzureStorageConnectionString": "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;QueueEndpoint=http://127.0.0.1:10001/devstoreaccount1;TableEndpoint=http://127.0.0.1:10002/devstoreaccount1;", "FUNCTIONS_WORKER_RUNTIME": "python", "AzureWebJobsFeatureFlags": "EnableWorkerIndexing", "KEY_VAULT_URL": "https://akv-atomsec-dev.vault.azure.net/", "USE_LOCAL_STORAGE": "true", "IS_LOCAL_DEV": "true", "AZURE_AD_CLIENT_ID": "2d313c1a-d62d-492c-869e-cf8cb9258204", "AZURE_AD_CLIENT_SECRET": "****************************************", "AZURE_AD_TENANT_ID": "41b676db-bf6f-46ae-a354-a83a1362533f", "AZURE_AD_REDIRECT_URI": "http://localhost:3000", "JWT_SECRET": "dev_secret_key_do_not_use_in_production", "AZURE_STORAGE_CONNECTION_STRING": "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;QueueEndpoint=http://127.0.0.1:10001/devstoreaccount1;TableEndpoint=http://127.0.0.1:10002/devstoreaccount1;", "SQL_CONNECTION_STRING": "", "FRONTEND_URL": "http://localhost:3000"}, "Host": {"CORS": "http://localhost:3000", "CORSCredentials": true}}