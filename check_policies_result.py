"""
Check PoliciesResult Table

This script checks if the PoliciesResult table exists in Azurite and lists its contents.
"""

import os
import sys
import logging
from azure.data.tables import TableServiceClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('check_policies_result')

def check_policies_result_table():
    """Check if PoliciesResult table exists and list its contents"""
    logger.info("Checking PoliciesResult table...")
    
    # Get connection string from environment variable or use default
    connection_string = os.environ.get(
        "AzureWebJobsStorage",
        "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;TableEndpoint=http://127.0.0.1:10002/devstoreaccount1;"
    )
    
    logger.info(f"Using connection string: {connection_string}")
    
    try:
        # Create table service client
        table_service = TableServiceClient.from_connection_string(connection_string)
        logger.info("Successfully created table service client")
        
        # List tables
        tables = list(table_service.list_tables())
        table_names = [table.name for table in tables]
        logger.info(f"Available tables: {table_names}")
        
        # Check if PoliciesResult table exists
        if "PoliciesResult" not in table_names:
            logger.error("PoliciesResult table does not exist!")
            return False
        
        # Get table client
        table_client = table_service.get_table_client("PoliciesResult")
        logger.info("Successfully got PoliciesResult table client")
        
        # Query all entities
        entities = list(table_client.query_entities(""))
        logger.info(f"Found {len(entities)} entities in PoliciesResult table")
        
        # Print entities
        for entity in entities:
            logger.info(f"Entity: {entity}")
        
        return True
    except Exception as e:
        logger.error(f"Error checking PoliciesResult table: {str(e)}")
        return False

if __name__ == "__main__":
    success = check_policies_result_table()
    if success:
        logger.info("PoliciesResult table check completed successfully")
    else:
        logger.error("PoliciesResult table check failed")
        sys.exit(1) 