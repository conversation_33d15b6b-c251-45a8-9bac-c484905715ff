# Legacy Code Examples

This directory contains legacy code and examples that are not actively used in the current application but are kept for reference.

## WrapperFunction

This is an example of integrating FastAPI with Azure Functions. It demonstrates how to use the ASGI integration to run a FastAPI application within Azure Functions.

To use this in the main application:

1. Uncomment the following lines in `function_app.py`:

```python
# from WrapperFunction import app as fastapi_app
# app = func.AsgiFunctionApp(app=fastapi_app, http_auth_level=func.AuthLevel.ANONYMOUS)
```

2. Comment out the existing FunctionApp definition:

```python
# app = func.FunctionApp(http_auth_level=func.AuthLevel.ANONYMOUS)
```

Note that you cannot use both the standard FunctionApp and AsgiFunctionApp simultaneously.
