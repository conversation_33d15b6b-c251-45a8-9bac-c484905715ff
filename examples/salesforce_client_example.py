"""
Salesforce Client Example

This module demonstrates how to use the SalesforceClient and utility functions.
"""

import logging
import os
import sys
import json
from typing import Dict, Any, Optional

# Add parent directory to path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import shared modules
from shared.salesforce_client import SalesforceClient
from shared.salesforce_utils import (
    get_salesforce_client,
    execute_salesforce_query,
    execute_salesforce_tooling_query,
    get_salesforce_access_token,
    test_salesforce_connection
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def example_direct_client_usage():
    """Example of using the SalesforceClient directly"""
    logger.info("Example: Using SalesforceClient directly")
    
    # Get credentials from environment variables
    client_id = os.environ.get("SALESFORCE_CLIENT_ID")
    client_secret = os.environ.get("SALESFORCE_CLIENT_SECRET")
    instance_url = os.environ.get("SALESFORCE_INSTANCE_URL")
    is_sandbox = os.environ.get("SALESFORCE_IS_SANDBOX", "false").lower() == "true"
    
    if not client_id or not client_secret or not instance_url:
        logger.error("Missing required environment variables")
        return
    
    # Create client
    client = SalesforceClient(
        instance_url=instance_url,
        client_id=client_id,
        client_secret=client_secret,
        is_sandbox=is_sandbox
    )
    
    # Test connection
    success, error_message, connection_details = client.test_client_credentials_flow()
    
    if not success:
        logger.error(f"Failed to connect to Salesforce: {error_message}")
        return
    
    logger.info(f"Successfully connected to Salesforce: {connection_details.get('instance_url')}")
    
    # Execute a query
    query_result = client.query("SELECT Id, Name FROM User LIMIT 5")
    
    if not query_result:
        logger.error("Failed to execute query")
        return
    
    # Print results
    logger.info(f"Query returned {query_result.get('totalSize')} records")
    for record in query_result.get('records', []):
        logger.info(f"User: {record.get('Name')} (ID: {record.get('Id')})")
    
    # Execute a tooling query
    tooling_query_result = client.tooling_query("SELECT Id, Name FROM Profile LIMIT 5")
    
    if not tooling_query_result:
        logger.error("Failed to execute tooling query")
        return
    
    # Print results
    logger.info(f"Tooling query returned {tooling_query_result.get('totalSize')} records")
    for record in tooling_query_result.get('records', []):
        logger.info(f"Profile: {record.get('Name')} (ID: {record.get('Id')})")


def example_utility_functions():
    """Example of using the utility functions"""
    logger.info("Example: Using utility functions")
    
    # Get credentials from environment variables
    client_id = os.environ.get("SALESFORCE_CLIENT_ID")
    client_secret = os.environ.get("SALESFORCE_CLIENT_SECRET")
    instance_url = os.environ.get("SALESFORCE_INSTANCE_URL")
    is_sandbox = os.environ.get("SALESFORCE_IS_SANDBOX", "false").lower() == "true"
    
    if not client_id or not client_secret or not instance_url:
        logger.error("Missing required environment variables")
        return
    
    # Test connection
    success, error_message, connection_details = test_salesforce_connection(
        client_id=client_id,
        client_secret=client_secret,
        tenant_url=instance_url,
        is_sandbox=is_sandbox
    )
    
    if not success:
        logger.error(f"Failed to connect to Salesforce: {error_message}")
        return
    
    logger.info(f"Successfully connected to Salesforce: {connection_details.get('instance_url')}")
    
    # Get access token
    access_token = connection_details.get('access_token')
    
    # Execute a query using the utility function
    query_result = execute_salesforce_query(
        query="SELECT Id, Name FROM User LIMIT 5",
        access_token=access_token,
        instance_url=instance_url
    )
    
    if not query_result:
        logger.error("Failed to execute query")
        return
    
    # Print results
    logger.info(f"Query returned {query_result.get('totalSize')} records")
    for record in query_result.get('records', []):
        logger.info(f"User: {record.get('Name')} (ID: {record.get('Id')})")
    
    # Execute a tooling query using the utility function
    tooling_query_result = execute_salesforce_tooling_query(
        query="SELECT Id, Name FROM Profile LIMIT 5",
        access_token=access_token,
        instance_url=instance_url
    )
    
    if not tooling_query_result:
        logger.error("Failed to execute tooling query")
        return
    
    # Print results
    logger.info(f"Tooling query returned {tooling_query_result.get('totalSize')} records")
    for record in tooling_query_result.get('records', []):
        logger.info(f"Profile: {record.get('Name')} (ID: {record.get('Id')})")


if __name__ == "__main__":
    # Run examples
    example_direct_client_usage()
    print("\n" + "-" * 80 + "\n")
    example_utility_functions()
