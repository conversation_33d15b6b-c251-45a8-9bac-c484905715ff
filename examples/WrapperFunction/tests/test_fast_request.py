import pytest
from fastapi.testclient import TestClient

from WrapperFunction import app

client = TestClient(app)


def replace_do_something():
    raise Exception()
    return


def test_read_main(monkeypatch: pytest.MonkeyPatch):
    response = client.get("/hello/x")
    assert response.status_code == 200
    assert response.json() == {"name": "x"}


""" def test_read_main_with_error(monkeypatch: pytest.MonkeyPatch):
    monkeypatch.setattr("app.do_something", replace_do_something)
    # Here we replace any reference to do_something 
    # with replace_do_something. Note the 'app.' prefix!
    
    response = client.get("/myroute")
    assert response.status_code == 400
    assert response.json() == {"detail": "something went wrong"} """