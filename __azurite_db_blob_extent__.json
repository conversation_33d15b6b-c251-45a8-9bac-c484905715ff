{"filename": "/Users/<USER>/Downloads/live-atomsec/atomsec-func-sfdc/__azurite_db_blob_extent__.json", "collections": [{"name": "$EXTENTS_COLLECTION$", "data": [{"id": "d9ffc153-8841-4df6-a517-afe5aca01ed9", "locationId": "<PERSON><PERSON><PERSON>", "path": "d9ffc153-8841-4df6-a517-afe5aca01ed9", "size": 5084, "lastModifiedInMS": 1744549078599, "meta": {"revision": 0, "created": 1744549078600, "version": 0}, "$loki": 1}, {"id": "6bbf2f80-fbd3-4dde-8985-589fbf7352d7", "locationId": "<PERSON><PERSON><PERSON>", "path": "6bbf2f80-fbd3-4dde-8985-589fbf7352d7", "size": 1094, "lastModifiedInMS": 1745689796382, "meta": {"revision": 2, "created": 1745689796382, "version": 0, "updated": 1745690985620}, "$loki": 2, "LastModifyInMS": 1745690985620}], "idIndex": [1, 2], "binaryIndices": {"id": {"name": "id", "dirty": false, "values": [1, 0]}}, "constraints": null, "uniqueNames": [], "transforms": {}, "objType": "$EXTENTS_COLLECTION$", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 2, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}], "databaseVersion": 1.5, "engineVersion": 1.5, "autosave": true, "autosaveInterval": 5000, "autosaveHandle": null, "throttledSaves": true, "options": {"persistenceMethod": "fs", "autosave": true, "autosaveInterval": 5000, "serializationMethod": "normal", "destructureDelimiter": "$<\n"}, "persistenceMethod": "fs", "persistenceAdapter": null, "verbose": false, "events": {"init": [null], "loaded": [], "flushChanges": [], "close": [], "changes": [], "warning": []}, "ENV": "NODEJS"}