import azure.functions as func
import logging
import json
from blueprints.integration import test_connection

def main(req: func.HttpRequest) -> func.HttpResponse:
    """
    Azure Functions HTTP trigger function for test-connection endpoint
    
    This function is a direct handler for the test-connection endpoint.
    It calls the test_connection function from the integration blueprint.
    
    Args:
        req: HTTP request
        
    Returns:
        func.HttpResponse: HTTP response
    """
    logging.info('Processing test-connection request directly...')
    
    # Call the test_connection function directly
    return test_connection(req)
