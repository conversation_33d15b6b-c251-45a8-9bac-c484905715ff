<?xml version='1.0' encoding='utf-8'?>
<BestPractices>
    <UserType name="BLANK">
        <Practice>
            <SalesforceSetting>ForceTwoFactor</SalesforceSetting>
            <StandardValue>TRUE</StandardValue>
            <Description>Require users to log in with multi-factor authentication.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>TwoFactorApi</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allow users to bypass MFA for API logins.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>BypassMFAForUiLogins</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows the user to skip MFA challenges in the UI.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
    </UserType>
    <UserType name="Salesforce Integration">
        <Practice>
            <SalesforceSetting>ForceTwoFactor</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Require users to log in with multi-factor authentication.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>TwoFactorApi</SalesforceSetting>
            <StandardValue>TRUE</StandardValue>
            <Description>Allow users to bypass MFA for API logins.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>BypassMFAForUiLogins</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows the user to skip MFA challenges in the UI.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
    </UserType>
    <UserType name="Salesforce">
        <Practice>
            <SalesforceSetting>ForceTwoFactor</SalesforceSetting>
            <StandardValue>TRUE</StandardValue>
            <Description>Require users to log in with multi-factor authentication.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>TwoFactorApi</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allow users to bypass MFA for API logins.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>BypassMFAForUiLogins</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows the user to skip MFA challenges in the UI.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
    </UserType>
    <UserType name="Guest User License">
        <Practice>
            <SalesforceSetting>ForceTwoFactor</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Require users to log in with multi-factor authentication.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>TwoFactorApi</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allow users to bypass MFA for API logins.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>BypassMFAForUiLogins</SalesforceSetting>
            <StandardValue>TRUE</StandardValue>
            <Description>Allows the user to skip MFA challenges in the UI.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
    </UserType>
    <UserType name="Customer Community Login">
        <Practice>
            <SalesforceSetting>ForceTwoFactor</SalesforceSetting>
            <StandardValue>TRUE</StandardValue>
            <Description>Require users to log in with multi-factor authentication.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>TwoFactorApi</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allow users to bypass MFA for API logins.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>BypassMFAForUiLogins</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows the user to skip MFA challenges in the UI.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
    </UserType>
</BestPractices>