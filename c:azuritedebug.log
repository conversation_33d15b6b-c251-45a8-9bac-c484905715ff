2025-05-15T05:41:25.657Z 	 info: Azurite Blob service is starting on 127.0.0.1:10000
2025-05-15T05:41:25.659Z 	 info: AccountDataStore:init() Refresh accounts from environment variable AZURITE_ACCOUNTS with value undefined
2025-05-15T05:41:25.659Z 	 info: AccountDataStore:init() Fallback to default emulator account devstoreaccount1.
2025-05-15T05:41:25.668Z 	 info: BlobGCManager:start() Starting BlobGCManager. Set status to Initializing.
2025-05-15T05:41:25.668Z 	 info: BlobGCManager:start() Trigger mark and sweep loop. Set status to Running.
2025-05-15T05:41:25.668Z 	 info: BlobGCManager:markSweepLoop() Start next mark and sweep.
2025-05-15T05:41:25.669Z 	 info: BlobGCManager:markSweep() Get all extents.
2025-05-15T05:41:25.669Z 	 info: BlobGCManager:start() BlobGCManager successfully started.
2025-05-15T05:41:25.672Z 	 info: BlobGCManager:markSweep() Got 0 extents.
2025-05-15T05:41:25.672Z 	 info: BlobGCManager:markSweep() Get referred extents.
2025-05-15T05:41:25.673Z 	 info: BlobGCManager:markSweep() Got referred extents, unreferenced extents count is 0.
2025-05-15T05:41:25.673Z 	 info: BlobGCManager:markSweepLoop() Mark and sweep finished, taken 5ms.
2025-05-15T05:41:25.673Z 	 info: BlobGCManager:markSweepLoop() Sleep for 600000ms.
2025-05-15T05:41:25.678Z 	 info: Azurite Blob service successfully listens on http://127.0.0.1:10000
2025-05-15T05:41:25.678Z 	 info: Azurite Queue service is starting on 127.0.0.1:10001
2025-05-15T05:41:25.678Z 	 info: AccountDataStore:init() Refresh accounts from environment variable AZURITE_ACCOUNTS with value undefined
2025-05-15T05:41:25.678Z 	 info: AccountDataStore:init() Fallback to default emulator account devstoreaccount1.
2025-05-15T05:41:25.684Z 	 info: QueueGCManager:start() Starting QueueGCManager, set status to Initializing
2025-05-15T05:41:25.684Z 	 info: QueueGCManager:start() Trigger mark and sweep loop, set status to Running.
2025-05-15T05:41:25.684Z 	 info: QueueGCManager:markSweepLoop() Start new mark and sweep.
2025-05-15T05:41:25.684Z 	 info: QueueGCManger:markSweep() Get all extents.
2025-05-15T05:41:25.685Z 	 info: QueueGCManager:start() QueueGCManager successfully started.
2025-05-15T05:41:25.685Z 	 info: QueueGCManager:marksweep() Get 0 extents.
2025-05-15T05:41:25.685Z 	 info: QueueGCManager:markSweep() Get referred extents, then remove from allExtents.
2025-05-15T05:41:25.685Z 	 info: QueueGCManager:markSweep() Got referred extents, unreferenced extents count is 0.
2025-05-15T05:41:25.685Z 	 info: QueueGCManager:markSweepLoop() Mark and sweep finished, take 1ms.
2025-05-15T05:41:25.685Z 	 info: QueueGCManager:markSweepLoop() Sleep for 60000
2025-05-15T05:41:25.686Z 	 info: Azurite Queue service successfully listens on http://127.0.0.1:10001
2025-05-15T05:41:25.687Z 	 info: Azurite Table service is starting on 127.0.0.1:10002
2025-05-15T05:41:25.687Z 	 info: AccountDataStore:init() Refresh accounts from environment variable AZURITE_ACCOUNTS with value undefined
2025-05-15T05:41:25.687Z 	 info: AccountDataStore:init() Fallback to default emulator account devstoreaccount1.
2025-05-15T05:41:25.690Z 	 info: Azurite Table service successfully listens on http://127.0.0.1:10002
2025-05-15T05:41:25.691Z 	 info: InstaceID fd4f4993-ddcb-436f-be17-90b595e00e0b, SessionID d741f0ca-2b56-405c-a6ea-67e46f1a5910.
2025-05-15T05:41:25.734Z 	 info: Telemetry initialize successfully.
2025-05-15T05:41:25.738Z 	 verbose: Send start telemetry
2025-05-15T05:41:49.816Z a1eb30df-287f-473e-ad31-a19bb8d3d6e5 info: QueueStorageContextMiddleware: RequestMethod=GET RequestURL=http://127.0.0.1/devstoreaccount1/task-queue-low?comp=metadata RequestHeaders:{"host":"127.0.0.1:10001","x-ms-version":"2025-01-05","accept":"application/xml","x-ms-client-request-id":"b19bee52-f2b1-4c1c-a360-9da67a915546","x-ms-return-client-request-id":"true","user-agent":"azsdk-net-Storage.Queues/12.21.0 (.NET 8.0.13; Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:53:26 PDT 2025; root:xnu-11417.121.6~2/RELEASE_X86_64)","x-ms-date":"Thu, 15 May 2025 05:41:49 GMT","authorization":"SharedKey devstoreaccount1:RJSt6rdcnFX8JE2Xq0ZCRY4/R6pNtMUWwzQCld45dDc="} ClientIP=127.0.0.1 Protocol=http HTTPVersion=1.1
2025-05-15T05:41:49.817Z a1eb30df-287f-473e-ad31-a19bb8d3d6e5 info: QueueStorageContextMiddleware: Account=devstoreaccount1 Queue=task-queue-low Message=undefined MessageId=undefined
2025-05-15T05:41:49.817Z a1eb30df-287f-473e-ad31-a19bb8d3d6e5 verbose: DispatchMiddleware: Dispatching request...
2025-05-15T05:41:49.819Z a1eb30df-287f-473e-ad31-a19bb8d3d6e5 info: DispatchMiddleware: Operation=Queue_GetProperties
2025-05-15T05:41:49.819Z a1eb30df-287f-473e-ad31-a19bb8d3d6e5 verbose: AuthenticationMiddlewareFactory:createAuthenticationMiddleware() Validating authentications.
2025-05-15T05:41:49.819Z a1eb30df-287f-473e-ad31-a19bb8d3d6e5 info: QueueSharedKeyAuthenticator:validate() Start validation against account shared key authentication.
2025-05-15T05:41:49.834Z a1eb30df-287f-473e-ad31-a19bb8d3d6e5 info: QueueSharedKeyAuthenticator:validate() [STRING TO SIGN]:"GET\n\n\n\n\n\n\n\n\n\n\n\nx-ms-client-request-id:b19bee52-f2b1-4c1c-a360-9da67a915546\nx-ms-date:Thu, 15 May 2025 05:41:49 GMT\nx-ms-return-client-request-id:true\nx-ms-version:2025-01-05\n/devstoreaccount1/devstoreaccount1/task-queue-low\ncomp:metadata"
2025-05-15T05:41:49.834Z a1eb30df-287f-473e-ad31-a19bb8d3d6e5 info: QueueSharedKeyAuthenticator:validate() Calculated authentication header based on key1: devstoreaccount1:RJSt6rdcnFX8JE2Xq0ZCRY4/R6pNtMUWwzQCld45dDc=
2025-05-15T05:41:49.834Z a1eb30df-287f-473e-ad31-a19bb8d3d6e5 info: QueueSharedKeyAuthenticator:validate() Signature 1 matched.
2025-05-15T05:41:49.835Z a1eb30df-287f-473e-ad31-a19bb8d3d6e5 verbose: DeserializerMiddleware: Start deserializing...
2025-05-15T05:41:49.836Z a1eb30df-287f-473e-ad31-a19bb8d3d6e5 info: HandlerMiddleware: DeserializedParameters={"options":{"requestId":"b19bee52-f2b1-4c1c-a360-9da67a915546"},"comp":"metadata","version":"2025-01-05"}
2025-05-15T05:41:49.843Z a1eb30df-287f-473e-ad31-a19bb8d3d6e5 error: ErrorMiddleware: Received a MiddlewareError, fill error information to HTTP response
2025-05-15T05:41:49.843Z a1eb30df-287f-473e-ad31-a19bb8d3d6e5 error: ErrorMiddleware: ErrorName=StorageError ErrorMessage=The specified queue does not exist.  ErrorHTTPStatusCode=404 ErrorHTTPStatusMessage=The specified queue does not exist. ErrorHTTPHeaders={"x-ms-error-code":"QueueNotFound","x-ms-request-id":"a1eb30df-287f-473e-ad31-a19bb8d3d6e5","x-ms-version":"2025-05-05"} ErrorHTTPBody="<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:a1eb30df-287f-473e-ad31-a19bb8d3d6e5\nTime:2025-05-15T05:41:49.837Z</Message>\n</Error>" ErrorStack="StorageError: The specified queue does not exist.\n    at StorageErrorFactory.getQueueNotFound (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/errors/StorageErrorFactory.js:110:16)\n    at LokiQueueMetadataStore.getQueue (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/persistence/LokiQueueMetadataStore.js:247:49)\n    at QueueHandler.getProperties (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/handlers/QueueHandler.js:81:48)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/middleware/HandlerMiddlewareFactory.js:58:18\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/ExpressMiddlewareFactory.js:77:63\n    at Layer.handle [as handle_request] (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:280:10)"
2025-05-15T05:41:49.843Z a1eb30df-287f-473e-ad31-a19bb8d3d6e5 error: ErrorMiddleware: Set HTTP code: 404
2025-05-15T05:41:49.843Z a1eb30df-287f-473e-ad31-a19bb8d3d6e5 error: ErrorMiddleware: Set HTTP status message: The specified queue does not exist.
2025-05-15T05:41:49.844Z a1eb30df-287f-473e-ad31-a19bb8d3d6e5 error: ErrorMiddleware: Set HTTP Header: x-ms-error-code=QueueNotFound
2025-05-15T05:41:49.844Z a1eb30df-287f-473e-ad31-a19bb8d3d6e5 error: ErrorMiddleware: Set HTTP Header: x-ms-request-id=a1eb30df-287f-473e-ad31-a19bb8d3d6e5
2025-05-15T05:41:49.844Z a1eb30df-287f-473e-ad31-a19bb8d3d6e5 error: ErrorMiddleware: Set HTTP Header: x-ms-version=2025-05-05
2025-05-15T05:41:49.844Z a1eb30df-287f-473e-ad31-a19bb8d3d6e5 error: ErrorMiddleware: Set content type: application/xml
2025-05-15T05:41:49.844Z a1eb30df-287f-473e-ad31-a19bb8d3d6e5 error: ErrorMiddleware: Set HTTP body: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:a1eb30df-287f-473e-ad31-a19bb8d3d6e5\nTime:2025-05-15T05:41:49.837Z</Message>\n</Error>"
2025-05-15T05:41:49.845Z a1eb30df-287f-473e-ad31-a19bb8d3d6e5 verbose: Send Queue telemetry: Q_Queue_GetProperties
2025-05-15T05:41:49.846Z a1eb30df-287f-473e-ad31-a19bb8d3d6e5 info: EndMiddleware: End response. TotalTimeInMS=29 StatusCode=404 StatusMessage=The specified queue does not exist. Headers={"server":"Azurite-Queue/3.34.0","x-ms-error-code":"QueueNotFound","x-ms-request-id":"a1eb30df-287f-473e-ad31-a19bb8d3d6e5","x-ms-version":"2025-05-05","content-type":"application/xml"}
2025-05-15T05:41:49.847Z b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba info: QueueStorageContextMiddleware: RequestMethod=GET RequestURL=http://127.0.0.1/devstoreaccount1/task-queue-high?comp=metadata RequestHeaders:{"host":"127.0.0.1:10001","x-ms-version":"2025-01-05","accept":"application/xml","x-ms-client-request-id":"ab8281aa-2642-40ea-be14-63dc9b9f8c39","x-ms-return-client-request-id":"true","user-agent":"azsdk-net-Storage.Queues/12.21.0 (.NET 8.0.13; Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:53:26 PDT 2025; root:xnu-11417.121.6~2/RELEASE_X86_64)","x-ms-date":"Thu, 15 May 2025 05:41:49 GMT","authorization":"SharedKey devstoreaccount1:iO/sf7v4QY1HGu5wJCIq4ilm2RUdGZIPrnw+PZX309k="} ClientIP=127.0.0.1 Protocol=http HTTPVersion=1.1
2025-05-15T05:41:49.847Z b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba info: QueueStorageContextMiddleware: Account=devstoreaccount1 Queue=task-queue-high Message=undefined MessageId=undefined
2025-05-15T05:41:49.848Z b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba verbose: DispatchMiddleware: Dispatching request...
2025-05-15T05:41:49.848Z b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba info: DispatchMiddleware: Operation=Queue_GetProperties
2025-05-15T05:41:49.848Z b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba verbose: AuthenticationMiddlewareFactory:createAuthenticationMiddleware() Validating authentications.
2025-05-15T05:41:49.848Z b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba info: QueueSharedKeyAuthenticator:validate() Start validation against account shared key authentication.
2025-05-15T05:41:49.848Z b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba info: QueueSharedKeyAuthenticator:validate() [STRING TO SIGN]:"GET\n\n\n\n\n\n\n\n\n\n\n\nx-ms-client-request-id:ab8281aa-2642-40ea-be14-63dc9b9f8c39\nx-ms-date:Thu, 15 May 2025 05:41:49 GMT\nx-ms-return-client-request-id:true\nx-ms-version:2025-01-05\n/devstoreaccount1/devstoreaccount1/task-queue-high\ncomp:metadata"
2025-05-15T05:41:49.849Z b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba info: QueueSharedKeyAuthenticator:validate() Calculated authentication header based on key1: devstoreaccount1:iO/sf7v4QY1HGu5wJCIq4ilm2RUdGZIPrnw+PZX309k=
2025-05-15T05:41:49.849Z b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba info: QueueSharedKeyAuthenticator:validate() Signature 1 matched.
2025-05-15T05:41:49.849Z b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba verbose: DeserializerMiddleware: Start deserializing...
2025-05-15T05:41:49.849Z b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba info: HandlerMiddleware: DeserializedParameters={"options":{"requestId":"ab8281aa-2642-40ea-be14-63dc9b9f8c39"},"comp":"metadata","version":"2025-01-05"}
2025-05-15T05:41:49.849Z b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba error: ErrorMiddleware: Received a MiddlewareError, fill error information to HTTP response
2025-05-15T05:41:49.850Z b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba error: ErrorMiddleware: ErrorName=StorageError ErrorMessage=The specified queue does not exist.  ErrorHTTPStatusCode=404 ErrorHTTPStatusMessage=The specified queue does not exist. ErrorHTTPHeaders={"x-ms-error-code":"QueueNotFound","x-ms-request-id":"b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba","x-ms-version":"2025-05-05"} ErrorHTTPBody="<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba\nTime:2025-05-15T05:41:49.849Z</Message>\n</Error>" ErrorStack="StorageError: The specified queue does not exist.\n    at StorageErrorFactory.getQueueNotFound (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/errors/StorageErrorFactory.js:110:16)\n    at LokiQueueMetadataStore.getQueue (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/persistence/LokiQueueMetadataStore.js:247:49)\n    at QueueHandler.getProperties (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/handlers/QueueHandler.js:81:48)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/middleware/HandlerMiddlewareFactory.js:58:18\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/ExpressMiddlewareFactory.js:77:63\n    at Layer.handle [as handle_request] (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:280:10)"
2025-05-15T05:41:49.850Z b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba error: ErrorMiddleware: Set HTTP code: 404
2025-05-15T05:41:49.850Z b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba error: ErrorMiddleware: Set HTTP status message: The specified queue does not exist.
2025-05-15T05:41:49.850Z b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba error: ErrorMiddleware: Set HTTP Header: x-ms-error-code=QueueNotFound
2025-05-15T05:41:49.850Z b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba error: ErrorMiddleware: Set HTTP Header: x-ms-request-id=b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba
2025-05-15T05:41:49.850Z b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba error: ErrorMiddleware: Set HTTP Header: x-ms-version=2025-05-05
2025-05-15T05:41:49.850Z b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba error: ErrorMiddleware: Set content type: application/xml
2025-05-15T05:41:49.850Z b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba error: ErrorMiddleware: Set HTTP body: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba\nTime:2025-05-15T05:41:49.849Z</Message>\n</Error>"
2025-05-15T05:41:49.850Z b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba verbose: Send Queue telemetry: Q_Queue_GetProperties
2025-05-15T05:41:49.850Z b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba info: EndMiddleware: End response. TotalTimeInMS=3 StatusCode=404 StatusMessage=The specified queue does not exist. Headers={"server":"Azurite-Queue/3.34.0","x-ms-error-code":"QueueNotFound","x-ms-request-id":"b6e748b7-9238-4d6b-bd6d-1ad1f48a9cba","x-ms-version":"2025-05-05","content-type":"application/xml"}
2025-05-15T05:41:49.851Z f83a51b2-ac40-4e6d-abf2-aa1278fb9690 info: QueueStorageContextMiddleware: RequestMethod=GET RequestURL=http://127.0.0.1/devstoreaccount1/task-queue-medium?comp=metadata RequestHeaders:{"host":"127.0.0.1:10001","x-ms-version":"2025-01-05","accept":"application/xml","x-ms-client-request-id":"51eb7d87-3ca5-4b3d-8e81-5202239cac4a","x-ms-return-client-request-id":"true","user-agent":"azsdk-net-Storage.Queues/12.21.0 (.NET 8.0.13; Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:53:26 PDT 2025; root:xnu-11417.121.6~2/RELEASE_X86_64)","x-ms-date":"Thu, 15 May 2025 05:41:49 GMT","authorization":"SharedKey devstoreaccount1:KmMPXJGQ/ae4WQe+pihATpuCvij/AEYlt13cce+bSII="} ClientIP=127.0.0.1 Protocol=http HTTPVersion=1.1
2025-05-15T05:41:49.851Z f83a51b2-ac40-4e6d-abf2-aa1278fb9690 info: QueueStorageContextMiddleware: Account=devstoreaccount1 Queue=task-queue-medium Message=undefined MessageId=undefined
2025-05-15T05:41:49.851Z f83a51b2-ac40-4e6d-abf2-aa1278fb9690 verbose: DispatchMiddleware: Dispatching request...
2025-05-15T05:41:49.852Z f83a51b2-ac40-4e6d-abf2-aa1278fb9690 info: DispatchMiddleware: Operation=Queue_GetProperties
2025-05-15T05:41:49.852Z f83a51b2-ac40-4e6d-abf2-aa1278fb9690 verbose: AuthenticationMiddlewareFactory:createAuthenticationMiddleware() Validating authentications.
2025-05-15T05:41:49.852Z f83a51b2-ac40-4e6d-abf2-aa1278fb9690 info: QueueSharedKeyAuthenticator:validate() Start validation against account shared key authentication.
2025-05-15T05:41:49.852Z f83a51b2-ac40-4e6d-abf2-aa1278fb9690 info: QueueSharedKeyAuthenticator:validate() [STRING TO SIGN]:"GET\n\n\n\n\n\n\n\n\n\n\n\nx-ms-client-request-id:51eb7d87-3ca5-4b3d-8e81-5202239cac4a\nx-ms-date:Thu, 15 May 2025 05:41:49 GMT\nx-ms-return-client-request-id:true\nx-ms-version:2025-01-05\n/devstoreaccount1/devstoreaccount1/task-queue-medium\ncomp:metadata"
2025-05-15T05:41:49.852Z f83a51b2-ac40-4e6d-abf2-aa1278fb9690 info: QueueSharedKeyAuthenticator:validate() Calculated authentication header based on key1: devstoreaccount1:KmMPXJGQ/ae4WQe+pihATpuCvij/AEYlt13cce+bSII=
2025-05-15T05:41:49.852Z f83a51b2-ac40-4e6d-abf2-aa1278fb9690 info: QueueSharedKeyAuthenticator:validate() Signature 1 matched.
2025-05-15T05:41:49.852Z f83a51b2-ac40-4e6d-abf2-aa1278fb9690 verbose: DeserializerMiddleware: Start deserializing...
2025-05-15T05:41:49.853Z f83a51b2-ac40-4e6d-abf2-aa1278fb9690 info: HandlerMiddleware: DeserializedParameters={"options":{"requestId":"51eb7d87-3ca5-4b3d-8e81-5202239cac4a"},"comp":"metadata","version":"2025-01-05"}
2025-05-15T05:41:49.853Z f83a51b2-ac40-4e6d-abf2-aa1278fb9690 error: ErrorMiddleware: Received a MiddlewareError, fill error information to HTTP response
2025-05-15T05:41:49.853Z f83a51b2-ac40-4e6d-abf2-aa1278fb9690 error: ErrorMiddleware: ErrorName=StorageError ErrorMessage=The specified queue does not exist.  ErrorHTTPStatusCode=404 ErrorHTTPStatusMessage=The specified queue does not exist. ErrorHTTPHeaders={"x-ms-error-code":"QueueNotFound","x-ms-request-id":"f83a51b2-ac40-4e6d-abf2-aa1278fb9690","x-ms-version":"2025-05-05"} ErrorHTTPBody="<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:f83a51b2-ac40-4e6d-abf2-aa1278fb9690\nTime:2025-05-15T05:41:49.853Z</Message>\n</Error>" ErrorStack="StorageError: The specified queue does not exist.\n    at StorageErrorFactory.getQueueNotFound (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/errors/StorageErrorFactory.js:110:16)\n    at LokiQueueMetadataStore.getQueue (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/persistence/LokiQueueMetadataStore.js:247:49)\n    at QueueHandler.getProperties (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/handlers/QueueHandler.js:81:48)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/middleware/HandlerMiddlewareFactory.js:58:18\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/ExpressMiddlewareFactory.js:77:63\n    at Layer.handle [as handle_request] (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:280:10)"
2025-05-15T05:41:49.853Z f83a51b2-ac40-4e6d-abf2-aa1278fb9690 error: ErrorMiddleware: Set HTTP code: 404
2025-05-15T05:41:49.853Z f83a51b2-ac40-4e6d-abf2-aa1278fb9690 error: ErrorMiddleware: Set HTTP status message: The specified queue does not exist.
2025-05-15T05:41:49.853Z f83a51b2-ac40-4e6d-abf2-aa1278fb9690 error: ErrorMiddleware: Set HTTP Header: x-ms-error-code=QueueNotFound
2025-05-15T05:41:49.853Z f83a51b2-ac40-4e6d-abf2-aa1278fb9690 error: ErrorMiddleware: Set HTTP Header: x-ms-request-id=f83a51b2-ac40-4e6d-abf2-aa1278fb9690
2025-05-15T05:41:49.853Z f83a51b2-ac40-4e6d-abf2-aa1278fb9690 error: ErrorMiddleware: Set HTTP Header: x-ms-version=2025-05-05
2025-05-15T05:41:49.853Z f83a51b2-ac40-4e6d-abf2-aa1278fb9690 error: ErrorMiddleware: Set content type: application/xml
2025-05-15T05:41:49.853Z f83a51b2-ac40-4e6d-abf2-aa1278fb9690 error: ErrorMiddleware: Set HTTP body: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:f83a51b2-ac40-4e6d-abf2-aa1278fb9690\nTime:2025-05-15T05:41:49.853Z</Message>\n</Error>"
2025-05-15T05:41:49.854Z f83a51b2-ac40-4e6d-abf2-aa1278fb9690 verbose: Send Queue telemetry: Q_Queue_GetProperties
2025-05-15T05:41:49.854Z f83a51b2-ac40-4e6d-abf2-aa1278fb9690 info: EndMiddleware: End response. TotalTimeInMS=3 StatusCode=404 StatusMessage=The specified queue does not exist. Headers={"server":"Azurite-Queue/3.34.0","x-ms-error-code":"QueueNotFound","x-ms-request-id":"f83a51b2-ac40-4e6d-abf2-aa1278fb9690","x-ms-version":"2025-05-05","content-type":"application/xml"}
2025-05-15T05:41:49.983Z 0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc info: QueueStorageContextMiddleware: RequestMethod=GET RequestURL=http://127.0.0.1/devstoreaccount1/task-queue-medium?comp=metadata RequestHeaders:{"host":"127.0.0.1:10001","x-ms-version":"2025-01-05","accept":"application/xml","x-ms-client-request-id":"1fb8091c-b0c3-4e77-8770-873f4de658e9","x-ms-return-client-request-id":"true","user-agent":"azsdk-net-Storage.Queues/12.21.0 (.NET 8.0.13; Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:53:26 PDT 2025; root:xnu-11417.121.6~2/RELEASE_X86_64)","x-ms-date":"Thu, 15 May 2025 05:41:49 GMT","authorization":"SharedKey devstoreaccount1:0eND3v1p2QIj2wqtBvmjWbokGh/dNbmu3pzaxeRaPZg="} ClientIP=127.0.0.1 Protocol=http HTTPVersion=1.1
2025-05-15T05:41:49.983Z 0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc info: QueueStorageContextMiddleware: Account=devstoreaccount1 Queue=task-queue-medium Message=undefined MessageId=undefined
2025-05-15T05:41:49.983Z 0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc verbose: DispatchMiddleware: Dispatching request...
2025-05-15T05:41:49.983Z 0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc info: DispatchMiddleware: Operation=Queue_GetProperties
2025-05-15T05:41:49.983Z 0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc verbose: AuthenticationMiddlewareFactory:createAuthenticationMiddleware() Validating authentications.
2025-05-15T05:41:49.983Z 0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc info: QueueSharedKeyAuthenticator:validate() Start validation against account shared key authentication.
2025-05-15T05:41:49.983Z 0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc info: QueueSharedKeyAuthenticator:validate() [STRING TO SIGN]:"GET\n\n\n\n\n\n\n\n\n\n\n\nx-ms-client-request-id:1fb8091c-b0c3-4e77-8770-873f4de658e9\nx-ms-date:Thu, 15 May 2025 05:41:49 GMT\nx-ms-return-client-request-id:true\nx-ms-version:2025-01-05\n/devstoreaccount1/devstoreaccount1/task-queue-medium\ncomp:metadata"
2025-05-15T05:41:49.984Z 0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc info: QueueSharedKeyAuthenticator:validate() Calculated authentication header based on key1: devstoreaccount1:0eND3v1p2QIj2wqtBvmjWbokGh/dNbmu3pzaxeRaPZg=
2025-05-15T05:41:49.984Z 0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc info: QueueSharedKeyAuthenticator:validate() Signature 1 matched.
2025-05-15T05:41:49.984Z 0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc verbose: DeserializerMiddleware: Start deserializing...
2025-05-15T05:41:49.984Z 0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc info: HandlerMiddleware: DeserializedParameters={"options":{"requestId":"1fb8091c-b0c3-4e77-8770-873f4de658e9"},"comp":"metadata","version":"2025-01-05"}
2025-05-15T05:41:49.984Z 0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc error: ErrorMiddleware: Received a MiddlewareError, fill error information to HTTP response
2025-05-15T05:41:49.985Z 0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc error: ErrorMiddleware: ErrorName=StorageError ErrorMessage=The specified queue does not exist.  ErrorHTTPStatusCode=404 ErrorHTTPStatusMessage=The specified queue does not exist. ErrorHTTPHeaders={"x-ms-error-code":"QueueNotFound","x-ms-request-id":"0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc","x-ms-version":"2025-05-05"} ErrorHTTPBody="<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc\nTime:2025-05-15T05:41:49.984Z</Message>\n</Error>" ErrorStack="StorageError: The specified queue does not exist.\n    at StorageErrorFactory.getQueueNotFound (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/errors/StorageErrorFactory.js:110:16)\n    at LokiQueueMetadataStore.getQueue (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/persistence/LokiQueueMetadataStore.js:247:49)\n    at QueueHandler.getProperties (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/handlers/QueueHandler.js:81:48)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/middleware/HandlerMiddlewareFactory.js:58:18\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/ExpressMiddlewareFactory.js:77:63\n    at Layer.handle [as handle_request] (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:280:10)"
2025-05-15T05:41:49.985Z 0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc error: ErrorMiddleware: Set HTTP code: 404
2025-05-15T05:41:49.985Z 0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc error: ErrorMiddleware: Set HTTP status message: The specified queue does not exist.
2025-05-15T05:41:49.985Z 0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc error: ErrorMiddleware: Set HTTP Header: x-ms-error-code=QueueNotFound
2025-05-15T05:41:49.985Z 0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc error: ErrorMiddleware: Set HTTP Header: x-ms-request-id=0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc
2025-05-15T05:41:49.985Z 0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc error: ErrorMiddleware: Set HTTP Header: x-ms-version=2025-05-05
2025-05-15T05:41:49.985Z 0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc error: ErrorMiddleware: Set content type: application/xml
2025-05-15T05:41:49.985Z 0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc error: ErrorMiddleware: Set HTTP body: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc\nTime:2025-05-15T05:41:49.984Z</Message>\n</Error>"
2025-05-15T05:41:49.985Z 0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc verbose: Send Queue telemetry: Q_Queue_GetProperties
2025-05-15T05:41:49.985Z 0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc info: EndMiddleware: End response. TotalTimeInMS=2 StatusCode=404 StatusMessage=The specified queue does not exist. Headers={"server":"Azurite-Queue/3.34.0","x-ms-error-code":"QueueNotFound","x-ms-request-id":"0ef3d180-b36b-4a5e-bbef-a32ec4dfd0cc","x-ms-version":"2025-05-05","content-type":"application/xml"}
2025-05-15T05:41:49.986Z 72486e5a-9d42-4feb-9342-bc4fd6653f41 info: QueueStorageContextMiddleware: RequestMethod=GET RequestURL=http://127.0.0.1/devstoreaccount1/task-queue-low?comp=metadata RequestHeaders:{"host":"127.0.0.1:10001","x-ms-version":"2025-01-05","accept":"application/xml","x-ms-client-request-id":"e2cf8bb9-8dbf-4407-a586-014c3da77e20","x-ms-return-client-request-id":"true","user-agent":"azsdk-net-Storage.Queues/12.21.0 (.NET 8.0.13; Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:53:26 PDT 2025; root:xnu-11417.121.6~2/RELEASE_X86_64)","x-ms-date":"Thu, 15 May 2025 05:41:49 GMT","authorization":"SharedKey devstoreaccount1:A8K5WIfslhZi/IDATmqpp+A0N+nTqDLvG4jsymUSiD8="} ClientIP=127.0.0.1 Protocol=http HTTPVersion=1.1
2025-05-15T05:41:49.986Z 72486e5a-9d42-4feb-9342-bc4fd6653f41 info: QueueStorageContextMiddleware: Account=devstoreaccount1 Queue=task-queue-low Message=undefined MessageId=undefined
2025-05-15T05:41:49.986Z 72486e5a-9d42-4feb-9342-bc4fd6653f41 verbose: DispatchMiddleware: Dispatching request...
2025-05-15T05:41:49.987Z 72486e5a-9d42-4feb-9342-bc4fd6653f41 info: DispatchMiddleware: Operation=Queue_GetProperties
2025-05-15T05:41:49.987Z 72486e5a-9d42-4feb-9342-bc4fd6653f41 verbose: AuthenticationMiddlewareFactory:createAuthenticationMiddleware() Validating authentications.
2025-05-15T05:41:49.987Z 72486e5a-9d42-4feb-9342-bc4fd6653f41 info: QueueSharedKeyAuthenticator:validate() Start validation against account shared key authentication.
2025-05-15T05:41:49.987Z 72486e5a-9d42-4feb-9342-bc4fd6653f41 info: QueueSharedKeyAuthenticator:validate() [STRING TO SIGN]:"GET\n\n\n\n\n\n\n\n\n\n\n\nx-ms-client-request-id:e2cf8bb9-8dbf-4407-a586-014c3da77e20\nx-ms-date:Thu, 15 May 2025 05:41:49 GMT\nx-ms-return-client-request-id:true\nx-ms-version:2025-01-05\n/devstoreaccount1/devstoreaccount1/task-queue-low\ncomp:metadata"
2025-05-15T05:41:49.987Z 72486e5a-9d42-4feb-9342-bc4fd6653f41 info: QueueSharedKeyAuthenticator:validate() Calculated authentication header based on key1: devstoreaccount1:A8K5WIfslhZi/IDATmqpp+A0N+nTqDLvG4jsymUSiD8=
2025-05-15T05:41:49.987Z 72486e5a-9d42-4feb-9342-bc4fd6653f41 info: QueueSharedKeyAuthenticator:validate() Signature 1 matched.
2025-05-15T05:41:49.987Z 72486e5a-9d42-4feb-9342-bc4fd6653f41 verbose: DeserializerMiddleware: Start deserializing...
2025-05-15T05:41:49.987Z 72486e5a-9d42-4feb-9342-bc4fd6653f41 info: HandlerMiddleware: DeserializedParameters={"options":{"requestId":"e2cf8bb9-8dbf-4407-a586-014c3da77e20"},"comp":"metadata","version":"2025-01-05"}
2025-05-15T05:41:49.988Z 72486e5a-9d42-4feb-9342-bc4fd6653f41 error: ErrorMiddleware: Received a MiddlewareError, fill error information to HTTP response
2025-05-15T05:41:49.988Z 72486e5a-9d42-4feb-9342-bc4fd6653f41 error: ErrorMiddleware: ErrorName=StorageError ErrorMessage=The specified queue does not exist.  ErrorHTTPStatusCode=404 ErrorHTTPStatusMessage=The specified queue does not exist. ErrorHTTPHeaders={"x-ms-error-code":"QueueNotFound","x-ms-request-id":"72486e5a-9d42-4feb-9342-bc4fd6653f41","x-ms-version":"2025-05-05"} ErrorHTTPBody="<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:72486e5a-9d42-4feb-9342-bc4fd6653f41\nTime:2025-05-15T05:41:49.987Z</Message>\n</Error>" ErrorStack="StorageError: The specified queue does not exist.\n    at StorageErrorFactory.getQueueNotFound (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/errors/StorageErrorFactory.js:110:16)\n    at LokiQueueMetadataStore.getQueue (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/persistence/LokiQueueMetadataStore.js:247:49)\n    at QueueHandler.getProperties (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/handlers/QueueHandler.js:81:48)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/middleware/HandlerMiddlewareFactory.js:58:18\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/ExpressMiddlewareFactory.js:77:63\n    at Layer.handle [as handle_request] (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:280:10)"
2025-05-15T05:41:49.988Z 72486e5a-9d42-4feb-9342-bc4fd6653f41 error: ErrorMiddleware: Set HTTP code: 404
2025-05-15T05:41:49.988Z 72486e5a-9d42-4feb-9342-bc4fd6653f41 error: ErrorMiddleware: Set HTTP status message: The specified queue does not exist.
2025-05-15T05:41:49.988Z 72486e5a-9d42-4feb-9342-bc4fd6653f41 error: ErrorMiddleware: Set HTTP Header: x-ms-error-code=QueueNotFound
2025-05-15T05:41:49.988Z 72486e5a-9d42-4feb-9342-bc4fd6653f41 error: ErrorMiddleware: Set HTTP Header: x-ms-request-id=72486e5a-9d42-4feb-9342-bc4fd6653f41
2025-05-15T05:41:49.988Z 72486e5a-9d42-4feb-9342-bc4fd6653f41 error: ErrorMiddleware: Set HTTP Header: x-ms-version=2025-05-05
2025-05-15T05:41:49.988Z 72486e5a-9d42-4feb-9342-bc4fd6653f41 error: ErrorMiddleware: Set content type: application/xml
2025-05-15T05:41:49.988Z 72486e5a-9d42-4feb-9342-bc4fd6653f41 error: ErrorMiddleware: Set HTTP body: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:72486e5a-9d42-4feb-9342-bc4fd6653f41\nTime:2025-05-15T05:41:49.987Z</Message>\n</Error>"
2025-05-15T05:41:49.989Z 72486e5a-9d42-4feb-9342-bc4fd6653f41 verbose: Send Queue telemetry: Q_Queue_GetProperties
2025-05-15T05:41:49.989Z 72486e5a-9d42-4feb-9342-bc4fd6653f41 info: EndMiddleware: End response. TotalTimeInMS=3 StatusCode=404 StatusMessage=The specified queue does not exist. Headers={"server":"Azurite-Queue/3.34.0","x-ms-error-code":"QueueNotFound","x-ms-request-id":"72486e5a-9d42-4feb-9342-bc4fd6653f41","x-ms-version":"2025-05-05","content-type":"application/xml"}
2025-05-15T05:41:49.990Z ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d info: QueueStorageContextMiddleware: RequestMethod=GET RequestURL=http://127.0.0.1/devstoreaccount1/task-queue-high?comp=metadata RequestHeaders:{"host":"127.0.0.1:10001","x-ms-version":"2025-01-05","accept":"application/xml","x-ms-client-request-id":"4c16537a-0160-4e93-b178-482c78f328d1","x-ms-return-client-request-id":"true","user-agent":"azsdk-net-Storage.Queues/12.21.0 (.NET 8.0.13; Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:53:26 PDT 2025; root:xnu-11417.121.6~2/RELEASE_X86_64)","x-ms-date":"Thu, 15 May 2025 05:41:49 GMT","authorization":"SharedKey devstoreaccount1:sRekWKKaF2v6B0EiBudmbR8ln1DWiMZgyxbCJC/V1t0="} ClientIP=127.0.0.1 Protocol=http HTTPVersion=1.1
2025-05-15T05:41:49.990Z ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d info: QueueStorageContextMiddleware: Account=devstoreaccount1 Queue=task-queue-high Message=undefined MessageId=undefined
2025-05-15T05:41:49.990Z ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d verbose: DispatchMiddleware: Dispatching request...
2025-05-15T05:41:49.990Z ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d info: DispatchMiddleware: Operation=Queue_GetProperties
2025-05-15T05:41:49.990Z ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d verbose: AuthenticationMiddlewareFactory:createAuthenticationMiddleware() Validating authentications.
2025-05-15T05:41:49.990Z ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d info: QueueSharedKeyAuthenticator:validate() Start validation against account shared key authentication.
2025-05-15T05:41:49.990Z ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d info: QueueSharedKeyAuthenticator:validate() [STRING TO SIGN]:"GET\n\n\n\n\n\n\n\n\n\n\n\nx-ms-client-request-id:4c16537a-0160-4e93-b178-482c78f328d1\nx-ms-date:Thu, 15 May 2025 05:41:49 GMT\nx-ms-return-client-request-id:true\nx-ms-version:2025-01-05\n/devstoreaccount1/devstoreaccount1/task-queue-high\ncomp:metadata"
2025-05-15T05:41:49.990Z ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d info: QueueSharedKeyAuthenticator:validate() Calculated authentication header based on key1: devstoreaccount1:sRekWKKaF2v6B0EiBudmbR8ln1DWiMZgyxbCJC/V1t0=
2025-05-15T05:41:49.990Z ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d info: QueueSharedKeyAuthenticator:validate() Signature 1 matched.
2025-05-15T05:41:49.990Z ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d verbose: DeserializerMiddleware: Start deserializing...
2025-05-15T05:41:49.990Z ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d info: HandlerMiddleware: DeserializedParameters={"options":{"requestId":"4c16537a-0160-4e93-b178-482c78f328d1"},"comp":"metadata","version":"2025-01-05"}
2025-05-15T05:41:49.991Z ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d error: ErrorMiddleware: Received a MiddlewareError, fill error information to HTTP response
2025-05-15T05:41:49.991Z ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d error: ErrorMiddleware: ErrorName=StorageError ErrorMessage=The specified queue does not exist.  ErrorHTTPStatusCode=404 ErrorHTTPStatusMessage=The specified queue does not exist. ErrorHTTPHeaders={"x-ms-error-code":"QueueNotFound","x-ms-request-id":"ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d","x-ms-version":"2025-05-05"} ErrorHTTPBody="<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d\nTime:2025-05-15T05:41:49.990Z</Message>\n</Error>" ErrorStack="StorageError: The specified queue does not exist.\n    at StorageErrorFactory.getQueueNotFound (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/errors/StorageErrorFactory.js:110:16)\n    at LokiQueueMetadataStore.getQueue (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/persistence/LokiQueueMetadataStore.js:247:49)\n    at QueueHandler.getProperties (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/handlers/QueueHandler.js:81:48)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/middleware/HandlerMiddlewareFactory.js:58:18\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/ExpressMiddlewareFactory.js:77:63\n    at Layer.handle [as handle_request] (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:280:10)"
2025-05-15T05:41:49.991Z ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d error: ErrorMiddleware: Set HTTP code: 404
2025-05-15T05:41:49.991Z ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d error: ErrorMiddleware: Set HTTP status message: The specified queue does not exist.
2025-05-15T05:41:49.991Z ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d error: ErrorMiddleware: Set HTTP Header: x-ms-error-code=QueueNotFound
2025-05-15T05:41:49.991Z ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d error: ErrorMiddleware: Set HTTP Header: x-ms-request-id=ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d
2025-05-15T05:41:49.991Z ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d error: ErrorMiddleware: Set HTTP Header: x-ms-version=2025-05-05
2025-05-15T05:41:49.991Z ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d error: ErrorMiddleware: Set content type: application/xml
2025-05-15T05:41:49.991Z ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d error: ErrorMiddleware: Set HTTP body: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d\nTime:2025-05-15T05:41:49.990Z</Message>\n</Error>"
2025-05-15T05:41:49.991Z ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d verbose: Send Queue telemetry: Q_Queue_GetProperties
2025-05-15T05:41:49.991Z ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d info: EndMiddleware: End response. TotalTimeInMS=1 StatusCode=404 StatusMessage=The specified queue does not exist. Headers={"server":"Azurite-Queue/3.34.0","x-ms-error-code":"QueueNotFound","x-ms-request-id":"ad11a83f-4f4c-42fd-b1d8-c9eb37af2e1d","x-ms-version":"2025-05-05","content-type":"application/xml"}
2025-05-15T05:41:50.180Z eb389be8-091b-490c-9212-ad52d67bdf5e info: QueueStorageContextMiddleware: RequestMethod=GET RequestURL=http://127.0.0.1/devstoreaccount1/task-queue-low?comp=metadata RequestHeaders:{"host":"127.0.0.1:10001","x-ms-version":"2025-01-05","accept":"application/xml","x-ms-client-request-id":"af25241b-a48b-43bb-bfa2-a50219e7892e","x-ms-return-client-request-id":"true","user-agent":"azsdk-net-Storage.Queues/12.21.0 (.NET 8.0.13; Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:53:26 PDT 2025; root:xnu-11417.121.6~2/RELEASE_X86_64)","x-ms-date":"Thu, 15 May 2025 05:41:50 GMT","authorization":"SharedKey devstoreaccount1:4u+XJp1hEjgesxESjUi1M7BElbR3nVMkDEYIB8z0MV8="} ClientIP=127.0.0.1 Protocol=http HTTPVersion=1.1
2025-05-15T05:41:50.180Z eb389be8-091b-490c-9212-ad52d67bdf5e info: QueueStorageContextMiddleware: Account=devstoreaccount1 Queue=task-queue-low Message=undefined MessageId=undefined
2025-05-15T05:41:50.180Z eb389be8-091b-490c-9212-ad52d67bdf5e verbose: DispatchMiddleware: Dispatching request...
2025-05-15T05:41:50.181Z eb389be8-091b-490c-9212-ad52d67bdf5e info: DispatchMiddleware: Operation=Queue_GetProperties
2025-05-15T05:41:50.181Z eb389be8-091b-490c-9212-ad52d67bdf5e verbose: AuthenticationMiddlewareFactory:createAuthenticationMiddleware() Validating authentications.
2025-05-15T05:41:50.181Z eb389be8-091b-490c-9212-ad52d67bdf5e info: QueueSharedKeyAuthenticator:validate() Start validation against account shared key authentication.
2025-05-15T05:41:50.181Z eb389be8-091b-490c-9212-ad52d67bdf5e info: QueueSharedKeyAuthenticator:validate() [STRING TO SIGN]:"GET\n\n\n\n\n\n\n\n\n\n\n\nx-ms-client-request-id:af25241b-a48b-43bb-bfa2-a50219e7892e\nx-ms-date:Thu, 15 May 2025 05:41:50 GMT\nx-ms-return-client-request-id:true\nx-ms-version:2025-01-05\n/devstoreaccount1/devstoreaccount1/task-queue-low\ncomp:metadata"
2025-05-15T05:41:50.181Z eb389be8-091b-490c-9212-ad52d67bdf5e info: QueueSharedKeyAuthenticator:validate() Calculated authentication header based on key1: devstoreaccount1:4u+XJp1hEjgesxESjUi1M7BElbR3nVMkDEYIB8z0MV8=
2025-05-15T05:41:50.181Z eb389be8-091b-490c-9212-ad52d67bdf5e info: QueueSharedKeyAuthenticator:validate() Signature 1 matched.
2025-05-15T05:41:50.181Z eb389be8-091b-490c-9212-ad52d67bdf5e verbose: DeserializerMiddleware: Start deserializing...
2025-05-15T05:41:50.181Z eb389be8-091b-490c-9212-ad52d67bdf5e info: HandlerMiddleware: DeserializedParameters={"options":{"requestId":"af25241b-a48b-43bb-bfa2-a50219e7892e"},"comp":"metadata","version":"2025-01-05"}
2025-05-15T05:41:50.182Z eb389be8-091b-490c-9212-ad52d67bdf5e error: ErrorMiddleware: Received a MiddlewareError, fill error information to HTTP response
2025-05-15T05:41:50.182Z eb389be8-091b-490c-9212-ad52d67bdf5e error: ErrorMiddleware: ErrorName=StorageError ErrorMessage=The specified queue does not exist.  ErrorHTTPStatusCode=404 ErrorHTTPStatusMessage=The specified queue does not exist. ErrorHTTPHeaders={"x-ms-error-code":"QueueNotFound","x-ms-request-id":"eb389be8-091b-490c-9212-ad52d67bdf5e","x-ms-version":"2025-05-05"} ErrorHTTPBody="<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:eb389be8-091b-490c-9212-ad52d67bdf5e\nTime:2025-05-15T05:41:50.181Z</Message>\n</Error>" ErrorStack="StorageError: The specified queue does not exist.\n    at StorageErrorFactory.getQueueNotFound (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/errors/StorageErrorFactory.js:110:16)\n    at LokiQueueMetadataStore.getQueue (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/persistence/LokiQueueMetadataStore.js:247:49)\n    at QueueHandler.getProperties (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/handlers/QueueHandler.js:81:48)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/middleware/HandlerMiddlewareFactory.js:58:18\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/ExpressMiddlewareFactory.js:77:63\n    at Layer.handle [as handle_request] (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:280:10)"
2025-05-15T05:41:50.182Z eb389be8-091b-490c-9212-ad52d67bdf5e error: ErrorMiddleware: Set HTTP code: 404
2025-05-15T05:41:50.182Z eb389be8-091b-490c-9212-ad52d67bdf5e error: ErrorMiddleware: Set HTTP status message: The specified queue does not exist.
2025-05-15T05:41:50.182Z eb389be8-091b-490c-9212-ad52d67bdf5e error: ErrorMiddleware: Set HTTP Header: x-ms-error-code=QueueNotFound
2025-05-15T05:41:50.182Z eb389be8-091b-490c-9212-ad52d67bdf5e error: ErrorMiddleware: Set HTTP Header: x-ms-request-id=eb389be8-091b-490c-9212-ad52d67bdf5e
2025-05-15T05:41:50.182Z eb389be8-091b-490c-9212-ad52d67bdf5e error: ErrorMiddleware: Set HTTP Header: x-ms-version=2025-05-05
2025-05-15T05:41:50.182Z eb389be8-091b-490c-9212-ad52d67bdf5e error: ErrorMiddleware: Set content type: application/xml
2025-05-15T05:41:50.182Z eb389be8-091b-490c-9212-ad52d67bdf5e error: ErrorMiddleware: Set HTTP body: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:eb389be8-091b-490c-9212-ad52d67bdf5e\nTime:2025-05-15T05:41:50.181Z</Message>\n</Error>"
2025-05-15T05:41:50.182Z eb389be8-091b-490c-9212-ad52d67bdf5e verbose: Send Queue telemetry: Q_Queue_GetProperties
2025-05-15T05:41:50.182Z eb389be8-091b-490c-9212-ad52d67bdf5e info: EndMiddleware: End response. TotalTimeInMS=2 StatusCode=404 StatusMessage=The specified queue does not exist. Headers={"server":"Azurite-Queue/3.34.0","x-ms-error-code":"QueueNotFound","x-ms-request-id":"eb389be8-091b-490c-9212-ad52d67bdf5e","x-ms-version":"2025-05-05","content-type":"application/xml"}
2025-05-15T05:41:50.186Z 5147126d-e792-4c5c-9e2a-0020ff3d4e65 info: QueueStorageContextMiddleware: RequestMethod=GET RequestURL=http://127.0.0.1/devstoreaccount1/task-queue-high?comp=metadata RequestHeaders:{"host":"127.0.0.1:10001","x-ms-version":"2025-01-05","accept":"application/xml","x-ms-client-request-id":"cbb90dbf-1798-46f5-88d2-bcf88ad27d72","x-ms-return-client-request-id":"true","user-agent":"azsdk-net-Storage.Queues/12.21.0 (.NET 8.0.13; Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:53:26 PDT 2025; root:xnu-11417.121.6~2/RELEASE_X86_64)","x-ms-date":"Thu, 15 May 2025 05:41:50 GMT","authorization":"SharedKey devstoreaccount1:FN2LLynyG7oj5s60s/W54/QStFJOep5XlUkEd9eP+Mk="} ClientIP=127.0.0.1 Protocol=http HTTPVersion=1.1
2025-05-15T05:41:50.187Z 5147126d-e792-4c5c-9e2a-0020ff3d4e65 info: QueueStorageContextMiddleware: Account=devstoreaccount1 Queue=task-queue-high Message=undefined MessageId=undefined
2025-05-15T05:41:50.187Z 5147126d-e792-4c5c-9e2a-0020ff3d4e65 verbose: DispatchMiddleware: Dispatching request...
2025-05-15T05:41:50.187Z 5147126d-e792-4c5c-9e2a-0020ff3d4e65 info: DispatchMiddleware: Operation=Queue_GetProperties
2025-05-15T05:41:50.187Z 5147126d-e792-4c5c-9e2a-0020ff3d4e65 verbose: AuthenticationMiddlewareFactory:createAuthenticationMiddleware() Validating authentications.
2025-05-15T05:41:50.187Z 5147126d-e792-4c5c-9e2a-0020ff3d4e65 info: QueueSharedKeyAuthenticator:validate() Start validation against account shared key authentication.
2025-05-15T05:41:50.187Z 5147126d-e792-4c5c-9e2a-0020ff3d4e65 info: QueueSharedKeyAuthenticator:validate() [STRING TO SIGN]:"GET\n\n\n\n\n\n\n\n\n\n\n\nx-ms-client-request-id:cbb90dbf-1798-46f5-88d2-bcf88ad27d72\nx-ms-date:Thu, 15 May 2025 05:41:50 GMT\nx-ms-return-client-request-id:true\nx-ms-version:2025-01-05\n/devstoreaccount1/devstoreaccount1/task-queue-high\ncomp:metadata"
2025-05-15T05:41:50.187Z 5147126d-e792-4c5c-9e2a-0020ff3d4e65 info: QueueSharedKeyAuthenticator:validate() Calculated authentication header based on key1: devstoreaccount1:FN2LLynyG7oj5s60s/W54/QStFJOep5XlUkEd9eP+Mk=
2025-05-15T05:41:50.187Z 5147126d-e792-4c5c-9e2a-0020ff3d4e65 info: QueueSharedKeyAuthenticator:validate() Signature 1 matched.
2025-05-15T05:41:50.187Z 5147126d-e792-4c5c-9e2a-0020ff3d4e65 verbose: DeserializerMiddleware: Start deserializing...
2025-05-15T05:41:50.187Z 5147126d-e792-4c5c-9e2a-0020ff3d4e65 info: HandlerMiddleware: DeserializedParameters={"options":{"requestId":"cbb90dbf-1798-46f5-88d2-bcf88ad27d72"},"comp":"metadata","version":"2025-01-05"}
2025-05-15T05:41:50.187Z 5147126d-e792-4c5c-9e2a-0020ff3d4e65 error: ErrorMiddleware: Received a MiddlewareError, fill error information to HTTP response
2025-05-15T05:41:50.188Z 5147126d-e792-4c5c-9e2a-0020ff3d4e65 error: ErrorMiddleware: ErrorName=StorageError ErrorMessage=The specified queue does not exist.  ErrorHTTPStatusCode=404 ErrorHTTPStatusMessage=The specified queue does not exist. ErrorHTTPHeaders={"x-ms-error-code":"QueueNotFound","x-ms-request-id":"5147126d-e792-4c5c-9e2a-0020ff3d4e65","x-ms-version":"2025-05-05"} ErrorHTTPBody="<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:5147126d-e792-4c5c-9e2a-0020ff3d4e65\nTime:2025-05-15T05:41:50.187Z</Message>\n</Error>" ErrorStack="StorageError: The specified queue does not exist.\n    at StorageErrorFactory.getQueueNotFound (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/errors/StorageErrorFactory.js:110:16)\n    at LokiQueueMetadataStore.getQueue (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/persistence/LokiQueueMetadataStore.js:247:49)\n    at QueueHandler.getProperties (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/handlers/QueueHandler.js:81:48)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/middleware/HandlerMiddlewareFactory.js:58:18\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/ExpressMiddlewareFactory.js:77:63\n    at Layer.handle [as handle_request] (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:280:10)"
2025-05-15T05:41:50.188Z 5147126d-e792-4c5c-9e2a-0020ff3d4e65 error: ErrorMiddleware: Set HTTP code: 404
2025-05-15T05:41:50.188Z 5147126d-e792-4c5c-9e2a-0020ff3d4e65 error: ErrorMiddleware: Set HTTP status message: The specified queue does not exist.
2025-05-15T05:41:50.188Z 5147126d-e792-4c5c-9e2a-0020ff3d4e65 error: ErrorMiddleware: Set HTTP Header: x-ms-error-code=QueueNotFound
2025-05-15T05:41:50.188Z 5147126d-e792-4c5c-9e2a-0020ff3d4e65 error: ErrorMiddleware: Set HTTP Header: x-ms-request-id=5147126d-e792-4c5c-9e2a-0020ff3d4e65
2025-05-15T05:41:50.188Z 5147126d-e792-4c5c-9e2a-0020ff3d4e65 error: ErrorMiddleware: Set HTTP Header: x-ms-version=2025-05-05
2025-05-15T05:41:50.188Z 5147126d-e792-4c5c-9e2a-0020ff3d4e65 error: ErrorMiddleware: Set content type: application/xml
2025-05-15T05:41:50.188Z 5147126d-e792-4c5c-9e2a-0020ff3d4e65 error: ErrorMiddleware: Set HTTP body: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:5147126d-e792-4c5c-9e2a-0020ff3d4e65\nTime:2025-05-15T05:41:50.187Z</Message>\n</Error>"
2025-05-15T05:41:50.188Z 5147126d-e792-4c5c-9e2a-0020ff3d4e65 verbose: Send Queue telemetry: Q_Queue_GetProperties
2025-05-15T05:41:50.188Z 5147126d-e792-4c5c-9e2a-0020ff3d4e65 info: EndMiddleware: End response. TotalTimeInMS=2 StatusCode=404 StatusMessage=The specified queue does not exist. Headers={"server":"Azurite-Queue/3.34.0","x-ms-error-code":"QueueNotFound","x-ms-request-id":"5147126d-e792-4c5c-9e2a-0020ff3d4e65","x-ms-version":"2025-05-05","content-type":"application/xml"}
2025-05-15T05:41:50.207Z a5b7bbe2-f99f-42d8-8130-bf1b795b27d8 info: QueueStorageContextMiddleware: RequestMethod=GET RequestURL=http://127.0.0.1/devstoreaccount1/task-queue-medium?comp=metadata RequestHeaders:{"host":"127.0.0.1:10001","x-ms-version":"2025-01-05","accept":"application/xml","x-ms-client-request-id":"25cedaa4-ba0d-4525-bc12-0c5dbe2d9d8b","x-ms-return-client-request-id":"true","user-agent":"azsdk-net-Storage.Queues/12.21.0 (.NET 8.0.13; Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:53:26 PDT 2025; root:xnu-11417.121.6~2/RELEASE_X86_64)","x-ms-date":"Thu, 15 May 2025 05:41:50 GMT","authorization":"SharedKey devstoreaccount1:A8fBUeAgk4uYlnfqiTtP6cb8m6pwV5szXjicTewLxcU="} ClientIP=127.0.0.1 Protocol=http HTTPVersion=1.1
2025-05-15T05:41:50.207Z a5b7bbe2-f99f-42d8-8130-bf1b795b27d8 info: QueueStorageContextMiddleware: Account=devstoreaccount1 Queue=task-queue-medium Message=undefined MessageId=undefined
2025-05-15T05:41:50.207Z a5b7bbe2-f99f-42d8-8130-bf1b795b27d8 verbose: DispatchMiddleware: Dispatching request...
2025-05-15T05:41:50.207Z a5b7bbe2-f99f-42d8-8130-bf1b795b27d8 info: DispatchMiddleware: Operation=Queue_GetProperties
2025-05-15T05:41:50.207Z a5b7bbe2-f99f-42d8-8130-bf1b795b27d8 verbose: AuthenticationMiddlewareFactory:createAuthenticationMiddleware() Validating authentications.
2025-05-15T05:41:50.207Z a5b7bbe2-f99f-42d8-8130-bf1b795b27d8 info: QueueSharedKeyAuthenticator:validate() Start validation against account shared key authentication.
2025-05-15T05:41:50.208Z a5b7bbe2-f99f-42d8-8130-bf1b795b27d8 info: QueueSharedKeyAuthenticator:validate() [STRING TO SIGN]:"GET\n\n\n\n\n\n\n\n\n\n\n\nx-ms-client-request-id:25cedaa4-ba0d-4525-bc12-0c5dbe2d9d8b\nx-ms-date:Thu, 15 May 2025 05:41:50 GMT\nx-ms-return-client-request-id:true\nx-ms-version:2025-01-05\n/devstoreaccount1/devstoreaccount1/task-queue-medium\ncomp:metadata"
2025-05-15T05:41:50.208Z a5b7bbe2-f99f-42d8-8130-bf1b795b27d8 info: QueueSharedKeyAuthenticator:validate() Calculated authentication header based on key1: devstoreaccount1:A8fBUeAgk4uYlnfqiTtP6cb8m6pwV5szXjicTewLxcU=
2025-05-15T05:41:50.208Z a5b7bbe2-f99f-42d8-8130-bf1b795b27d8 info: QueueSharedKeyAuthenticator:validate() Signature 1 matched.
2025-05-15T05:41:50.208Z a5b7bbe2-f99f-42d8-8130-bf1b795b27d8 verbose: DeserializerMiddleware: Start deserializing...
2025-05-15T05:41:50.208Z a5b7bbe2-f99f-42d8-8130-bf1b795b27d8 info: HandlerMiddleware: DeserializedParameters={"options":{"requestId":"25cedaa4-ba0d-4525-bc12-0c5dbe2d9d8b"},"comp":"metadata","version":"2025-01-05"}
2025-05-15T05:41:50.209Z a5b7bbe2-f99f-42d8-8130-bf1b795b27d8 error: ErrorMiddleware: Received a MiddlewareError, fill error information to HTTP response
2025-05-15T05:41:50.209Z a5b7bbe2-f99f-42d8-8130-bf1b795b27d8 error: ErrorMiddleware: ErrorName=StorageError ErrorMessage=The specified queue does not exist.  ErrorHTTPStatusCode=404 ErrorHTTPStatusMessage=The specified queue does not exist. ErrorHTTPHeaders={"x-ms-error-code":"QueueNotFound","x-ms-request-id":"a5b7bbe2-f99f-42d8-8130-bf1b795b27d8","x-ms-version":"2025-05-05"} ErrorHTTPBody="<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:a5b7bbe2-f99f-42d8-8130-bf1b795b27d8\nTime:2025-05-15T05:41:50.208Z</Message>\n</Error>" ErrorStack="StorageError: The specified queue does not exist.\n    at StorageErrorFactory.getQueueNotFound (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/errors/StorageErrorFactory.js:110:16)\n    at LokiQueueMetadataStore.getQueue (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/persistence/LokiQueueMetadataStore.js:247:49)\n    at QueueHandler.getProperties (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/handlers/QueueHandler.js:81:48)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/middleware/HandlerMiddlewareFactory.js:58:18\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/ExpressMiddlewareFactory.js:77:63\n    at Layer.handle [as handle_request] (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:280:10)"
2025-05-15T05:41:50.209Z a5b7bbe2-f99f-42d8-8130-bf1b795b27d8 error: ErrorMiddleware: Set HTTP code: 404
2025-05-15T05:41:50.209Z a5b7bbe2-f99f-42d8-8130-bf1b795b27d8 error: ErrorMiddleware: Set HTTP status message: The specified queue does not exist.
2025-05-15T05:41:50.209Z a5b7bbe2-f99f-42d8-8130-bf1b795b27d8 error: ErrorMiddleware: Set HTTP Header: x-ms-error-code=QueueNotFound
2025-05-15T05:41:50.209Z a5b7bbe2-f99f-42d8-8130-bf1b795b27d8 error: ErrorMiddleware: Set HTTP Header: x-ms-request-id=a5b7bbe2-f99f-42d8-8130-bf1b795b27d8
2025-05-15T05:41:50.209Z a5b7bbe2-f99f-42d8-8130-bf1b795b27d8 error: ErrorMiddleware: Set HTTP Header: x-ms-version=2025-05-05
2025-05-15T05:41:50.209Z a5b7bbe2-f99f-42d8-8130-bf1b795b27d8 error: ErrorMiddleware: Set content type: application/xml
2025-05-15T05:41:50.209Z a5b7bbe2-f99f-42d8-8130-bf1b795b27d8 error: ErrorMiddleware: Set HTTP body: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:a5b7bbe2-f99f-42d8-8130-bf1b795b27d8\nTime:2025-05-15T05:41:50.208Z</Message>\n</Error>"
2025-05-15T05:41:50.210Z a5b7bbe2-f99f-42d8-8130-bf1b795b27d8 verbose: Send Queue telemetry: Q_Queue_GetProperties
2025-05-15T05:41:50.210Z a5b7bbe2-f99f-42d8-8130-bf1b795b27d8 info: EndMiddleware: End response. TotalTimeInMS=3 StatusCode=404 StatusMessage=The specified queue does not exist. Headers={"server":"Azurite-Queue/3.34.0","x-ms-error-code":"QueueNotFound","x-ms-request-id":"a5b7bbe2-f99f-42d8-8130-bf1b795b27d8","x-ms-version":"2025-05-05","content-type":"application/xml"}
2025-05-15T05:41:50.465Z 86f0be78-b72d-4b5a-82a6-bea4d5cc6005 info: QueueStorageContextMiddleware: RequestMethod=GET RequestURL=http://127.0.0.1/devstoreaccount1/task-queue-low?comp=metadata RequestHeaders:{"host":"127.0.0.1:10001","x-ms-version":"2025-01-05","accept":"application/xml","x-ms-client-request-id":"77be56e5-dd7a-4abb-81b4-a2cee142966a","x-ms-return-client-request-id":"true","user-agent":"azsdk-net-Storage.Queues/12.21.0 (.NET 8.0.13; Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:53:26 PDT 2025; root:xnu-11417.121.6~2/RELEASE_X86_64)","x-ms-date":"Thu, 15 May 2025 05:41:50 GMT","authorization":"SharedKey devstoreaccount1:CiVLx1cdfK/jUpe0D4imMwjiKhQdrYWpzE710ILq2k4="} ClientIP=127.0.0.1 Protocol=http HTTPVersion=1.1
2025-05-15T05:41:50.465Z 86f0be78-b72d-4b5a-82a6-bea4d5cc6005 info: QueueStorageContextMiddleware: Account=devstoreaccount1 Queue=task-queue-low Message=undefined MessageId=undefined
2025-05-15T05:41:50.466Z 86f0be78-b72d-4b5a-82a6-bea4d5cc6005 verbose: DispatchMiddleware: Dispatching request...
2025-05-15T05:41:50.466Z 86f0be78-b72d-4b5a-82a6-bea4d5cc6005 info: DispatchMiddleware: Operation=Queue_GetProperties
2025-05-15T05:41:50.466Z 86f0be78-b72d-4b5a-82a6-bea4d5cc6005 verbose: AuthenticationMiddlewareFactory:createAuthenticationMiddleware() Validating authentications.
2025-05-15T05:41:50.466Z 86f0be78-b72d-4b5a-82a6-bea4d5cc6005 info: QueueSharedKeyAuthenticator:validate() Start validation against account shared key authentication.
2025-05-15T05:41:50.466Z 86f0be78-b72d-4b5a-82a6-bea4d5cc6005 info: QueueSharedKeyAuthenticator:validate() [STRING TO SIGN]:"GET\n\n\n\n\n\n\n\n\n\n\n\nx-ms-client-request-id:77be56e5-dd7a-4abb-81b4-a2cee142966a\nx-ms-date:Thu, 15 May 2025 05:41:50 GMT\nx-ms-return-client-request-id:true\nx-ms-version:2025-01-05\n/devstoreaccount1/devstoreaccount1/task-queue-low\ncomp:metadata"
2025-05-15T05:41:50.466Z 86f0be78-b72d-4b5a-82a6-bea4d5cc6005 info: QueueSharedKeyAuthenticator:validate() Calculated authentication header based on key1: devstoreaccount1:CiVLx1cdfK/jUpe0D4imMwjiKhQdrYWpzE710ILq2k4=
2025-05-15T05:41:50.466Z 86f0be78-b72d-4b5a-82a6-bea4d5cc6005 info: QueueSharedKeyAuthenticator:validate() Signature 1 matched.
2025-05-15T05:41:50.466Z 86f0be78-b72d-4b5a-82a6-bea4d5cc6005 verbose: DeserializerMiddleware: Start deserializing...
2025-05-15T05:41:50.466Z 86f0be78-b72d-4b5a-82a6-bea4d5cc6005 info: HandlerMiddleware: DeserializedParameters={"options":{"requestId":"77be56e5-dd7a-4abb-81b4-a2cee142966a"},"comp":"metadata","version":"2025-01-05"}
2025-05-15T05:41:50.466Z 86f0be78-b72d-4b5a-82a6-bea4d5cc6005 error: ErrorMiddleware: Received a MiddlewareError, fill error information to HTTP response
2025-05-15T05:41:50.466Z 86f0be78-b72d-4b5a-82a6-bea4d5cc6005 error: ErrorMiddleware: ErrorName=StorageError ErrorMessage=The specified queue does not exist.  ErrorHTTPStatusCode=404 ErrorHTTPStatusMessage=The specified queue does not exist. ErrorHTTPHeaders={"x-ms-error-code":"QueueNotFound","x-ms-request-id":"86f0be78-b72d-4b5a-82a6-bea4d5cc6005","x-ms-version":"2025-05-05"} ErrorHTTPBody="<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:86f0be78-b72d-4b5a-82a6-bea4d5cc6005\nTime:2025-05-15T05:41:50.466Z</Message>\n</Error>" ErrorStack="StorageError: The specified queue does not exist.\n    at StorageErrorFactory.getQueueNotFound (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/errors/StorageErrorFactory.js:110:16)\n    at LokiQueueMetadataStore.getQueue (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/persistence/LokiQueueMetadataStore.js:247:49)\n    at QueueHandler.getProperties (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/handlers/QueueHandler.js:81:48)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/middleware/HandlerMiddlewareFactory.js:58:18\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/ExpressMiddlewareFactory.js:77:63\n    at Layer.handle [as handle_request] (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:280:10)"
2025-05-15T05:41:50.467Z 86f0be78-b72d-4b5a-82a6-bea4d5cc6005 error: ErrorMiddleware: Set HTTP code: 404
2025-05-15T05:41:50.467Z 86f0be78-b72d-4b5a-82a6-bea4d5cc6005 error: ErrorMiddleware: Set HTTP status message: The specified queue does not exist.
2025-05-15T05:41:50.467Z 86f0be78-b72d-4b5a-82a6-bea4d5cc6005 error: ErrorMiddleware: Set HTTP Header: x-ms-error-code=QueueNotFound
2025-05-15T05:41:50.467Z 86f0be78-b72d-4b5a-82a6-bea4d5cc6005 error: ErrorMiddleware: Set HTTP Header: x-ms-request-id=86f0be78-b72d-4b5a-82a6-bea4d5cc6005
2025-05-15T05:41:50.467Z 86f0be78-b72d-4b5a-82a6-bea4d5cc6005 error: ErrorMiddleware: Set HTTP Header: x-ms-version=2025-05-05
2025-05-15T05:41:50.467Z 86f0be78-b72d-4b5a-82a6-bea4d5cc6005 error: ErrorMiddleware: Set content type: application/xml
2025-05-15T05:41:50.467Z 86f0be78-b72d-4b5a-82a6-bea4d5cc6005 error: ErrorMiddleware: Set HTTP body: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:86f0be78-b72d-4b5a-82a6-bea4d5cc6005\nTime:2025-05-15T05:41:50.466Z</Message>\n</Error>"
2025-05-15T05:41:50.467Z 86f0be78-b72d-4b5a-82a6-bea4d5cc6005 verbose: Send Queue telemetry: Q_Queue_GetProperties
2025-05-15T05:41:50.467Z 86f0be78-b72d-4b5a-82a6-bea4d5cc6005 info: EndMiddleware: End response. TotalTimeInMS=2 StatusCode=404 StatusMessage=The specified queue does not exist. Headers={"server":"Azurite-Queue/3.34.0","x-ms-error-code":"QueueNotFound","x-ms-request-id":"86f0be78-b72d-4b5a-82a6-bea4d5cc6005","x-ms-version":"2025-05-05","content-type":"application/xml"}
2025-05-15T05:41:50.526Z 8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027 info: QueueStorageContextMiddleware: RequestMethod=GET RequestURL=http://127.0.0.1/devstoreaccount1/task-queue-medium?comp=metadata RequestHeaders:{"host":"127.0.0.1:10001","x-ms-version":"2025-01-05","accept":"application/xml","x-ms-client-request-id":"8669e41f-ec80-4ef1-868a-2a8e245c1b50","x-ms-return-client-request-id":"true","user-agent":"azsdk-net-Storage.Queues/12.21.0 (.NET 8.0.13; Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:53:26 PDT 2025; root:xnu-11417.121.6~2/RELEASE_X86_64)","x-ms-date":"Thu, 15 May 2025 05:41:50 GMT","authorization":"SharedKey devstoreaccount1:wANy+MPVlwC4lTtAtFuD0dZM2uWDYXiW01m19stzA5w="} ClientIP=127.0.0.1 Protocol=http HTTPVersion=1.1
2025-05-15T05:41:50.526Z 8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027 info: QueueStorageContextMiddleware: Account=devstoreaccount1 Queue=task-queue-medium Message=undefined MessageId=undefined
2025-05-15T05:41:50.526Z 8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027 verbose: DispatchMiddleware: Dispatching request...
2025-05-15T05:41:50.526Z 8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027 info: DispatchMiddleware: Operation=Queue_GetProperties
2025-05-15T05:41:50.526Z 8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027 verbose: AuthenticationMiddlewareFactory:createAuthenticationMiddleware() Validating authentications.
2025-05-15T05:41:50.526Z 8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027 info: QueueSharedKeyAuthenticator:validate() Start validation against account shared key authentication.
2025-05-15T05:41:50.526Z 8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027 info: QueueSharedKeyAuthenticator:validate() [STRING TO SIGN]:"GET\n\n\n\n\n\n\n\n\n\n\n\nx-ms-client-request-id:8669e41f-ec80-4ef1-868a-2a8e245c1b50\nx-ms-date:Thu, 15 May 2025 05:41:50 GMT\nx-ms-return-client-request-id:true\nx-ms-version:2025-01-05\n/devstoreaccount1/devstoreaccount1/task-queue-medium\ncomp:metadata"
2025-05-15T05:41:50.526Z 8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027 info: QueueSharedKeyAuthenticator:validate() Calculated authentication header based on key1: devstoreaccount1:wANy+MPVlwC4lTtAtFuD0dZM2uWDYXiW01m19stzA5w=
2025-05-15T05:41:50.526Z 8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027 info: QueueSharedKeyAuthenticator:validate() Signature 1 matched.
2025-05-15T05:41:50.526Z 8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027 verbose: DeserializerMiddleware: Start deserializing...
2025-05-15T05:41:50.526Z 8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027 info: HandlerMiddleware: DeserializedParameters={"options":{"requestId":"8669e41f-ec80-4ef1-868a-2a8e245c1b50"},"comp":"metadata","version":"2025-01-05"}
2025-05-15T05:41:50.527Z 8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027 error: ErrorMiddleware: Received a MiddlewareError, fill error information to HTTP response
2025-05-15T05:41:50.527Z 8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027 error: ErrorMiddleware: ErrorName=StorageError ErrorMessage=The specified queue does not exist.  ErrorHTTPStatusCode=404 ErrorHTTPStatusMessage=The specified queue does not exist. ErrorHTTPHeaders={"x-ms-error-code":"QueueNotFound","x-ms-request-id":"8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027","x-ms-version":"2025-05-05"} ErrorHTTPBody="<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027\nTime:2025-05-15T05:41:50.526Z</Message>\n</Error>" ErrorStack="StorageError: The specified queue does not exist.\n    at StorageErrorFactory.getQueueNotFound (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/errors/StorageErrorFactory.js:110:16)\n    at LokiQueueMetadataStore.getQueue (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/persistence/LokiQueueMetadataStore.js:247:49)\n    at QueueHandler.getProperties (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/handlers/QueueHandler.js:81:48)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/middleware/HandlerMiddlewareFactory.js:58:18\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/ExpressMiddlewareFactory.js:77:63\n    at Layer.handle [as handle_request] (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:280:10)"
2025-05-15T05:41:50.527Z 8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027 error: ErrorMiddleware: Set HTTP code: 404
2025-05-15T05:41:50.527Z 8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027 error: ErrorMiddleware: Set HTTP status message: The specified queue does not exist.
2025-05-15T05:41:50.527Z 8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027 error: ErrorMiddleware: Set HTTP Header: x-ms-error-code=QueueNotFound
2025-05-15T05:41:50.527Z 8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027 error: ErrorMiddleware: Set HTTP Header: x-ms-request-id=8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027
2025-05-15T05:41:50.527Z 8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027 error: ErrorMiddleware: Set HTTP Header: x-ms-version=2025-05-05
2025-05-15T05:41:50.527Z 8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027 error: ErrorMiddleware: Set content type: application/xml
2025-05-15T05:41:50.527Z 8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027 error: ErrorMiddleware: Set HTTP body: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027\nTime:2025-05-15T05:41:50.526Z</Message>\n</Error>"
2025-05-15T05:41:50.527Z 8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027 verbose: Send Queue telemetry: Q_Queue_GetProperties
2025-05-15T05:41:50.527Z 8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027 info: EndMiddleware: End response. TotalTimeInMS=1 StatusCode=404 StatusMessage=The specified queue does not exist. Headers={"server":"Azurite-Queue/3.34.0","x-ms-error-code":"QueueNotFound","x-ms-request-id":"8c92f20c-d6f9-4b86-9f4f-ddc0c82a7027","x-ms-version":"2025-05-05","content-type":"application/xml"}
2025-05-15T05:41:50.530Z 2a197d20-68fc-40b2-9452-98896a91c477 info: QueueStorageContextMiddleware: RequestMethod=GET RequestURL=http://127.0.0.1/devstoreaccount1/task-queue-high?comp=metadata RequestHeaders:{"host":"127.0.0.1:10001","x-ms-version":"2025-01-05","accept":"application/xml","x-ms-client-request-id":"2f17da46-932a-428a-992a-4b94e4f9a7df","x-ms-return-client-request-id":"true","user-agent":"azsdk-net-Storage.Queues/12.21.0 (.NET 8.0.13; Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:53:26 PDT 2025; root:xnu-11417.121.6~2/RELEASE_X86_64)","x-ms-date":"Thu, 15 May 2025 05:41:50 GMT","authorization":"SharedKey devstoreaccount1:MST0gXaIJloNS0SDbNSejRYHb3HWM9R2dXXQGv7jyV8="} ClientIP=127.0.0.1 Protocol=http HTTPVersion=1.1
2025-05-15T05:41:50.531Z 2a197d20-68fc-40b2-9452-98896a91c477 info: QueueStorageContextMiddleware: Account=devstoreaccount1 Queue=task-queue-high Message=undefined MessageId=undefined
2025-05-15T05:41:50.531Z 2a197d20-68fc-40b2-9452-98896a91c477 verbose: DispatchMiddleware: Dispatching request...
2025-05-15T05:41:50.531Z 2a197d20-68fc-40b2-9452-98896a91c477 info: DispatchMiddleware: Operation=Queue_GetProperties
2025-05-15T05:41:50.531Z 2a197d20-68fc-40b2-9452-98896a91c477 verbose: AuthenticationMiddlewareFactory:createAuthenticationMiddleware() Validating authentications.
2025-05-15T05:41:50.531Z 2a197d20-68fc-40b2-9452-98896a91c477 info: QueueSharedKeyAuthenticator:validate() Start validation against account shared key authentication.
2025-05-15T05:41:50.531Z 2a197d20-68fc-40b2-9452-98896a91c477 info: QueueSharedKeyAuthenticator:validate() [STRING TO SIGN]:"GET\n\n\n\n\n\n\n\n\n\n\n\nx-ms-client-request-id:2f17da46-932a-428a-992a-4b94e4f9a7df\nx-ms-date:Thu, 15 May 2025 05:41:50 GMT\nx-ms-return-client-request-id:true\nx-ms-version:2025-01-05\n/devstoreaccount1/devstoreaccount1/task-queue-high\ncomp:metadata"
2025-05-15T05:41:50.531Z 2a197d20-68fc-40b2-9452-98896a91c477 info: QueueSharedKeyAuthenticator:validate() Calculated authentication header based on key1: devstoreaccount1:MST0gXaIJloNS0SDbNSejRYHb3HWM9R2dXXQGv7jyV8=
2025-05-15T05:41:50.531Z 2a197d20-68fc-40b2-9452-98896a91c477 info: QueueSharedKeyAuthenticator:validate() Signature 1 matched.
2025-05-15T05:41:50.531Z 2a197d20-68fc-40b2-9452-98896a91c477 verbose: DeserializerMiddleware: Start deserializing...
2025-05-15T05:41:50.531Z 2a197d20-68fc-40b2-9452-98896a91c477 info: HandlerMiddleware: DeserializedParameters={"options":{"requestId":"2f17da46-932a-428a-992a-4b94e4f9a7df"},"comp":"metadata","version":"2025-01-05"}
2025-05-15T05:41:50.531Z 2a197d20-68fc-40b2-9452-98896a91c477 error: ErrorMiddleware: Received a MiddlewareError, fill error information to HTTP response
2025-05-15T05:41:50.531Z 2a197d20-68fc-40b2-9452-98896a91c477 error: ErrorMiddleware: ErrorName=StorageError ErrorMessage=The specified queue does not exist.  ErrorHTTPStatusCode=404 ErrorHTTPStatusMessage=The specified queue does not exist. ErrorHTTPHeaders={"x-ms-error-code":"QueueNotFound","x-ms-request-id":"2a197d20-68fc-40b2-9452-98896a91c477","x-ms-version":"2025-05-05"} ErrorHTTPBody="<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:2a197d20-68fc-40b2-9452-98896a91c477\nTime:2025-05-15T05:41:50.531Z</Message>\n</Error>" ErrorStack="StorageError: The specified queue does not exist.\n    at StorageErrorFactory.getQueueNotFound (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/errors/StorageErrorFactory.js:110:16)\n    at LokiQueueMetadataStore.getQueue (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/persistence/LokiQueueMetadataStore.js:247:49)\n    at QueueHandler.getProperties (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/handlers/QueueHandler.js:81:48)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/middleware/HandlerMiddlewareFactory.js:58:18\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/ExpressMiddlewareFactory.js:77:63\n    at Layer.handle [as handle_request] (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:280:10)"
2025-05-15T05:41:50.531Z 2a197d20-68fc-40b2-9452-98896a91c477 error: ErrorMiddleware: Set HTTP code: 404
2025-05-15T05:41:50.531Z 2a197d20-68fc-40b2-9452-98896a91c477 error: ErrorMiddleware: Set HTTP status message: The specified queue does not exist.
2025-05-15T05:41:50.531Z 2a197d20-68fc-40b2-9452-98896a91c477 error: ErrorMiddleware: Set HTTP Header: x-ms-error-code=QueueNotFound
2025-05-15T05:41:50.532Z 2a197d20-68fc-40b2-9452-98896a91c477 error: ErrorMiddleware: Set HTTP Header: x-ms-request-id=2a197d20-68fc-40b2-9452-98896a91c477
2025-05-15T05:41:50.532Z 2a197d20-68fc-40b2-9452-98896a91c477 error: ErrorMiddleware: Set HTTP Header: x-ms-version=2025-05-05
2025-05-15T05:41:50.532Z 2a197d20-68fc-40b2-9452-98896a91c477 error: ErrorMiddleware: Set content type: application/xml
2025-05-15T05:41:50.532Z 2a197d20-68fc-40b2-9452-98896a91c477 error: ErrorMiddleware: Set HTTP body: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:2a197d20-68fc-40b2-9452-98896a91c477\nTime:2025-05-15T05:41:50.531Z</Message>\n</Error>"
2025-05-15T05:41:50.532Z 2a197d20-68fc-40b2-9452-98896a91c477 verbose: Send Queue telemetry: Q_Queue_GetProperties
2025-05-15T05:41:50.532Z 2a197d20-68fc-40b2-9452-98896a91c477 info: EndMiddleware: End response. TotalTimeInMS=2 StatusCode=404 StatusMessage=The specified queue does not exist. Headers={"server":"Azurite-Queue/3.34.0","x-ms-error-code":"QueueNotFound","x-ms-request-id":"2a197d20-68fc-40b2-9452-98896a91c477","x-ms-version":"2025-05-05","content-type":"application/xml"}
2025-05-15T05:41:50.931Z e8d2d867-fd5c-4969-800b-5033085dad01 info: QueueStorageContextMiddleware: RequestMethod=GET RequestURL=http://127.0.0.1/devstoreaccount1/task-queue-low?comp=metadata RequestHeaders:{"host":"127.0.0.1:10001","x-ms-version":"2025-01-05","accept":"application/xml","x-ms-client-request-id":"f34aa957-bb55-4331-9634-7100b4638245","x-ms-return-client-request-id":"true","user-agent":"azsdk-net-Storage.Queues/12.21.0 (.NET 8.0.13; Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:53:26 PDT 2025; root:xnu-11417.121.6~2/RELEASE_X86_64)","x-ms-date":"Thu, 15 May 2025 05:41:50 GMT","authorization":"SharedKey devstoreaccount1:yq4Cz/UXYi3Eu1tq/eSmFe0VYqFp2BAH3M3YcXTlcyQ="} ClientIP=127.0.0.1 Protocol=http HTTPVersion=1.1
2025-05-15T05:41:50.931Z e8d2d867-fd5c-4969-800b-5033085dad01 info: QueueStorageContextMiddleware: Account=devstoreaccount1 Queue=task-queue-low Message=undefined MessageId=undefined
2025-05-15T05:41:50.931Z e8d2d867-fd5c-4969-800b-5033085dad01 verbose: DispatchMiddleware: Dispatching request...
2025-05-15T05:41:50.931Z e8d2d867-fd5c-4969-800b-5033085dad01 info: DispatchMiddleware: Operation=Queue_GetProperties
2025-05-15T05:41:50.931Z e8d2d867-fd5c-4969-800b-5033085dad01 verbose: AuthenticationMiddlewareFactory:createAuthenticationMiddleware() Validating authentications.
2025-05-15T05:41:50.931Z e8d2d867-fd5c-4969-800b-5033085dad01 info: QueueSharedKeyAuthenticator:validate() Start validation against account shared key authentication.
2025-05-15T05:41:50.931Z e8d2d867-fd5c-4969-800b-5033085dad01 info: QueueSharedKeyAuthenticator:validate() [STRING TO SIGN]:"GET\n\n\n\n\n\n\n\n\n\n\n\nx-ms-client-request-id:f34aa957-bb55-4331-9634-7100b4638245\nx-ms-date:Thu, 15 May 2025 05:41:50 GMT\nx-ms-return-client-request-id:true\nx-ms-version:2025-01-05\n/devstoreaccount1/devstoreaccount1/task-queue-low\ncomp:metadata"
2025-05-15T05:41:50.931Z e8d2d867-fd5c-4969-800b-5033085dad01 info: QueueSharedKeyAuthenticator:validate() Calculated authentication header based on key1: devstoreaccount1:yq4Cz/UXYi3Eu1tq/eSmFe0VYqFp2BAH3M3YcXTlcyQ=
2025-05-15T05:41:50.931Z e8d2d867-fd5c-4969-800b-5033085dad01 info: QueueSharedKeyAuthenticator:validate() Signature 1 matched.
2025-05-15T05:41:50.932Z e8d2d867-fd5c-4969-800b-5033085dad01 verbose: DeserializerMiddleware: Start deserializing...
2025-05-15T05:41:50.932Z e8d2d867-fd5c-4969-800b-5033085dad01 info: HandlerMiddleware: DeserializedParameters={"options":{"requestId":"f34aa957-bb55-4331-9634-7100b4638245"},"comp":"metadata","version":"2025-01-05"}
2025-05-15T05:41:50.932Z e8d2d867-fd5c-4969-800b-5033085dad01 error: ErrorMiddleware: Received a MiddlewareError, fill error information to HTTP response
2025-05-15T05:41:50.932Z e8d2d867-fd5c-4969-800b-5033085dad01 error: ErrorMiddleware: ErrorName=StorageError ErrorMessage=The specified queue does not exist.  ErrorHTTPStatusCode=404 ErrorHTTPStatusMessage=The specified queue does not exist. ErrorHTTPHeaders={"x-ms-error-code":"QueueNotFound","x-ms-request-id":"e8d2d867-fd5c-4969-800b-5033085dad01","x-ms-version":"2025-05-05"} ErrorHTTPBody="<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:e8d2d867-fd5c-4969-800b-5033085dad01\nTime:2025-05-15T05:41:50.932Z</Message>\n</Error>" ErrorStack="StorageError: The specified queue does not exist.\n    at StorageErrorFactory.getQueueNotFound (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/errors/StorageErrorFactory.js:110:16)\n    at LokiQueueMetadataStore.getQueue (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/persistence/LokiQueueMetadataStore.js:247:49)\n    at QueueHandler.getProperties (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/handlers/QueueHandler.js:81:48)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/middleware/HandlerMiddlewareFactory.js:58:18\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/ExpressMiddlewareFactory.js:77:63\n    at Layer.handle [as handle_request] (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:280:10)"
2025-05-15T05:41:50.932Z e8d2d867-fd5c-4969-800b-5033085dad01 error: ErrorMiddleware: Set HTTP code: 404
2025-05-15T05:41:50.932Z e8d2d867-fd5c-4969-800b-5033085dad01 error: ErrorMiddleware: Set HTTP status message: The specified queue does not exist.
2025-05-15T05:41:50.932Z e8d2d867-fd5c-4969-800b-5033085dad01 error: ErrorMiddleware: Set HTTP Header: x-ms-error-code=QueueNotFound
2025-05-15T05:41:50.932Z e8d2d867-fd5c-4969-800b-5033085dad01 error: ErrorMiddleware: Set HTTP Header: x-ms-request-id=e8d2d867-fd5c-4969-800b-5033085dad01
2025-05-15T05:41:50.932Z e8d2d867-fd5c-4969-800b-5033085dad01 error: ErrorMiddleware: Set HTTP Header: x-ms-version=2025-05-05
2025-05-15T05:41:50.932Z e8d2d867-fd5c-4969-800b-5033085dad01 error: ErrorMiddleware: Set content type: application/xml
2025-05-15T05:41:50.932Z e8d2d867-fd5c-4969-800b-5033085dad01 error: ErrorMiddleware: Set HTTP body: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:e8d2d867-fd5c-4969-800b-5033085dad01\nTime:2025-05-15T05:41:50.932Z</Message>\n</Error>"
2025-05-15T05:41:50.932Z e8d2d867-fd5c-4969-800b-5033085dad01 verbose: Send Queue telemetry: Q_Queue_GetProperties
2025-05-15T05:41:50.932Z e8d2d867-fd5c-4969-800b-5033085dad01 info: EndMiddleware: End response. TotalTimeInMS=1 StatusCode=404 StatusMessage=The specified queue does not exist. Headers={"server":"Azurite-Queue/3.34.0","x-ms-error-code":"QueueNotFound","x-ms-request-id":"e8d2d867-fd5c-4969-800b-5033085dad01","x-ms-version":"2025-05-05","content-type":"application/xml"}
2025-05-15T05:41:51.014Z 6218b80f-cd67-4063-af0a-8bbec97ff118 info: QueueStorageContextMiddleware: RequestMethod=GET RequestURL=http://127.0.0.1/devstoreaccount1/task-queue-medium?comp=metadata RequestHeaders:{"host":"127.0.0.1:10001","x-ms-version":"2025-01-05","accept":"application/xml","x-ms-client-request-id":"5d7bff14-356f-4c95-8576-d5d1bda73b2b","x-ms-return-client-request-id":"true","user-agent":"azsdk-net-Storage.Queues/12.21.0 (.NET 8.0.13; Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:53:26 PDT 2025; root:xnu-11417.121.6~2/RELEASE_X86_64)","x-ms-date":"Thu, 15 May 2025 05:41:51 GMT","authorization":"SharedKey devstoreaccount1:MYpmN4h+g8p2JwKyZotZSJz9zXT+q/BX7ooHTEMNyxo="} ClientIP=127.0.0.1 Protocol=http HTTPVersion=1.1
2025-05-15T05:41:51.014Z 6218b80f-cd67-4063-af0a-8bbec97ff118 info: QueueStorageContextMiddleware: Account=devstoreaccount1 Queue=task-queue-medium Message=undefined MessageId=undefined
2025-05-15T05:41:51.014Z 6218b80f-cd67-4063-af0a-8bbec97ff118 verbose: DispatchMiddleware: Dispatching request...
2025-05-15T05:41:51.014Z 6218b80f-cd67-4063-af0a-8bbec97ff118 info: DispatchMiddleware: Operation=Queue_GetProperties
2025-05-15T05:41:51.014Z 6218b80f-cd67-4063-af0a-8bbec97ff118 verbose: AuthenticationMiddlewareFactory:createAuthenticationMiddleware() Validating authentications.
2025-05-15T05:41:51.014Z 6218b80f-cd67-4063-af0a-8bbec97ff118 info: QueueSharedKeyAuthenticator:validate() Start validation against account shared key authentication.
2025-05-15T05:41:51.014Z 6218b80f-cd67-4063-af0a-8bbec97ff118 info: QueueSharedKeyAuthenticator:validate() [STRING TO SIGN]:"GET\n\n\n\n\n\n\n\n\n\n\n\nx-ms-client-request-id:5d7bff14-356f-4c95-8576-d5d1bda73b2b\nx-ms-date:Thu, 15 May 2025 05:41:51 GMT\nx-ms-return-client-request-id:true\nx-ms-version:2025-01-05\n/devstoreaccount1/devstoreaccount1/task-queue-medium\ncomp:metadata"
2025-05-15T05:41:51.014Z 6218b80f-cd67-4063-af0a-8bbec97ff118 info: QueueSharedKeyAuthenticator:validate() Calculated authentication header based on key1: devstoreaccount1:MYpmN4h+g8p2JwKyZotZSJz9zXT+q/BX7ooHTEMNyxo=
2025-05-15T05:41:51.014Z 6218b80f-cd67-4063-af0a-8bbec97ff118 info: QueueSharedKeyAuthenticator:validate() Signature 1 matched.
2025-05-15T05:41:51.014Z 6218b80f-cd67-4063-af0a-8bbec97ff118 verbose: DeserializerMiddleware: Start deserializing...
2025-05-15T05:41:51.015Z 6218b80f-cd67-4063-af0a-8bbec97ff118 info: HandlerMiddleware: DeserializedParameters={"options":{"requestId":"5d7bff14-356f-4c95-8576-d5d1bda73b2b"},"comp":"metadata","version":"2025-01-05"}
2025-05-15T05:41:51.015Z 6218b80f-cd67-4063-af0a-8bbec97ff118 error: ErrorMiddleware: Received a MiddlewareError, fill error information to HTTP response
2025-05-15T05:41:51.015Z 6218b80f-cd67-4063-af0a-8bbec97ff118 error: ErrorMiddleware: ErrorName=StorageError ErrorMessage=The specified queue does not exist.  ErrorHTTPStatusCode=404 ErrorHTTPStatusMessage=The specified queue does not exist. ErrorHTTPHeaders={"x-ms-error-code":"QueueNotFound","x-ms-request-id":"6218b80f-cd67-4063-af0a-8bbec97ff118","x-ms-version":"2025-05-05"} ErrorHTTPBody="<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:6218b80f-cd67-4063-af0a-8bbec97ff118\nTime:2025-05-15T05:41:51.015Z</Message>\n</Error>" ErrorStack="StorageError: The specified queue does not exist.\n    at StorageErrorFactory.getQueueNotFound (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/errors/StorageErrorFactory.js:110:16)\n    at LokiQueueMetadataStore.getQueue (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/persistence/LokiQueueMetadataStore.js:247:49)\n    at QueueHandler.getProperties (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/handlers/QueueHandler.js:81:48)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/middleware/HandlerMiddlewareFactory.js:58:18\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/ExpressMiddlewareFactory.js:77:63\n    at Layer.handle [as handle_request] (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:280:10)"
2025-05-15T05:41:51.015Z 6218b80f-cd67-4063-af0a-8bbec97ff118 error: ErrorMiddleware: Set HTTP code: 404
2025-05-15T05:41:51.015Z 6218b80f-cd67-4063-af0a-8bbec97ff118 error: ErrorMiddleware: Set HTTP status message: The specified queue does not exist.
2025-05-15T05:41:51.015Z 6218b80f-cd67-4063-af0a-8bbec97ff118 error: ErrorMiddleware: Set HTTP Header: x-ms-error-code=QueueNotFound
2025-05-15T05:41:51.015Z 6218b80f-cd67-4063-af0a-8bbec97ff118 error: ErrorMiddleware: Set HTTP Header: x-ms-request-id=6218b80f-cd67-4063-af0a-8bbec97ff118
2025-05-15T05:41:51.015Z 6218b80f-cd67-4063-af0a-8bbec97ff118 error: ErrorMiddleware: Set HTTP Header: x-ms-version=2025-05-05
2025-05-15T05:41:51.015Z 6218b80f-cd67-4063-af0a-8bbec97ff118 error: ErrorMiddleware: Set content type: application/xml
2025-05-15T05:41:51.015Z 6218b80f-cd67-4063-af0a-8bbec97ff118 error: ErrorMiddleware: Set HTTP body: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:6218b80f-cd67-4063-af0a-8bbec97ff118\nTime:2025-05-15T05:41:51.015Z</Message>\n</Error>"
2025-05-15T05:41:51.015Z 6218b80f-cd67-4063-af0a-8bbec97ff118 verbose: Send Queue telemetry: Q_Queue_GetProperties
2025-05-15T05:41:51.016Z 6218b80f-cd67-4063-af0a-8bbec97ff118 info: EndMiddleware: End response. TotalTimeInMS=2 StatusCode=404 StatusMessage=The specified queue does not exist. Headers={"server":"Azurite-Queue/3.34.0","x-ms-error-code":"QueueNotFound","x-ms-request-id":"6218b80f-cd67-4063-af0a-8bbec97ff118","x-ms-version":"2025-05-05","content-type":"application/xml"}
2025-05-15T05:41:51.019Z 61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51 info: QueueStorageContextMiddleware: RequestMethod=GET RequestURL=http://127.0.0.1/devstoreaccount1/task-queue-high?comp=metadata RequestHeaders:{"host":"127.0.0.1:10001","x-ms-version":"2025-01-05","accept":"application/xml","x-ms-client-request-id":"0962627e-d214-43f0-978b-90daf35e0070","x-ms-return-client-request-id":"true","user-agent":"azsdk-net-Storage.Queues/12.21.0 (.NET 8.0.13; Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:53:26 PDT 2025; root:xnu-11417.121.6~2/RELEASE_X86_64)","x-ms-date":"Thu, 15 May 2025 05:41:51 GMT","authorization":"SharedKey devstoreaccount1:ilvZPWqb3oJZj4h0r59gW/IWLB4k/iXISanUHj9RpCA="} ClientIP=127.0.0.1 Protocol=http HTTPVersion=1.1
2025-05-15T05:41:51.019Z 61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51 info: QueueStorageContextMiddleware: Account=devstoreaccount1 Queue=task-queue-high Message=undefined MessageId=undefined
2025-05-15T05:41:51.019Z 61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51 verbose: DispatchMiddleware: Dispatching request...
2025-05-15T05:41:51.019Z 61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51 info: DispatchMiddleware: Operation=Queue_GetProperties
2025-05-15T05:41:51.019Z 61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51 verbose: AuthenticationMiddlewareFactory:createAuthenticationMiddleware() Validating authentications.
2025-05-15T05:41:51.019Z 61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51 info: QueueSharedKeyAuthenticator:validate() Start validation against account shared key authentication.
2025-05-15T05:41:51.019Z 61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51 info: QueueSharedKeyAuthenticator:validate() [STRING TO SIGN]:"GET\n\n\n\n\n\n\n\n\n\n\n\nx-ms-client-request-id:0962627e-d214-43f0-978b-90daf35e0070\nx-ms-date:Thu, 15 May 2025 05:41:51 GMT\nx-ms-return-client-request-id:true\nx-ms-version:2025-01-05\n/devstoreaccount1/devstoreaccount1/task-queue-high\ncomp:metadata"
2025-05-15T05:41:51.019Z 61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51 info: QueueSharedKeyAuthenticator:validate() Calculated authentication header based on key1: devstoreaccount1:ilvZPWqb3oJZj4h0r59gW/IWLB4k/iXISanUHj9RpCA=
2025-05-15T05:41:51.019Z 61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51 info: QueueSharedKeyAuthenticator:validate() Signature 1 matched.
2025-05-15T05:41:51.019Z 61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51 verbose: DeserializerMiddleware: Start deserializing...
2025-05-15T05:41:51.019Z 61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51 info: HandlerMiddleware: DeserializedParameters={"options":{"requestId":"0962627e-d214-43f0-978b-90daf35e0070"},"comp":"metadata","version":"2025-01-05"}
2025-05-15T05:41:51.019Z 61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51 error: ErrorMiddleware: Received a MiddlewareError, fill error information to HTTP response
2025-05-15T05:41:51.019Z 61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51 error: ErrorMiddleware: ErrorName=StorageError ErrorMessage=The specified queue does not exist.  ErrorHTTPStatusCode=404 ErrorHTTPStatusMessage=The specified queue does not exist. ErrorHTTPHeaders={"x-ms-error-code":"QueueNotFound","x-ms-request-id":"61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51","x-ms-version":"2025-05-05"} ErrorHTTPBody="<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51\nTime:2025-05-15T05:41:51.019Z</Message>\n</Error>" ErrorStack="StorageError: The specified queue does not exist.\n    at StorageErrorFactory.getQueueNotFound (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/errors/StorageErrorFactory.js:110:16)\n    at LokiQueueMetadataStore.getQueue (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/persistence/LokiQueueMetadataStore.js:247:49)\n    at QueueHandler.getProperties (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/handlers/QueueHandler.js:81:48)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/middleware/HandlerMiddlewareFactory.js:58:18\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/ExpressMiddlewareFactory.js:77:63\n    at Layer.handle [as handle_request] (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:280:10)"
2025-05-15T05:41:51.020Z 61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51 error: ErrorMiddleware: Set HTTP code: 404
2025-05-15T05:41:51.020Z 61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51 error: ErrorMiddleware: Set HTTP status message: The specified queue does not exist.
2025-05-15T05:41:51.020Z 61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51 error: ErrorMiddleware: Set HTTP Header: x-ms-error-code=QueueNotFound
2025-05-15T05:41:51.020Z 61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51 error: ErrorMiddleware: Set HTTP Header: x-ms-request-id=61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51
2025-05-15T05:41:51.020Z 61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51 error: ErrorMiddleware: Set HTTP Header: x-ms-version=2025-05-05
2025-05-15T05:41:51.020Z 61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51 error: ErrorMiddleware: Set content type: application/xml
2025-05-15T05:41:51.020Z 61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51 error: ErrorMiddleware: Set HTTP body: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51\nTime:2025-05-15T05:41:51.019Z</Message>\n</Error>"
2025-05-15T05:41:51.020Z 61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51 verbose: Send Queue telemetry: Q_Queue_GetProperties
2025-05-15T05:41:51.020Z 61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51 info: EndMiddleware: End response. TotalTimeInMS=2 StatusCode=404 StatusMessage=The specified queue does not exist. Headers={"server":"Azurite-Queue/3.34.0","x-ms-error-code":"QueueNotFound","x-ms-request-id":"61ae1ea4-d12c-4bf4-a8bd-ae08ef9dca51","x-ms-version":"2025-05-05","content-type":"application/xml"}
2025-05-15T05:41:51.918Z 05f42b4c-7e1f-4187-96c2-23d79e9f5b2b info: QueueStorageContextMiddleware: RequestMethod=GET RequestURL=http://127.0.0.1/devstoreaccount1/task-queue-low?comp=metadata RequestHeaders:{"host":"127.0.0.1:10001","x-ms-version":"2025-01-05","accept":"application/xml","x-ms-client-request-id":"8d40bb19-6721-44b2-98b9-66249c97e2b3","x-ms-return-client-request-id":"true","user-agent":"azsdk-net-Storage.Queues/12.21.0 (.NET 8.0.13; Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:53:26 PDT 2025; root:xnu-11417.121.6~2/RELEASE_X86_64)","x-ms-date":"Thu, 15 May 2025 05:41:51 GMT","authorization":"SharedKey devstoreaccount1:a3/ZXPwb1Rxc2coHns7p9DLFWOImCdPa/CErBEIBNQc="} ClientIP=127.0.0.1 Protocol=http HTTPVersion=1.1
2025-05-15T05:41:51.919Z 05f42b4c-7e1f-4187-96c2-23d79e9f5b2b info: QueueStorageContextMiddleware: Account=devstoreaccount1 Queue=task-queue-low Message=undefined MessageId=undefined
2025-05-15T05:41:51.919Z 05f42b4c-7e1f-4187-96c2-23d79e9f5b2b verbose: DispatchMiddleware: Dispatching request...
2025-05-15T05:41:51.919Z 05f42b4c-7e1f-4187-96c2-23d79e9f5b2b info: DispatchMiddleware: Operation=Queue_GetProperties
2025-05-15T05:41:51.919Z 05f42b4c-7e1f-4187-96c2-23d79e9f5b2b verbose: AuthenticationMiddlewareFactory:createAuthenticationMiddleware() Validating authentications.
2025-05-15T05:41:51.919Z 05f42b4c-7e1f-4187-96c2-23d79e9f5b2b info: QueueSharedKeyAuthenticator:validate() Start validation against account shared key authentication.
2025-05-15T05:41:51.919Z 05f42b4c-7e1f-4187-96c2-23d79e9f5b2b info: QueueSharedKeyAuthenticator:validate() [STRING TO SIGN]:"GET\n\n\n\n\n\n\n\n\n\n\n\nx-ms-client-request-id:8d40bb19-6721-44b2-98b9-66249c97e2b3\nx-ms-date:Thu, 15 May 2025 05:41:51 GMT\nx-ms-return-client-request-id:true\nx-ms-version:2025-01-05\n/devstoreaccount1/devstoreaccount1/task-queue-low\ncomp:metadata"
2025-05-15T05:41:51.919Z 05f42b4c-7e1f-4187-96c2-23d79e9f5b2b info: QueueSharedKeyAuthenticator:validate() Calculated authentication header based on key1: devstoreaccount1:a3/ZXPwb1Rxc2coHns7p9DLFWOImCdPa/CErBEIBNQc=
2025-05-15T05:41:51.919Z 05f42b4c-7e1f-4187-96c2-23d79e9f5b2b info: QueueSharedKeyAuthenticator:validate() Signature 1 matched.
2025-05-15T05:41:51.919Z 05f42b4c-7e1f-4187-96c2-23d79e9f5b2b verbose: DeserializerMiddleware: Start deserializing...
2025-05-15T05:41:51.919Z 05f42b4c-7e1f-4187-96c2-23d79e9f5b2b info: HandlerMiddleware: DeserializedParameters={"options":{"requestId":"8d40bb19-6721-44b2-98b9-66249c97e2b3"},"comp":"metadata","version":"2025-01-05"}
2025-05-15T05:41:51.920Z 05f42b4c-7e1f-4187-96c2-23d79e9f5b2b error: ErrorMiddleware: Received a MiddlewareError, fill error information to HTTP response
2025-05-15T05:41:51.920Z 05f42b4c-7e1f-4187-96c2-23d79e9f5b2b error: ErrorMiddleware: ErrorName=StorageError ErrorMessage=The specified queue does not exist.  ErrorHTTPStatusCode=404 ErrorHTTPStatusMessage=The specified queue does not exist. ErrorHTTPHeaders={"x-ms-error-code":"QueueNotFound","x-ms-request-id":"05f42b4c-7e1f-4187-96c2-23d79e9f5b2b","x-ms-version":"2025-05-05"} ErrorHTTPBody="<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:05f42b4c-7e1f-4187-96c2-23d79e9f5b2b\nTime:2025-05-15T05:41:51.919Z</Message>\n</Error>" ErrorStack="StorageError: The specified queue does not exist.\n    at StorageErrorFactory.getQueueNotFound (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/errors/StorageErrorFactory.js:110:16)\n    at LokiQueueMetadataStore.getQueue (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/persistence/LokiQueueMetadataStore.js:247:49)\n    at QueueHandler.getProperties (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/handlers/QueueHandler.js:81:48)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/middleware/HandlerMiddlewareFactory.js:58:18\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/ExpressMiddlewareFactory.js:77:63\n    at Layer.handle [as handle_request] (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:280:10)"
2025-05-15T05:41:51.920Z 05f42b4c-7e1f-4187-96c2-23d79e9f5b2b error: ErrorMiddleware: Set HTTP code: 404
2025-05-15T05:41:51.920Z 05f42b4c-7e1f-4187-96c2-23d79e9f5b2b error: ErrorMiddleware: Set HTTP status message: The specified queue does not exist.
2025-05-15T05:41:51.920Z 05f42b4c-7e1f-4187-96c2-23d79e9f5b2b error: ErrorMiddleware: Set HTTP Header: x-ms-error-code=QueueNotFound
2025-05-15T05:41:51.920Z 05f42b4c-7e1f-4187-96c2-23d79e9f5b2b error: ErrorMiddleware: Set HTTP Header: x-ms-request-id=05f42b4c-7e1f-4187-96c2-23d79e9f5b2b
2025-05-15T05:41:51.920Z 05f42b4c-7e1f-4187-96c2-23d79e9f5b2b error: ErrorMiddleware: Set HTTP Header: x-ms-version=2025-05-05
2025-05-15T05:41:51.920Z 05f42b4c-7e1f-4187-96c2-23d79e9f5b2b error: ErrorMiddleware: Set content type: application/xml
2025-05-15T05:41:51.920Z 05f42b4c-7e1f-4187-96c2-23d79e9f5b2b error: ErrorMiddleware: Set HTTP body: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:05f42b4c-7e1f-4187-96c2-23d79e9f5b2b\nTime:2025-05-15T05:41:51.919Z</Message>\n</Error>"
2025-05-15T05:41:51.920Z 05f42b4c-7e1f-4187-96c2-23d79e9f5b2b verbose: Send Queue telemetry: Q_Queue_GetProperties
2025-05-15T05:41:51.920Z 05f42b4c-7e1f-4187-96c2-23d79e9f5b2b info: EndMiddleware: End response. TotalTimeInMS=2 StatusCode=404 StatusMessage=The specified queue does not exist. Headers={"server":"Azurite-Queue/3.34.0","x-ms-error-code":"QueueNotFound","x-ms-request-id":"05f42b4c-7e1f-4187-96c2-23d79e9f5b2b","x-ms-version":"2025-05-05","content-type":"application/xml"}
2025-05-15T05:41:51.942Z 3aabc370-59c2-4a0e-ad6a-bf39b1265335 info: QueueStorageContextMiddleware: RequestMethod=GET RequestURL=http://127.0.0.1/devstoreaccount1/task-queue-medium?comp=metadata RequestHeaders:{"host":"127.0.0.1:10001","x-ms-version":"2025-01-05","accept":"application/xml","x-ms-client-request-id":"d5bcef72-542a-48c8-96d4-6f0d401710c0","x-ms-return-client-request-id":"true","user-agent":"azsdk-net-Storage.Queues/12.21.0 (.NET 8.0.13; Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:53:26 PDT 2025; root:xnu-11417.121.6~2/RELEASE_X86_64)","x-ms-date":"Thu, 15 May 2025 05:41:51 GMT","authorization":"SharedKey devstoreaccount1:nOGuENKTm0ioDUwEdk7uMeMVYAchEZD3ltrY1SuPa0c="} ClientIP=127.0.0.1 Protocol=http HTTPVersion=1.1
2025-05-15T05:41:51.942Z 3aabc370-59c2-4a0e-ad6a-bf39b1265335 info: QueueStorageContextMiddleware: Account=devstoreaccount1 Queue=task-queue-medium Message=undefined MessageId=undefined
2025-05-15T05:41:51.942Z 3aabc370-59c2-4a0e-ad6a-bf39b1265335 verbose: DispatchMiddleware: Dispatching request...
2025-05-15T05:41:51.942Z 3aabc370-59c2-4a0e-ad6a-bf39b1265335 info: DispatchMiddleware: Operation=Queue_GetProperties
2025-05-15T05:41:51.942Z 3aabc370-59c2-4a0e-ad6a-bf39b1265335 verbose: AuthenticationMiddlewareFactory:createAuthenticationMiddleware() Validating authentications.
2025-05-15T05:41:51.942Z 3aabc370-59c2-4a0e-ad6a-bf39b1265335 info: QueueSharedKeyAuthenticator:validate() Start validation against account shared key authentication.
2025-05-15T05:41:51.942Z 3aabc370-59c2-4a0e-ad6a-bf39b1265335 info: QueueSharedKeyAuthenticator:validate() [STRING TO SIGN]:"GET\n\n\n\n\n\n\n\n\n\n\n\nx-ms-client-request-id:d5bcef72-542a-48c8-96d4-6f0d401710c0\nx-ms-date:Thu, 15 May 2025 05:41:51 GMT\nx-ms-return-client-request-id:true\nx-ms-version:2025-01-05\n/devstoreaccount1/devstoreaccount1/task-queue-medium\ncomp:metadata"
2025-05-15T05:41:51.942Z 3aabc370-59c2-4a0e-ad6a-bf39b1265335 info: QueueSharedKeyAuthenticator:validate() Calculated authentication header based on key1: devstoreaccount1:nOGuENKTm0ioDUwEdk7uMeMVYAchEZD3ltrY1SuPa0c=
2025-05-15T05:41:51.942Z 3aabc370-59c2-4a0e-ad6a-bf39b1265335 info: QueueSharedKeyAuthenticator:validate() Signature 1 matched.
2025-05-15T05:41:51.942Z 3aabc370-59c2-4a0e-ad6a-bf39b1265335 verbose: DeserializerMiddleware: Start deserializing...
2025-05-15T05:41:51.943Z 3aabc370-59c2-4a0e-ad6a-bf39b1265335 info: HandlerMiddleware: DeserializedParameters={"options":{"requestId":"d5bcef72-542a-48c8-96d4-6f0d401710c0"},"comp":"metadata","version":"2025-01-05"}
2025-05-15T05:41:51.943Z 3aabc370-59c2-4a0e-ad6a-bf39b1265335 error: ErrorMiddleware: Received a MiddlewareError, fill error information to HTTP response
2025-05-15T05:41:51.943Z 3aabc370-59c2-4a0e-ad6a-bf39b1265335 error: ErrorMiddleware: ErrorName=StorageError ErrorMessage=The specified queue does not exist.  ErrorHTTPStatusCode=404 ErrorHTTPStatusMessage=The specified queue does not exist. ErrorHTTPHeaders={"x-ms-error-code":"QueueNotFound","x-ms-request-id":"3aabc370-59c2-4a0e-ad6a-bf39b1265335","x-ms-version":"2025-05-05"} ErrorHTTPBody="<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:3aabc370-59c2-4a0e-ad6a-bf39b1265335\nTime:2025-05-15T05:41:51.943Z</Message>\n</Error>" ErrorStack="StorageError: The specified queue does not exist.\n    at StorageErrorFactory.getQueueNotFound (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/errors/StorageErrorFactory.js:110:16)\n    at LokiQueueMetadataStore.getQueue (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/persistence/LokiQueueMetadataStore.js:247:49)\n    at QueueHandler.getProperties (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/handlers/QueueHandler.js:81:48)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/middleware/HandlerMiddlewareFactory.js:58:18\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/ExpressMiddlewareFactory.js:77:63\n    at Layer.handle [as handle_request] (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:280:10)"
2025-05-15T05:41:51.943Z 3aabc370-59c2-4a0e-ad6a-bf39b1265335 error: ErrorMiddleware: Set HTTP code: 404
2025-05-15T05:41:51.943Z 3aabc370-59c2-4a0e-ad6a-bf39b1265335 error: ErrorMiddleware: Set HTTP status message: The specified queue does not exist.
2025-05-15T05:41:51.943Z 3aabc370-59c2-4a0e-ad6a-bf39b1265335 error: ErrorMiddleware: Set HTTP Header: x-ms-error-code=QueueNotFound
2025-05-15T05:41:51.943Z 3aabc370-59c2-4a0e-ad6a-bf39b1265335 error: ErrorMiddleware: Set HTTP Header: x-ms-request-id=3aabc370-59c2-4a0e-ad6a-bf39b1265335
2025-05-15T05:41:51.943Z 3aabc370-59c2-4a0e-ad6a-bf39b1265335 error: ErrorMiddleware: Set HTTP Header: x-ms-version=2025-05-05
2025-05-15T05:41:51.943Z 3aabc370-59c2-4a0e-ad6a-bf39b1265335 error: ErrorMiddleware: Set content type: application/xml
2025-05-15T05:41:51.943Z 3aabc370-59c2-4a0e-ad6a-bf39b1265335 error: ErrorMiddleware: Set HTTP body: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:3aabc370-59c2-4a0e-ad6a-bf39b1265335\nTime:2025-05-15T05:41:51.943Z</Message>\n</Error>"
2025-05-15T05:41:51.943Z 3aabc370-59c2-4a0e-ad6a-bf39b1265335 verbose: Send Queue telemetry: Q_Queue_GetProperties
2025-05-15T05:41:51.943Z 3aabc370-59c2-4a0e-ad6a-bf39b1265335 info: EndMiddleware: End response. TotalTimeInMS=1 StatusCode=404 StatusMessage=The specified queue does not exist. Headers={"server":"Azurite-Queue/3.34.0","x-ms-error-code":"QueueNotFound","x-ms-request-id":"3aabc370-59c2-4a0e-ad6a-bf39b1265335","x-ms-version":"2025-05-05","content-type":"application/xml"}
2025-05-15T05:41:51.945Z 09da77b4-5044-4a0b-994f-c600e6720e25 info: QueueStorageContextMiddleware: RequestMethod=GET RequestURL=http://127.0.0.1/devstoreaccount1/task-queue-high?comp=metadata RequestHeaders:{"host":"127.0.0.1:10001","x-ms-version":"2025-01-05","accept":"application/xml","x-ms-client-request-id":"33e6ef17-ecbb-4691-87b0-38195c3c01b8","x-ms-return-client-request-id":"true","user-agent":"azsdk-net-Storage.Queues/12.21.0 (.NET 8.0.13; Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:53:26 PDT 2025; root:xnu-11417.121.6~2/RELEASE_X86_64)","x-ms-date":"Thu, 15 May 2025 05:41:51 GMT","authorization":"SharedKey devstoreaccount1:Spk1G5haTxnejoNxNjrpjCqAiwVVoTdw8O92IOr+5Tc="} ClientIP=127.0.0.1 Protocol=http HTTPVersion=1.1
2025-05-15T05:41:51.945Z 09da77b4-5044-4a0b-994f-c600e6720e25 info: QueueStorageContextMiddleware: Account=devstoreaccount1 Queue=task-queue-high Message=undefined MessageId=undefined
2025-05-15T05:41:51.945Z 09da77b4-5044-4a0b-994f-c600e6720e25 verbose: DispatchMiddleware: Dispatching request...
2025-05-15T05:41:51.945Z 09da77b4-5044-4a0b-994f-c600e6720e25 info: DispatchMiddleware: Operation=Queue_GetProperties
2025-05-15T05:41:51.945Z 09da77b4-5044-4a0b-994f-c600e6720e25 verbose: AuthenticationMiddlewareFactory:createAuthenticationMiddleware() Validating authentications.
2025-05-15T05:41:51.945Z 09da77b4-5044-4a0b-994f-c600e6720e25 info: QueueSharedKeyAuthenticator:validate() Start validation against account shared key authentication.
2025-05-15T05:41:51.945Z 09da77b4-5044-4a0b-994f-c600e6720e25 info: QueueSharedKeyAuthenticator:validate() [STRING TO SIGN]:"GET\n\n\n\n\n\n\n\n\n\n\n\nx-ms-client-request-id:33e6ef17-ecbb-4691-87b0-38195c3c01b8\nx-ms-date:Thu, 15 May 2025 05:41:51 GMT\nx-ms-return-client-request-id:true\nx-ms-version:2025-01-05\n/devstoreaccount1/devstoreaccount1/task-queue-high\ncomp:metadata"
2025-05-15T05:41:51.945Z 09da77b4-5044-4a0b-994f-c600e6720e25 info: QueueSharedKeyAuthenticator:validate() Calculated authentication header based on key1: devstoreaccount1:Spk1G5haTxnejoNxNjrpjCqAiwVVoTdw8O92IOr+5Tc=
2025-05-15T05:41:51.945Z 09da77b4-5044-4a0b-994f-c600e6720e25 info: QueueSharedKeyAuthenticator:validate() Signature 1 matched.
2025-05-15T05:41:51.946Z 09da77b4-5044-4a0b-994f-c600e6720e25 verbose: DeserializerMiddleware: Start deserializing...
2025-05-15T05:41:51.946Z 09da77b4-5044-4a0b-994f-c600e6720e25 info: HandlerMiddleware: DeserializedParameters={"options":{"requestId":"33e6ef17-ecbb-4691-87b0-38195c3c01b8"},"comp":"metadata","version":"2025-01-05"}
2025-05-15T05:41:51.946Z 09da77b4-5044-4a0b-994f-c600e6720e25 error: ErrorMiddleware: Received a MiddlewareError, fill error information to HTTP response
2025-05-15T05:41:51.946Z 09da77b4-5044-4a0b-994f-c600e6720e25 error: ErrorMiddleware: ErrorName=StorageError ErrorMessage=The specified queue does not exist.  ErrorHTTPStatusCode=404 ErrorHTTPStatusMessage=The specified queue does not exist. ErrorHTTPHeaders={"x-ms-error-code":"QueueNotFound","x-ms-request-id":"09da77b4-5044-4a0b-994f-c600e6720e25","x-ms-version":"2025-05-05"} ErrorHTTPBody="<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:09da77b4-5044-4a0b-994f-c600e6720e25\nTime:2025-05-15T05:41:51.946Z</Message>\n</Error>" ErrorStack="StorageError: The specified queue does not exist.\n    at StorageErrorFactory.getQueueNotFound (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/errors/StorageErrorFactory.js:110:16)\n    at LokiQueueMetadataStore.getQueue (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/persistence/LokiQueueMetadataStore.js:247:49)\n    at QueueHandler.getProperties (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/handlers/QueueHandler.js:81:48)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/middleware/HandlerMiddlewareFactory.js:58:18\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/dist/src/queue/generated/ExpressMiddlewareFactory.js:77:63\n    at Layer.handle [as handle_request] (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/.nvm/versions/node/v22.15.0/lib/node_modules/azurite/node_modules/express/lib/router/index.js:280:10)"
2025-05-15T05:41:51.946Z 09da77b4-5044-4a0b-994f-c600e6720e25 error: ErrorMiddleware: Set HTTP code: 404
2025-05-15T05:41:51.946Z 09da77b4-5044-4a0b-994f-c600e6720e25 error: ErrorMiddleware: Set HTTP status message: The specified queue does not exist.
2025-05-15T05:41:51.946Z 09da77b4-5044-4a0b-994f-c600e6720e25 error: ErrorMiddleware: Set HTTP Header: x-ms-error-code=QueueNotFound
2025-05-15T05:41:51.946Z 09da77b4-5044-4a0b-994f-c600e6720e25 error: ErrorMiddleware: Set HTTP Header: x-ms-request-id=09da77b4-5044-4a0b-994f-c600e6720e25
2025-05-15T05:41:51.946Z 09da77b4-5044-4a0b-994f-c600e6720e25 error: ErrorMiddleware: Set HTTP Header: x-ms-version=2025-05-05
2025-05-15T05:41:51.947Z 09da77b4-5044-4a0b-994f-c600e6720e25 error: ErrorMiddleware: Set content type: application/xml
2025-05-15T05:41:51.947Z 09da77b4-5044-4a0b-994f-c600e6720e25 error: ErrorMiddleware: Set HTTP body: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Error>\n  <Code>QueueNotFound</Code>\n  <Message>The specified queue does not exist.\nRequestId:09da77b4-5044-4a0b-994f-c600e6720e25\nTime:2025-05-15T05:41:51.946Z</Message>\n</Error>"
2025-05-15T05:41:51.947Z 09da77b4-5044-4a0b-994f-c600e6720e25 verbose: Send Queue telemetry: Q_Queue_GetProperties
2025-05-15T05:41:51.947Z 09da77b4-5044-4a0b-994f-c600e6720e25 info: EndMiddleware: End response. TotalTimeInMS=2 StatusCode=404 StatusMessage=The specified queue does not exist. Headers={"server":"Azurite-Queue/3.34.0","x-ms-error-code":"QueueNotFound","x-ms-request-id":"09da77b4-5044-4a0b-994f-c600e6720e25","x-ms-version":"2025-05-05","content-type":"application/xml"}
2025-05-15T05:41:53.217Z f55dee12-8376-4a28-aac3-0f0f3b71c5b8 info: TableStorageContextMiddleware: RequestMethod=GET RequestURL=http://127.0.0.1/devstoreaccount1/Tables?$format=application%2Fjson%3Bodata%3Dminimalmetadata&$filter=TableName%20eq%20%27AzureFunctionsDiagnosticEvents202505%27 RequestHeaders:{"host":"127.0.0.1:10002","x-ms-version":"2020-12-06","dataserviceversion":"3.0","accept":"application/json;odata=minimalmetadata","x-ms-client-request-id":"87d09d8b-886f-4123-81ac-54ae5ec22b74","x-ms-return-client-request-id":"true","user-agent":"azsdk-net-Data.Tables/12.8.3 (.NET 8.0.13; Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:53:26 PDT 2025; root:xnu-11417.121.6~2/RELEASE_X86_64)","x-ms-date":"Thu, 15 May 2025 05:41:53 GMT","authorization":"SharedKeyLite devstoreaccount1:TioYr/TWu3gHzRe43ZyJRQmWkWzgAu3go2ucIFXeefc="} ClientIP=127.0.0.1 Protocol=http HTTPVersion=1.1
2025-05-15T05:41:53.217Z f55dee12-8376-4a28-aac3-0f0f3b71c5b8 debug: tableStorageContextMiddleware: Dispatch pattern string: /Tables
2025-05-15T05:41:53.218Z f55dee12-8376-4a28-aac3-0f0f3b71c5b8 info: tableStorageContextMiddleware: Account=devstoreaccount1 tableName=undefined
2025-05-15T05:41:53.218Z f55dee12-8376-4a28-aac3-0f0f3b71c5b8 verbose: DispatchMiddleware: Dispatching request...
2025-05-15T05:41:53.218Z f55dee12-8376-4a28-aac3-0f0f3b71c5b8 info: DispatchMiddleware: Operation=Table_Query
2025-05-15T05:41:53.218Z f55dee12-8376-4a28-aac3-0f0f3b71c5b8 verbose: AuthenticationMiddlewareFactory:createAuthenticationMiddleware() Validating authentications.
2025-05-15T05:41:53.219Z f55dee12-8376-4a28-aac3-0f0f3b71c5b8 info: TableSharedKeyLiteAuthenticator:validate() Start validation against account shared key authentication.
2025-05-15T05:41:53.219Z f55dee12-8376-4a28-aac3-0f0f3b71c5b8 info: TableSharedKeyLiteAuthenticator:validate() [STRING TO SIGN]:"Thu, 15 May 2025 05:41:53 GMT\n/devstoreaccount1/devstoreaccount1/Tables"
2025-05-15T05:41:53.219Z f55dee12-8376-4a28-aac3-0f0f3b71c5b8 info: TableSharedKeyLiteAuthenticator:validate() Calculated authentication header based on key1: SharedKeyLite devstoreaccount1:TioYr/TWu3gHzRe43ZyJRQmWkWzgAu3go2ucIFXeefc=
2025-05-15T05:41:53.219Z f55dee12-8376-4a28-aac3-0f0f3b71c5b8 info: TableSharedKeyLiteAuthenticator:validate() Signature 1 matched.
2025-05-15T05:41:53.219Z f55dee12-8376-4a28-aac3-0f0f3b71c5b8 verbose: DeserializerMiddleware: Start deserializing...
2025-05-15T05:41:53.220Z f55dee12-8376-4a28-aac3-0f0f3b71c5b8 info: HandlerMiddleware: DeserializedParameters={"options":{"queryOptions":{"format":"application/json;odata=minimalmetadata","filter":"TableName eq 'AzureFunctionsDiagnosticEvents202505'"},"requestId":"87d09d8b-886f-4123-81ac-54ae5ec22b74","dataServiceVersion":"3.0"},"version":"2020-12-06"}
2025-05-15T05:41:53.222Z f55dee12-8376-4a28-aac3-0f0f3b71c5b8 verbose: SerializerMiddleware: Start serializing...
2025-05-15T05:41:53.223Z f55dee12-8376-4a28-aac3-0f0f3b71c5b8 debug: Serializer: Raw response body string is {"odata.metadata":"http://127.0.0.1:10002/devstoreaccount1/$metadata#Tables","value":[]}
2025-05-15T05:41:53.223Z f55dee12-8376-4a28-aac3-0f0f3b71c5b8 info: Serializer: Start returning stream body.
2025-05-15T05:41:53.224Z f55dee12-8376-4a28-aac3-0f0f3b71c5b8 verbose: Send Table telemetry: T_Table_Query
2025-05-15T05:41:53.224Z f55dee12-8376-4a28-aac3-0f0f3b71c5b8 info: EndMiddleware: End response. TotalTimeInMS=7 StatusCode=200 StatusMessage=OK Headers={"server":"Azurite-Table/3.34.0","content-type":"application/json;odata=minimalmetadata","x-ms-client-request-id":"87d09d8b-886f-4123-81ac-54ae5ec22b74","x-ms-request-id":"f55dee12-8376-4a28-aac3-0f0f3b71c5b8","x-ms-version":"2025-05-05","date":"Thu, 15 May 2025 05:41:53 GMT"}
2025-05-15T05:41:53.252Z c360ff0e-be28-4d98-8460-c58b5b66e119 info: TableStorageContextMiddleware: RequestMethod=POST RequestURL=http://127.0.0.1/devstoreaccount1/Tables?$format=application%2Fjson%3Bodata%3Dminimalmetadata RequestHeaders:{"host":"127.0.0.1:10002","x-ms-version":"2020-12-06","dataserviceversion":"3.0","accept":"application/json;odata=minimalmetadata","prefer":"return-no-content","x-ms-client-request-id":"e28daa52-2ae1-4784-82e2-50b7618b9988","x-ms-return-client-request-id":"true","user-agent":"azsdk-net-Data.Tables/12.8.3 (.NET 8.0.13; Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:53:26 PDT 2025; root:xnu-11417.121.6~2/RELEASE_X86_64)","x-ms-date":"Thu, 15 May 2025 05:41:53 GMT","authorization":"SharedKeyLite devstoreaccount1:TioYr/TWu3gHzRe43ZyJRQmWkWzgAu3go2ucIFXeefc=","content-type":"application/json;odata=nometadata","content-length":"52"} ClientIP=127.0.0.1 Protocol=http HTTPVersion=1.1
2025-05-15T05:41:53.252Z c360ff0e-be28-4d98-8460-c58b5b66e119 debug: tableStorageContextMiddleware: Dispatch pattern string: /Tables
2025-05-15T05:41:53.252Z c360ff0e-be28-4d98-8460-c58b5b66e119 info: tableStorageContextMiddleware: Account=devstoreaccount1 tableName=undefined
2025-05-15T05:41:53.252Z c360ff0e-be28-4d98-8460-c58b5b66e119 verbose: DispatchMiddleware: Dispatching request...
2025-05-15T05:41:53.252Z c360ff0e-be28-4d98-8460-c58b5b66e119 info: DispatchMiddleware: Operation=Table_Create
2025-05-15T05:41:53.252Z c360ff0e-be28-4d98-8460-c58b5b66e119 verbose: AuthenticationMiddlewareFactory:createAuthenticationMiddleware() Validating authentications.
2025-05-15T05:41:53.252Z c360ff0e-be28-4d98-8460-c58b5b66e119 info: TableSharedKeyLiteAuthenticator:validate() Start validation against account shared key authentication.
2025-05-15T05:41:53.252Z c360ff0e-be28-4d98-8460-c58b5b66e119 info: TableSharedKeyLiteAuthenticator:validate() [STRING TO SIGN]:"Thu, 15 May 2025 05:41:53 GMT\n/devstoreaccount1/devstoreaccount1/Tables"
2025-05-15T05:41:53.252Z c360ff0e-be28-4d98-8460-c58b5b66e119 info: TableSharedKeyLiteAuthenticator:validate() Calculated authentication header based on key1: SharedKeyLite devstoreaccount1:TioYr/TWu3gHzRe43ZyJRQmWkWzgAu3go2ucIFXeefc=
2025-05-15T05:41:53.253Z c360ff0e-be28-4d98-8460-c58b5b66e119 info: TableSharedKeyLiteAuthenticator:validate() Signature 1 matched.
2025-05-15T05:41:53.253Z c360ff0e-be28-4d98-8460-c58b5b66e119 verbose: DeserializerMiddleware: Start deserializing...
2025-05-15T05:41:53.253Z c360ff0e-be28-4d98-8460-c58b5b66e119 debug: deserialize(): Raw request body string is (removed all empty characters) {"TableName":"AzureFunctionsDiagnosticEvents202505"}
2025-05-15T05:41:53.254Z c360ff0e-be28-4d98-8460-c58b5b66e119 info: HandlerMiddleware: DeserializedParameters={"options":{"queryOptions":{"format":"application/json;odata=minimalmetadata"},"requestId":"e28daa52-2ae1-4784-82e2-50b7618b9988","dataServiceVersion":"3.0","responsePreference":"return-no-content"},"version":"2020-12-06","tableProperties":{"tableName":"AzureFunctionsDiagnosticEvents202505"},"body":"ReadableStream"}
2025-05-15T05:41:53.255Z c360ff0e-be28-4d98-8460-c58b5b66e119 verbose: SerializerMiddleware: Start serializing...
2025-05-15T05:41:53.255Z c360ff0e-be28-4d98-8460-c58b5b66e119 verbose: Send Table telemetry: T_Table_Create
2025-05-15T05:41:53.255Z c360ff0e-be28-4d98-8460-c58b5b66e119 info: EndMiddleware: End response. TotalTimeInMS=3 StatusCode=204 StatusMessage=undefined Headers={"server":"Azurite-Table/3.34.0","content-type":"application/json;odata=minimalmetadata","x-ms-client-request-id":"e28daa52-2ae1-4784-82e2-50b7618b9988","x-ms-request-id":"c360ff0e-be28-4d98-8460-c58b5b66e119","x-ms-version":"2025-05-05","date":"Thu, 15 May 2025 05:41:53 GMT","preference-applied":"return-no-content"}
2025-05-15T05:42:06.756Z 	 verbose: Send stop telemetry
2025-05-15T05:42:06.756Z 	 info: Azurite Blob service is closing...
2025-05-15T05:42:06.756Z 	 info: Azurite Queue service is closing...
2025-05-15T05:42:06.756Z 	 info: Azurite Table service is closing...
2025-05-15T05:42:06.757Z 	 info: BlobGCManager:close() Start closing BlobGCManager. Set status to Closing.
2025-05-15T05:42:06.757Z 	 info: QueueGCManager:close() Start closing QueueGCManager, set status to Closing.
2025-05-15T05:42:06.757Z 	 info: BlobGCManager:start() Mark and sweep loop is closed.
2025-05-15T05:42:06.757Z 	 info: BlobGCManager:close() BlobGCManager successfully closed. Set status to Closed.
2025-05-15T05:42:06.758Z 	 info: QueueGCManager:start() Mark and sweep loop is closed.
2025-05-15T05:42:06.758Z 	 info: QueueGCManager:close() QueueGCManager successfully closed, set status to Closed.
2025-05-15T05:42:06.758Z 	 info: Azurite Table service successfully closed
2025-05-15T05:42:06.758Z 	 info: Azurite Blob service successfully closed
2025-05-15T05:42:06.758Z 	 info: Azurite Queue service successfully closed
