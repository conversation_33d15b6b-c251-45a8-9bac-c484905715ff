import azure.functions as func
import logging
import sys
import os

# Configure logging
logger = logging.getLogger('wrapper_function')
logger.setLevel(logging.INFO)

# Set environment variables for local development
os.environ["IS_LOCAL_DEV"] = "true"
os.environ["USE_LOCAL_STORAGE"] = "true"

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the FastAPI app
try:
    # Import the fixed router app
    from run_fixed_router_updated import app as fastapi_app
    logger.info("Successfully imported fixed router FastAPI app")
except ImportError as e:
    logger.error(f"Error importing fixed router FastAPI app: {str(e)}")
    logger.error(f"Python path: {sys.path}")

    try:
        # Try to import the full app as fallback
        from app import app as fastapi_app
        logger.info("Successfully imported full FastAPI app")
    except ImportError as e2:
        logger.error(f"Error importing full FastAPI app: {str(e2)}")

        # Create a dummy FastAPI app as last resort
        import fastapi
        fastapi_app = fastapi.FastAPI()
        logger.warning("Using dummy FastAPI app as fallback")

# Import the account management FastAPI app
try:
    from account_management_asgi import app as account_app
    logger.info("Successfully imported account management FastAPI app")
except ImportError as e:
    logger.error(f"Error importing account management FastAPI app: {str(e)}")

    # Create a dummy FastAPI app as fallback
    import fastapi
    account_app = fastapi.FastAPI()
    logger.warning("Using dummy account management FastAPI app as fallback")

# Create an ASGI handler for the FastAPI app
async def main(req: func.HttpRequest, context: func.Context) -> func.HttpResponse:
    """
    Azure Functions HTTP trigger function for FastAPI integration

    This function is a wrapper for the FastAPI application.
    It uses the ASGI integration to run the FastAPI app within Azure Functions.

    Args:
        req: HTTP request
        context: Function context (not used directly but required by Azure Functions)

    Returns:
        func.HttpResponse: HTTP response
    """
    try:
        # Log detailed request information
        logger.info(f"Processing request for {req.url}")
        logger.info(f"Request method: {req.method}")
        logger.info(f"Request route: {req.route_params.get('route', 'No route param')}")
        logger.info(f"Request headers: {dict(req.headers)}")
        logger.info(f"Request body: {req.get_body().decode('utf-8') if req.get_body() else 'No body'}")
        logger.info(f"Request params: {dict(req.params)}")
        logger.info(f"Request route_params: {dict(req.route_params)}")

        # Get the route from the route parameters
        route = req.route_params.get('route', '')

        # Log the route for debugging
        logger.info(f"Processing route: {route}")

        # Handle account management endpoints using the account management ASGI app
        # Support both /api/accounts and /api/api/accounts patterns
        if (route.startswith('api/accounts') or route.startswith('api/roles') or route.startswith('api/users') or
            route.startswith('api/api/accounts') or route.startswith('api/api/roles') or route.startswith('api/api/users') or
            route.startswith('api/test') or route.startswith('api/api/test')):
            logger.info(f"Routing {req.method} /{route} to account management ASGI app")

            # Use our custom ASGI middleware for better path handling
            from shared.asgi_middleware import AsgiMiddleware
            account_asgi_handler = AsgiMiddleware(account_app)

            # Process the request through the account ASGI handler
            try:
                response = await account_asgi_handler.handle(req)

                # Log response information
                logger.info(f"Account management response status code: {response.status_code}")

                return response
            except Exception as e:
                logger.error(f"Error processing account management request: {str(e)}")
                import traceback
                logger.error(f"Account management traceback: {traceback.format_exc()}")

                # Continue with the main ASGI handler if account management fails

        # Special case for integrations endpoint - no longer needed as we've updated the routes
        # if route == 'integrations' and req.method == 'GET':
        #     logger.info(f"Special handling for integrations endpoint")
        #     # Modify the route to match the integration router's expected format
        #     route = 'api/integration/integrations'
        #     logger.info(f"Modified route for integrations endpoint: {route}")

        # Handle API routes that were previously handled by proxy functions
        # This allows the WrapperFunction to handle more routes directly
        # Support both old and new API path formats for backward compatibility
        if (route.startswith('api/') or
            route.startswith('auth/') or
            route.startswith('user/') or
            route.startswith('tasks/') or
            route.startswith('task-status') or
            route.startswith('orgs') or
            route.startswith('connect-org') or
            route.startswith('disconnect-org') or
            route.startswith('rescan-org') or
            route.startswith('integration/')):

            logger.info(f"Routing API request {req.method} /{route} to main API router")

            # Modify the route to use the consolidated API router
            # Strip the 'api/' prefix if present to match the expected format
            if route.startswith('api/'):
                modified_route = route[4:]  # Remove 'api/' prefix
                logger.info(f"Modified route for API router: {modified_route}")

                # Update the route parameter
                req.route_params['path'] = modified_route

                # Forward to the main API router function in function_app.py
                try:
                    from function_app import api_router
                    return api_router(req)
                except Exception as e:
                    logger.error(f"Error forwarding to API router: {str(e)}")
                    import traceback
                    logger.error(f"API router forwarding traceback: {traceback.format_exc()}")

                    # Continue with the ASGI handler if API router fails

        # Log all available routes in FastAPI app
        routes = [{"path": route.path, "name": route.name, "methods": route.methods} for route in fastapi_app.routes]
        logger.info(f"Available FastAPI routes: {routes}")

        # Use our custom ASGI middleware for better path handling
        from shared.asgi_middleware import AsgiMiddleware
        asgi_handler = AsgiMiddleware(fastapi_app)

        # Process the request through the ASGI handler
        response = await asgi_handler.handle(req)

        # Log response information
        logger.info(f"Response status code: {response.status_code}")
        logger.info(f"Response headers: {dict(response.headers)}")

        return response
    except Exception as e:
        logger.error(f"Error processing request: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

        # Return error response
        return func.HttpResponse(
            body=f"Error processing request: {str(e)}",
            status_code=500
        )
