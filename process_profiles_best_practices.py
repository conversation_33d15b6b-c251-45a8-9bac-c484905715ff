import os
import xml.etree.ElementTree as ET
import json
import argparse
from shared.data_access import BlobStorageRepository

def normalize_value(val):
    if val is None:
        return None
    val = val.strip().lower()
    if val in ['❌ false', 'x false', 'false', 'no', 'disabled', '0']:
        return 'false'
    if val in ['✅ true', 'tickmark/true', 'true', 'yes', 'enabled', '1']:
        return 'true'
    return val

def normalize_key(key):
    if key is None:
        return None
    return key.strip().lower().replace(' ', '')

def load_best_practices_xml(xml_path):
    tree = ET.parse(xml_path)
    root = tree.getroot()
    best_practices = []
    for practice in root.findall('Practice'):
        bp = {child.tag: child.text for child in practice}
        best_practices.append(bp)
    return best_practices

def parse_profile_xml_from_blob(blob_bytes):
    try:
        root = ET.fromstring(blob_bytes)
        ns = {'sf': root.tag.split('}')[0].strip('{')}  # Extract namespace
        user_permissions = {}
        for perm in root.findall('.//sf:userPermissions', ns):
            name = perm.find('sf:name', ns)
            enabled = perm.find('sf:enabled', ns)
            if name is not None and name.text:
                user_permissions[name.text] = enabled.text if enabled is not None else None
        print("\nDEBUG: Extracted user_permissions:", user_permissions)  # Debug print
        return user_permissions
    except Exception as e:
        print(f"Error parsing profile XML: {e}")
        return {}

def compare_profile_with_best_practices(profile_name, user_permissions, best_practices):
    results = []
    # Normalize user_permissions keys for matching
    normalized_permissions = {normalize_key(k): v for k, v in user_permissions.items()}
    for bp in best_practices:
        setting = bp.get('SalesforceSetting')
        normalized_setting = normalize_key(setting)
        if normalized_setting not in normalized_permissions:
            continue  # Skip if the profile does not have this setting
        expected = normalize_value(bp.get('StandardValue'))
        actual = normalize_value(normalized_permissions.get(normalized_setting))
        match = (expected == actual)
        print(f"DEBUG: Checking setting '{setting}' (normalized: '{normalized_setting}') -> actual value: {actual}")  # Debug print
        result = {
            'SalesforceSetting': setting,
            'Description': bp.get('Description'),
            'OWASP': bp.get('OWASP'),
            'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity'),
            'UserType': bp.get('UserType'),
            'StandardValue': expected,
            'ProfileValue': actual,
            'Match': match
        }
        results.append(result)
    return results

def process_profiles_in_blob(blob_repo, blob_prefix, best_practices, summary_output_path=None):
    # List all profile XMLs under the given prefix
    profile_folder = f"{blob_prefix}/profiles/"
    blobs = blob_repo.list_blobs(name_starts_with=profile_folder)
    profile_results = {}
    summary_lines = []
    for blob_name in blobs:
        if not blob_name.endswith('.profile'):
            continue
        profile_name = os.path.basename(blob_name).replace('.profile', '')
        # Download blob content
        try:
            blob_client = blob_repo.container_client.get_blob_client(blob_name)
            blob_bytes = blob_client.download_blob().readall()
            user_permissions = parse_profile_xml_from_blob(blob_bytes)
            if not user_permissions:
                continue  # Skip profiles with no extracted permissions
            results = compare_profile_with_best_practices(profile_name, user_permissions, best_practices)
            mismatch_count = sum(1 for r in results if not r.get('Match'))
            summary_lines.append(f"{profile_name}, {len(results)}, {mismatch_count}")
            profile_results[profile_name] = results
        except Exception as e:
            print(f"Error processing {blob_name}: {e}")
    if summary_output_path:
        with open(summary_output_path, 'w', encoding='utf-8') as f:
            f.write("Profile,TotalChecks,Mismatches\n")
            for line in summary_lines:
                f.write(line + "\n")
    return profile_results

def main():
    parser = argparse.ArgumentParser(description='Compare Salesforce profile metadata with best practices.')
    parser.add_argument('--xml', required=True, help='Path to Profiles_PermissionSetRisks-BestPractice.xml')
    parser.add_argument('--blob-prefix', required=True, help='Blob prefix for the integration metadata (e.g. org_name/integration_id/metadata_folder)')
    parser.add_argument('--container', default='salesforce-metadata', help='Blob container name (default: salesforce-metadata)')
    parser.add_argument('--output', help='Optional output file to write JSON results')
    parser.add_argument('--summary-output', help='Optional output file to write profile summary (CSV)')
    args = parser.parse_args()

    best_practices = load_best_practices_xml(args.xml)
    blob_repo = BlobStorageRepository(container_name=args.container)
    profile_results = process_profiles_in_blob(blob_repo, args.blob_prefix, best_practices, summary_output_path=args.summary_output)

    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(profile_results, f, indent=2, ensure_ascii=False)
        print(f"Results written to {args.output}")
    else:
        print(json.dumps(profile_results, indent=2))

if __name__ == '__main__':
    main() 