<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="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" />
      </map>
    </option>
  </component>
</project>