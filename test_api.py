"""
Test API

This script tests the account management API endpoints.
"""

import requests
import json
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_api')

# Base URL for API calls
BASE_URL = "http://localhost:7071/api/api/api"

def create_account():
    """Create an account using the API"""
    logger.info("Creating account using API...")
    
    url = f"{BASE_URL}/accounts"
    headers = {"Content-Type": "application/json"}
    data = {"name": "Test Account API"}
    
    try:
        response = requests.post(url, headers=headers, json=data)
        logger.info(f"Response status code: {response.status_code}")
        logger.info(f"Response body: {response.text}")
        
        if response.status_code == 201:
            return response.json()
        else:
            logger.error(f"Failed to create account: {response.text}")
            return None
    except Exception as e:
        logger.error(f"Error creating account: {str(e)}")
        return None

def get_accounts():
    """Get accounts using the API"""
    logger.info("Getting accounts using API...")
    
    url = f"{BASE_URL}/accounts"
    
    try:
        response = requests.get(url)
        logger.info(f"Response status code: {response.status_code}")
        logger.info(f"Response body: {response.text}")
        
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"Failed to get accounts: {response.text}")
            return None
    except Exception as e:
        logger.error(f"Error getting accounts: {str(e)}")
        return None

def main():
    """Main function"""
    logger.info("Starting API test...")
    
    # Create account
    account_response = create_account()
    logger.info(f"Create account response: {account_response}")
    
    # Get accounts
    accounts_response = get_accounts()
    logger.info(f"Get accounts response: {accounts_response}")
    
    logger.info("API test completed")

if __name__ == "__main__":
    main()
