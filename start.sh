#!/bin/bash

# Start script for AtomSec Function App
# This script ensures all required queues exist before starting the application

echo "Starting AtomSec Function App..."

# Ensure Azurite is running
echo "Checking if Azurite is running..."
if ! nc -z localhost 10000 && ! nc -z localhost 10001 && ! nc -z localhost 10002; then
    echo "Azurite is not running. Starting Azurite..."
    azurite &
    # Wait for <PERSON><PERSON><PERSON> to start
    sleep 5
    echo "Azurite started."
else
    echo "Azurite is already running."
fi

# Create required queues
echo "Creating required queues..."
python create_queues.py

# Start the function app
echo "Starting function app..."
func start
